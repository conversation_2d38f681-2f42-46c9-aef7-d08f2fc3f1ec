@extends('layouts.app')
@section('title', 'Pesquisa')

@section('content')
<section class="contentServico txtcontent">
	
	<div class="container-fluid">
		
		<div class="row">
			
			<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12">

				<h1>Pesquisa</h1>

				

        <div class="itemSearch">
          @if ($results->isEmpty())
          <p>Refine a pesquisa. Caso não encontre, envie sua dúvida para <a href="mailto:<EMAIL>?subject=Pergunte ao Tabelião"><EMAIL></a>, com o assunto Pergunte ao Tabelião.</p>
          @else
            <p>Veja abaixo o resultado da sua pesquisa:</p>
              @foreach ($results as $result)
              <summary>
                <a href="{{ $result->final_link }}" class="@if($result->restricted_access) private-page @endif">{{ $result->title }}</a> <label for="">{{ $result->label }}</label>
              </summary>
            @endforeach
          @endif
        </div>

        {{ $results->appends(request()->input())->links('components.pagination') }}
			</div>
		</div>

	</div>

</section>
@endsection