<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('submenus', function (Blueprint $table) {
          $table->id();
          $table->string('title')->unique();
          $table->string('slug')->unique();
          $table->unsignedBigInteger('menu_id');
          $table->foreign('menu_id')->references('id')->on('menus');
          $table->unsignedTinyInteger('index');
          $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('submenus');
    }
};
