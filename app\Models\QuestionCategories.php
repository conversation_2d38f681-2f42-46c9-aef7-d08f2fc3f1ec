<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuestionCategories extends Model
{
    use HasFactory;

  // public function questions($title = '')
  // {
  //   $title = '%' . $title . '%';
  //   $t = $this->hasMany(Question::class, 'category_id')
  //               ->where('questions.question', 'like', $title)
  //               ->orderBy('index')
  //               ->get();

  //               return $t;
  // }

  public function questions($title = '')
  {
    $title = '%' . $title . '%';
    return $this->hasMany(Question::class, 'category_id')
                ->where('questions.question', 'like', $title)
                ->orderBy('index');

  }
}
