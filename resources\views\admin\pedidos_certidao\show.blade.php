@extends('layouts.admin')
@section('title', 'Pedidos Certidão')

@section('content')
    <h4 class="fw-bold py-3 mb-4">
        <span class="text-muted fw-light">Pedidos Certidão / </span> Informações
    </h4>

    <div class="card">
        <div class="card-header">
            <h5>Dados do Ato</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="mb-3 col-sm-6">
                    <label class="form-label">Número do Pedido</label>
                    <input type="text" class="form-control" value="{{ $pedido->pedido_id }}" readonly>
                </div>
                <div class="mb-3 col-sm-6">
                    <label class="form-label">Tipo do Ato</label>
                    <input type="text" class="form-control" value="{{ $form->Tipo ?? '' }}" readonly>
                </div>
            </div>

            @if (stripos($form->Tipo ?? '', 'Busca') === 0)
                <div class="row">
                    <div class="mb-3 col-sm-12">
                        <label class="form-label">Dados da busca</label>
                        <textarea class="form-control" readonly>{{ $form->Dados_para_busca ?? '' }}</textarea>
                    </div>
                </div>
            @endif

            @if (stripos($form->Tipo ?? '', 'Documento arquivado') === 0)
                <div class="row">
                    <div class="mb-3 col-sm-12">
                        <label class="form-label">Dados do documento</label>
                        <textarea class="form-control" readonly>{{ $form->Dados_do_documento ?? '' }}</textarea>
                    </div>
                </div>
            @endif
            <div class="row">
                <div class="mb-3 col-sm-6">
                    <label class="form-label">Finalidade da solicitação</label>
                    <textarea class="form-control" readonly>{{ $form->Finalidade_da_solicitação ?? '' }}</textarea>
                </div>
                <div class="mb-3 col-sm-6">
                    <label class="form-label">Partes</label>
                    <textarea class="form-control" readonly>{{ $form->Partes ?? '' }}</textarea>
                </div>
            </div>
            <div class="row">
                <div class="mb-3 col-sm-6">
                    <label class="form-label">Livro</label>
                    <input type="text" class="form-control" value="{{ $form->Livro ?? '' }}" readonly>
                </div>
                <div class="mb-3 col-sm-6">
                    <label class="form-label">Folha</label>
                    <input type="text" class="form-control" value="{{ $form->Folha ?? '' }}" readonly>
                </div>
            </div>
        </div>
    </div>
    @if (isset($form->arquivos))
        <div class="card mt-4">
            <div class="card-header">
                <h5>Arquivos</h5>
            </div>
            <div class="card-body">
                @foreach ($form->arquivos as $arquivo)
                    @php
                        $arquivoExiste = true;

                        $path = storage_path('app/' . $arquivo);

                        // Verifica se o arquivo existe
                        if (!file_exists($path)) {
                            $arquivoExiste = false;
                        }

                        // Extrai o nome do arquivo sem diretório
                        $nomeCompleto = basename($arquivo);

                        // Remove a extensão
                        $nomeLimpo = pathinfo($nomeCompleto, PATHINFO_FILENAME);

                        $filename = $arquivo;

                        $parts = explode('_', $filename); // Separa os blocos por "_"
                        $secondItem = $parts[2]; // Pega o segundo item

                        $extension = pathinfo($filename, PATHINFO_EXTENSION); // Captura a extensão

                        $result = $secondItem . '.' . $extension;
                    @endphp

                    <div class="row">
                        @if ($arquivoExiste)
                            <div class="mb-3 col-sm-12">
                                <a href="{{ route('download_anexo_pedido', ['filename' => $nomeLimpo, 'ext' => $extension]) }}"
                                    target="_blank" class="btn btn-primary">{{ $result }}</a>
                            </div>
                        @else
                            <div class="mb-3 col-sm-12">
                                <span class="text-danger">{{ $result }} - Arquivo já foi excluído</span>
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>
        </div>
    @endif
    <div class="card mt-4">
        <div class="card-header">
            <h5>Dados do Solicitante</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="mb-3 col-sm-6">
                    <label class="form-label">Nome / Razão Social</label>
                    <input type="text" class="form-control" value="{{ $form->nome }}" readonly>
                </div>
                <div class="mb-3 col-sm-6">
                    <label class="form-label">CPF / CNPJ</label>
                    <input type="text" class="form-control" value="{{ $form->CPF ?? $form->CNPJ }}" readonly>
                </div>
            </div>

            <div class="row">
                <div class="mb-3 col-sm-6">
                    <label class="form-label">Telefone</label>
                    <input type="text" class="form-control" value="{{ $form->telefone ?? '' }}" readonly>
                </div>
                <div class="mb-3 col-sm-6">
                    <label class="form-label">E-mail</label>
                    <input type="text" class="form-control" value="{{ $form->email ?? '' }}" readonly>
                </div>
            </div>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <h5>Dados de Entrega</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="mb-3 col-sm-4">
                    <label class="form-label">Tipo da Certidão</label>
                    <input type="text" class="form-control" value="{{ $form->Tipo_da_Certidão }}" readonly>
                </div>
                <div class="mb-3 col-sm-4">
                    <label class="form-label">Forma de Entrega</label>
                    <input type="text" class="form-control" value="{{ $form->Forma_de_Entrega ?? '' }}" readonly>
                </div>
                <div class="mb-3 col-sm-4">
                    <label class="form-label">Tipo de Envio</label>
                    <input type="text" class="form-control" value="{{ $form->tipo_envio_select ?? '' }}" readonly>
                </div>
            </div>

            <div class="row">
                <div class="mb-3 col-sm-2">
                    <label class="form-label">CEP</label>
                    <input type="text" class="form-control" value="{{ $form->CEP ?? '' }}" readonly>
                </div>
                <div class="mb-3 col-sm-4">
                    <label class="form-label">Logradouro</label>
                    <input type="text" class="form-control" value="{{ $form->Logradouro ?? '' }}" readonly>
                </div>
                <div class="mb-3 col-sm-2">
                    <label class="form-label">Número</label>
                    <input type="text" class="form-control" value="{{ $form->Número ?? '' }}" readonly>
                </div>
                <div class="mb-3 col-sm-2">
                    <label class="form-label">Complemento</label>
                    <input type="text" class="form-control" value="{{ $form->Complemento ?? '' }}" readonly>
                </div>
                <div class="mb-3 col-sm-2">
                    <label class="form-label">Bairro</label>
                    <input type="text" class="form-control" value="{{ $form->Bairro ?? '' }}" readonly>
                </div>
            </div>
            <div class="row">
                <div class="mb-3 col-sm-2">
                    <label class="form-label">Cidade</label>
                    <input type="text" class="form-control" value="{{ $form->Cidade ?? '' }}" readonly>
                </div>
                <div class="mb-3 col-sm-2">
                    <label class="form-label">UF</label>
                    <input type="text" class="form-control" value="{{ $form->UF ?? '' }}" readonly>
                </div>
            </div>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <h5>Valor do Ato</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="mb-3 col-sm-4">
                    <label class="form-label">Tipo da Certidão</label>
                    <input type="text" id="total_costs" class="form-control" name="total_costs"
                        value="{{ $form->Tipo_da_Certidão }}" readonly>
                </div>
                <div class="mb-3 col-sm-4">
                    <label class="form-label">Forma de Entrega</label>
                    <input type="text" id="total_costs" class="form-control" name="total_costs"
                        value="{{ $form->Forma_de_Entrega }}" readonly>
                </div>
                <div class="mb-3 col-sm-4">
                    <label class="form-label">Tipo de Envio</label>
                    <input type="text" id="total_costs" class="form-control" name="total_costs"
                        value="{{ $form->tipo_envio_select }}" readonly>
                </div>
            </div>
            <div class="row">
                <div class="mb-3 col-sm-4">
                    <label class="form-label">Valor</label>
                    <input type="text" id="total_costs" class="form-control money" name="total_costs"
                        value="{{ number_format($form->Valor, 2) }}" readonly>
                </div>
            </div>

            <div class="row">
                <div class="mb-3 col-sm-4">
                    <label class="form-label">Opção Digital</label>
                    <input type="text" id="others_costs" class="form-control money"
                        value="{{ number_format($form->Opção_digital, 2) }}" readonly>
                </div>
            </div>

            <div class="row">
                <div class="mb-3 col-sm-4">
                    <label class="form-label">Taxa de Entrega</label>
                    <input type="text" id="others_costs" class="form-control money"
                        value="{{ number_format($form->Taxa_de_entrega, 2) }}" readonly>
                </div>
            </div>

            <div class="row">
                <div class="mb-3 col-sm-4">
                    <label class="form-label">Total</label>
                    <input type="text" id="total" class="form-control money"
                        value="{{ number_format($form->Valor + $form->Taxa_de_entrega + $form->Opção_digital, 2) }}"
                        name="total" readonly>
                </div>
            </div>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <h5>Status - {{ $pedido->descricaoStatus() }}</h5>
        </div>
        <div class="card-body">
            <form method="post" action="{{ route('admin.pedidos_certidao.update', $pedido->id) }}">
                @csrf
                <div class="row">
                    <div class="mb-3 col-sm-1">
                        <button class="btn btn-primary" type="button" onclick="AlteraStatus(1)">Elegível</button>
                    </div>
                    <div class="mb-3 col-sm-2">
                        <button class="btn btn-danger" type="button" onclick="AlteraStatus(0)">Não elegível</button>
                    </div>
                </div>
                <div class="row">
                    <div class="mb-3 col-sm-12">
                        <input type="hidden" value="" name="status" id="status" />
                        <input type="hidden" value="1" name="mensagemPersonalizada" />
                        <div id="editorElegivel" style="display: none;">
                            <label class="form-label">Elegível</label>
                            <textarea class="form-control editor measures" style="visiblity: hidden" name="textElegivel" id="textElegivel"
                                rows="5">{{ $textElegivel }}</textarea>
                        </div>
                        <div id="editorInelegivel" style="display: none;">
                            <label class="form-label">Inelegível</label>
                            <textarea class="form-control editor measures" style="visiblity: hidden" name="textInelegivel" id="textInelegivel"
                                rows="5">{{ $textInelegivel }}</textarea>
                        </div>
                    </div>
                </div>
                <div class="row" id="variaveis" style="display: none">
                    <div class="col-12">
                        Variáveis disponíveis: <br>
                        <b>
                            @{{ chavepix }}, @{{ valorpix }}, @{{ valorboleto }},
                            @{{ boletocodigobarras }}, @{{ protocolo }}, @{{ tipoato }},
                            @{{ finalidade }}, @{{ partes }}, @{{ livro }},
                            @{{ folha }}, @{{ nomerazaosocial }}, @{{ telefone }},
                            @{{ email }}, @{{ tipocertidao }}, @{{ formaentrega }},
                            @{{ tipoenvio }}, @{{ cep }}, @{{ logradouro }},
                            @{{ numero }}, @{{ complemento }}, @{{ bairro }},
                            @{{ cidade }}, @{{ uf }}, @{{ cpfcnpj }}
                        </b>
                    </div>
                </div>
                <div class="row">
                    <div class="mb-3 col-sm-1">
                        <button class="btn btn-primary" id="divConfirmar" style="display: none">Confirmar</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    @if ($pedido->status == 'E')
        <div class="card mt-4" id="dados_boleto">
            <div class="card-header">
                <h5>Boleto</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="mb-3 col-sm-6">
                        <label class="form-label">Código de barras</label>
                        <div class="input-group align-items-center">
                            <input type="text" class="form-control" value="{{ $pedido->barcode }}" id="barcode"
                                readonly>
                            <button class="btn btn-primary" onclick="copyField(this, 'barcode')"
                                data-clipboard-target="#barcode">Copiar</button>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    @endif
    <br><br><br>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.4/clipboard.min.js"></script>
    <script nonce="{{ csp_nonce() }}">
        function AlteraStatus(status) {
            $("#divConfirmar").show();
            $("#variaveis").show();
            $("#status").val(status);
            if (status == 0) {
                $('#editorInelegivel').show();
                $('#editorElegivel').hide();
            } else {
                $('#editorElegivel').show();
                $('#editorInelegivel').hide();
            }
        }

        function initEditor() {
            tinymce.init({
                selector: ".editor",
                theme: "silver",
                content_css: '/css/tinyTextArea-min.css',
                plugins: ['advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                    'anchor', 'searchreplace', 'visualblocks', 'fullscreen',
                    'insertdatetime', 'media', 'table', 'code', 'help', 'wordcount', 'hr'
                ],
                height: 300,
                resize: 'vertical',
                valid_elements: '*[*]',
                extended_valid_elements: 'script[language|type|src]',
                advcode_inline: true,
                tinymce_allow_script_urls: true,
                menubar: false,
                toolbar: 'undo redo | blocks | bold italic backcolor link | ' +
                    'alignleft aligncenter alignright alignjustify | ' +
                    'hr bullist numlist outdent indent | removeformat | code | help',
                language: 'pt_BR',
                setup: function(editor) {
                    editor.on('keyup change undo', function() {
                        editor.targetElm.innerHTML = editor.getContent();
                        editor.targetElm.dispatchEvent(new Event("change"));
                    });
                }
            });
        }
        $(document).ready(function() {
            initEditor();
        });

        function removeMask(val) {
            val = val.replaceAll('.', '');
            return val.replaceAll(',', '.');
        }

        function copyField(button, fieldID) {
            $(button).html('Copiado!');
        }
    </script>
@endsection
