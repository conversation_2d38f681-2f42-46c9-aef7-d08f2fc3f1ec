<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use App\Mail\SendTokenToClient;
use App\Models\Client;

class RegisterController extends Controller
{
    public function register()
    {
        return view('auth.register');
    }

    public function registerClient(Request $request)
    {
        $request->validate([
            'g-recaptcha-response' => 'required',
            'email' => 'required|email|unique:clients',
            'password' => 'required|confirmed|min:6',
        ], [
            'g-recaptcha-response.required' => 'Assinale a caixa "não sou um robô"!',
            'password.confirmed' => 'As senhas não coincidem.',
            'email.unique' => 'O email informado já está em uso.
'
        ]);

        $client = new Client();
        $client->email = $request->email;
        $client->password = Hash::make($request->password);
        $client->is_active = 0;
        $client->save();

        Mail::to($client->email)->send(new SendTokenToClient($client->email, null, 'registro'));


        return redirect()->route('login')->with('success', 'Conta criada com sucesso. Verifique seu e-mail para ativar a conta.');
    }

}
