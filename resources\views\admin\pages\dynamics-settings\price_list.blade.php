@extends('layouts.admin')
@section('title', 'Tabela de Preços')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="fw-bold py-3 m-0">
    <span class="text-muted fw-light">Páginas / Dinamicas / </span> Tabela de Preços
  </h4>
</div>

<form method="POST" action="{{ route('pages.dynamics.update', ['price_list']) }}" x-data="settingsComponent(@js($dynamic_page_settings))">
  @csrf

  <div class="card">
    <h5 class="card-header">Configurações</h5>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Titulo</label>
          <input type="text" class="form-control @error('title') is-invalid @enderror" placeholder="Digite o título" name="title" value="Tabela de Preços" required readonly>
        </div>
      </div>
      <div class="row">
        <div class="mb-3 col-sm-5">
          <label class="form-label">Descrição breve</label>
          <textarea type="text" class="form-control @error('description') is-invalid @enderror" placeholder="Digite a descrição" name="description" rows="4">{{ $page->description ?? '' }}</textarea>
        </div>
        <div class="mb-3 col-sm-2">
          <div class="form-check mt-3">
            <input class="form-check-input" type="checkbox" @checked($page->restricted_access ?? false) id="restricted_access" name="restricted_access">
            <label class="form-check-label" for="restricted_access">
              Adicionar Cadeado
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="card mt-4">
    <h5 class="card-header">Conteúdo</h5>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Conteúdo antes do calculo</label>
          <div class="editor" x-on:change="editorContentChanges($event, 'before_calc')" x-html="settings.before_calc"></div>
        </div>
      </div>
      <br>
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Conteúdo após o calculo</label>
          <div class="editor" x-on:change="editorContentChanges($event, 'after_calc')" x-html="settings.after_calc"></div>
        </div>
      </div>
    </div>
  </div>

  <div class="card mt-4">
    <h5 class="card-header">Configurações</h5>
    <div class="card-body">
      <div class="mb-3 col-sm-12">
        <label class="form-label">Escritura onerosa (%)</label>
        <input type="text" class="form-control" placeholder="Informe a porcentagem" name="title" required x-model="settings.onerousDeed">
      </div>

      <div class="mb-3 col-sm-12">
        <label class="form-label">Escritura não onerosa (%)</label>
        <input type="text" class="form-control" placeholder="Informe a porcentagem" name="title" required x-model="settings.nonOnerousDeed">
      </div>

      <div class="mb-3 col-sm-12">
        <label class="form-label">Máximo isenção ITBI</label>
        <input type="text" class="form-control" placeholder="Informe o valor" name="title" required x-model="settings.maximumITBIExemption">
      </div>

      <div class="mb-3 col-sm-12">
        <label class="form-label">Máximo isenção ITCMD</label>
        <input type="text" class="form-control" placeholder="Informe o valor" name="title" required x-model="settings.maximumITCMDExemption">
      </div>
    </div>
  </div>

  <div class="card mt-4">
    <h5 class="card-header">Tabela Emolumentos Para Registro de Imóveis</h5>
    <div class="table-responsive text-nowrap" style="max-height: 500px;">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>De</th>
            <th>Até</th>
            <th>Emolumentos</th>
            <th>Ações</th>
          </tr>
        </thead>
        <tbody class="table-border-bottom-0 ui-sortable" id="sortable">
          <template x-for="(track, index) in settings.propertyTracks">
            <tr>
              <td><input type="text" class="form-control" placeholder="Informe o valor" name="title" required x-model="track.of"></td>
              <td><input type="text" class="form-control" placeholder="Informe o valor" name="title" required x-model="track.until" ></td>
              <td><input type="text" class="form-control" placeholder="Informe o valor" name="title" required x-model="track.emoluments"></td>
              <td>
                <button type="button" class="btn text-danger" href="javascript:void(0);" x-on:click="removePropertyTrack(index)"><i class="bx bx-trash me-1"></i></button>
              </td>
            </tr>
          </template>
        </tbody>
      </table>
    </div>

    <div class="row p-4">
      <div>
        <button class="btn btn-primary mt-3" type="button" x-on:click="addPropertyTrack">Adicionar Faixa</button>
      </div>
    </div>
  </div>


  <div class="card mt-4">
    <h5 class="card-header">Tabela Emolumentos e Tributos</h5>
    <div class="table-responsive text-nowrap" style="max-height: 500px;">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>De</th>
            <th>Até</th>
            <th>Emolumentos</th>
            <th>Tributos</th>
            <th>Ações</th>
          </tr>
        </thead>
        <tbody class="table-border-bottom-0 ui-sortable" id="sortable">
          <template x-for="(track, index) in settings.emolumentsAndTaxesTracks">
            <tr>
              <td><input type="text" class="form-control" placeholder="Informe o valor" name="title" required x-model="track.of"></td>
              <td><input type="text" class="form-control" placeholder="Informe o valor" name="title" required x-model="track.until" ></td>
              <td><input type="text" class="form-control" placeholder="Informe o valor" name="title" required x-model="track.emoluments"></td>
              <td><input type="text" class="form-control" placeholder="Informe o valor" name="title" required x-model="track.taxes"></td>
              <td>
                <button type="button" class="btn text-danger" href="javascript:void(0);" x-on:click="removeEatTrack(index)"><i class="bx bx-trash me-1"></i></button>
              </td>
            </tr>
          </template>
        </tbody>
      </table>
    </div>

    <div class="row p-4">
      <div>
        <button class="btn btn-primary mt-3" type="button" x-on:click="addEatTrack">Adicionar Faixa</button>
      </div>
    </div>

    <input type="hidden" name="settings" x-model="JSON.stringify(settings)">
  </div>


  <button class="btn btn-primary mt-5">Salvar</button>
</form>


<br><br><br><br><br><br><br>

<script src="{{asset("tinymce\\tinymce.min.js")}}"></script>

<script nonce="{{ csp_nonce() }}">
    function initEditor() {
        tinymce.init({
            selector: ".editor",
            theme: "silver",
            content_css: '/css/tinyTextArea-min.css',
            plugins: ['advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                    'anchor', 'searchreplace', 'visualblocks', 'fullscreen',
                    'insertdatetime', 'media', 'table', 'code', 'help', 'wordcount', 'hr'
            ],
            height: 300,
            resize: 'vertical',
            valid_elements : '*[*]',
            extended_valid_elements: 'script[language|type|src]',
            advcode_inline: true,
            tinymce_allow_script_urls: true,
            menubar: false,
            toolbar: 'undo redo | blocks | bold italic backcolor link | ' +
                    'alignleft aligncenter alignright alignjustify | ' +
                    'hr bullist numlist outdent indent | removeformat | code | help',
            language: 'pt_BR',
            setup: function(editor) {
                editor.on('keyup change undo', function () {
                editor.targetElm.innerHTML = editor.getContent();
                editor.targetElm.dispatchEvent(new Event("change"));
                });
            }
        });
    }
    // function initEditor() {
    //   $('.editor').tinymce({
    //     height: 300,
    //     resize: 'vertical',
    //     menubar: false,
    //     tinymce_allow_script_urls: true,
    //     valid_elements : '*[*]',
    //     extended_valid_elements: 'script[language|type|src]',
    //     plugins: [
    //       'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
    //       'anchor', 'searchreplace', 'visualblocks', 'fullscreen',
    //       'insertdatetime', 'media', 'table', 'code', 'help', 'wordcount', 'hr'
    //     ],
    //     toolbar: 'undo redo | blocks | bold italic backcolor link | ' +
    //       'alignleft aligncenter alignright alignjustify | ' +
    //       'hr bullist numlist outdent indent | removeformat | code | help',
    //     setup: function(editor) {
    //       editor.on('keyup change undo', function () {
    //         editor.targetElm.innerHTML = editor.getContent();
    //         editor.targetElm.dispatchEvent(new Event("change"));
    //       });
    //     }
    //   });
    // }

    $( document ).ready(function() {
      initEditor();
    });

   function settingsComponent(initialSettings = []) {
    return {
      settings: {
        before_calc: '',
        after_calc: '',
        onerousDeed: '',
        nonOnerousDeed: '',
        maximumITBIExemption: '',
        maximumITCMDExemption: '',
        propertyTracks: [
          { of: '', until: '', emoluments: '' },
        ],
        emolumentsAndTaxesTracks: [
          { of: '', until: '', emoluments: '', taxes: '' },
        ]
      },
      init() {
        if (initialSettings.length > 0) {
          this.settings = JSON.parse(initialSettings);
        }
      },
      editorContentChanges(e, prop) {
        this.settings[prop] = e.srcElement.innerHTML;
        console.log(this.settings[prop]);
      },
      addPropertyTrack() {
        this.settings.propertyTracks.push({
          of: '',
          until: '',
          emoluments: ''
        });
      },
      removePropertyTrack(index) {
        if (!confirm('Quer mesmo excluir essa faixa?'))
          return;

        this.settings.propertyTracks.splice(index, 1);
      },

      addEatTrack() {
        this.settings.emolumentsAndTaxesTracks.push({
          of: '',
          until: '',
          emoluments: '',
          taxes: ''
        });
      },
      removeEatTrack(index) {
        if (!confirm('Quer mesmo excluir essa faixa?'))
          return;

        this.settings.emolumentsAndTaxesTracks.splice(index, 1);
      }
    }
  }
</script>
@endsection
