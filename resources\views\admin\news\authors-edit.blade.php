@extends('layouts.admin')
@section('title', 'Autores')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="fw-bold py-3 m-0">
    <span class="text-muted fw-light">Novidades / </span> Autores
  </h4>
</div>

<form class="card my-4" method="POST" action="{{ route('news.authors.update', [$author->id]) }}">
  @csrf
  <h5 class="card-header">Editar Autor</h5>
  <div class="card-body">
    <div class="d-flex align-items-center">
      <div class="flex-grow-1 me-3">
        <label class="form-label">Nome</label>
        <input type="text" class="form-control @error('name') is-invalid @enderror" placeholder="Digite o nome do autor" name="name" value="{{ $author->name }}">
      </div>
      <div class="flex-grow-1 me-3">
        <label class="form-label">Ocupação</label>
        <input type="text" class="form-control @error('occupation') is-invalid @enderror" placeholder="Digite a ocupação do autor" name="occupation" value="{{ $author->occupation }}">
      </div>
    </div>
    <button class="btn btn-primary mt-3">Salvar</button>
  </div>
</form>

  <script nonce="{{ csp_nonce() }}">
    $( "#sortable" ).sortable();

    $( ".accordion-button" ).on('click', function(){
      $(".accordion-collapse").collapse('hide');
    });
  </script>
@endsection