## 26º Tabelionato de Notas de São Paulo
Site 26 Notas de São Paulo em Laravel

## URLs
Site: https://26notas.com.br   
Admin: https://26notas.com.br/admin  

## Informações Técnicas
O servidor está na AWS e tem a gestão pelo TI de 26 Notas e contém 2 instâncias:    
1 instância para os arquivos do site.  
1 Instância para o Banco de dados.  

## Outros sites / Aplicações
A pasta _html_ é a pasta _public_ do Laravel.  

### BLOG
O BLOG se encontra dentro de _html_. É em WordPress, que se encontra neste repositório - https://github.com/lampejos/26notas-site-blog.  

### Vivanapaz _(desabilitado em 2024)_
Esta na raiz do servidor com domínio próprio, que se encontra neste repositório - https://github.com/lampejos/26notas-viva-na-paz.  

## Atualizações
**17/02/2025**  
Adição de classes para sub-menu personalizado em cores e negrito via painel admin.  

**06/10/2023**  
Backup full para repositório