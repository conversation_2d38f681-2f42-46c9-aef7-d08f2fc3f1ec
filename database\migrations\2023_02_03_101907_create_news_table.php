<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('news', function (Blueprint $table) {
          $table->id();
          $table->string('title');
          $table->string('slug')->unique();
          $table->unsignedBigInteger('category_id');
          $table->foreign('category_id')->references('id')->on('news_categories');
          $table->unsignedBigInteger('author_id');
          $table->foreign('author_id')->references('id')->on('news_authors');
          $table->longText('photo')->nullable();
          $table->text('content');
          $table->boolean('published')->default(0);
          $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('news');
    }
};
