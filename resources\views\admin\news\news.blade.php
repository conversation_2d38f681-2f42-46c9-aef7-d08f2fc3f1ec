@extends('layouts.admin')
@section('title', 'Novidades')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="fw-bold py-3 m-0">
    Novidades
  </h4>
  <a href="{{ route('news.news.create') }}" class="btn btn-primary mt-3">Nova</a>
</div>

 <div class="card">
  <h5 class="card-header">Novidades</h5>
  <div class="table-responsive text-nowrap">
    <table class="table table-hover">
      <thead>
        <tr>
          <th>Título</th>
          <th>Categoria</th>
          <th>Autor</th>
          <th>Publicada</th>
          <th>Publicada Em</th>
          <th>Ações</th>
        </tr>
      </thead>
      <div class="px-3 mb-3">
        <div class="d-flex align-items-center">
          <input type="text" class="form-control ms-3" id="title" placeholder="Digite o título" value="{{ request()->titulo }}" style="max-width: 500px">
          <select type="text" class="form-select ms-3" id="category" style="max-width: 200px;">
            <option value="">Todas</option>
            @foreach ($categories as $category)
              <option value="{{$category->id}}" @selected(request()->categoria == $category->id)>{{ $category->name }}</option>
            @endforeach
          </select>
          <button class="btn btn-primary ms-3" onclick="filter()">Filtrar</button>
        </div>
      </div>
    </div>
      <tbody class="table-border-bottom-0" id="sortable">
        @foreach ($news as $theNews)
          <tr>
            <td class="adminNewsTitleColumn"><strong>{{ $theNews->title }}</strong></td>
            <td>{{ $theNews->category->name }}</td>
            <td>{{ $theNews->author->name ?? '' }}</td>
            <td>@if($theNews->published) sim @else não @endif</td>
            <td>{{ $theNews->created_at->format('d/m/Y') }}</td>
            <td>
              <div class="dropdown">
                <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown" aria-expanded="false"><i class="bx bx-dots-vertical-rounded"></i></button>
                <div class="dropdown-menu">
                  <a class="dropdown-item" href="{{ route('news.news.edit', [$theNews->id]) }}"><i class="bx bx-edit-alt me-1"></i> Edit</a>
                  <form method="POST" action="{{ route('news.news.destroy', [$theNews->id]) }}">
                    @csrf
                    @method('delete')
                    <button class="dropdown-item" href="javascript:void(0);" onclick="return confirm('Tem certeza disso? Essa ação deletará a novidade')"><i class="bx bx-trash me-1"></i> Delete</button>
                  </form>
                </div>
              </div>
            </td>
          </tr>
        @endforeach
      </tbody>
    </table>
  </div>
  <div class="card-footer">
      {{ $news->appends(request()->except('page'))->links() }}
  </div>
</div>

<script nonce="{{ csp_nonce() }}">
  function filter() {
    var title = $('#title').val();
    var category = $('#category').val();

    var url = new URL(window.location.href);
    url.searchParams.set('titulo', title);
    url.searchParams.set('categoria', category);
    window.location = url.href;
  }
</script>
@endsection