@extends('layouts.admin')
@section('title', 'Men<PERSON>')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="fw-bold py-3 m-0">
    <span class="text-muted fw-light">Menus / </span> Cabe<PERSON>lho
  </h4>
  <a href="{{ route('menus.header.create') }}" type="button" class="btn btn-primary">Novo</a>
</div>

 <div class="card">
  <h5 class="card-header">Menus</h5>
  <div class="table-responsive text-nowrap">
    <table class="table table-hover">
      <thead>
        <tr>
          <th>Título</th>
          <th>Ações</th>
        </tr>
      </thead>
      <tbody class="table-border-bottom-0" id="sortable">
        @foreach ($menus as $menu)
          <tr data-id="{{ $menu->id }}">
            <td><i class="fab fa-angular fa-lg text-danger me-3"></i><i class='bx bxs-sort-alt me-3'></i><strong>{{ $menu->title }}</strong></td>
            <td>
              <div class="dropdown">
                <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown" aria-expanded="false"><i class="bx bx-dots-vertical-rounded"></i></button>
                <div class="dropdown-menu">
                  <a class="dropdown-item" href="{{ route('menus.header.show', [$menu->id]) }}"><i class="bx bx-edit-alt me-1"></i> Edit</a>
                  <form method="POST" action="{{ route('menus.header.destroy', [$menu->id]) }}">
                    @csrf
                    @method('delete')
                    <button class="dropdown-item" href="javascript:void(0);" onclick="return confirm('Tem certeza disso? Essa ação deletará o menu e seus submenus e links')"><i class="bx bx-trash me-1"></i> Delete</button>
                  </form>
                </div>
              </div>
            </td>
          </tr>
        @endforeach
      </tbody>
    </table>
  </div>
</div>

  <script nonce="{{ csp_nonce() }}">
    $( "#sortable" ).sortable({
      update: function( event, ui ) {
        var ids = [];

        var items = $('#sortable').children('tr');
        for (let i = 0; i < items.length; i++) {
          ids.push(items.eq(i).data('id'));
        }        

        $.get("{{ route('menus.update.order') }}", {
          ids: ids
        }, function() {
          //$(button).html('Enviado!');
        });
      }
    });

    $( ".accordion-button" ).on('click', function(){
      $(".accordion-collapse").collapse('hide');
    });
  </script>
@endsection