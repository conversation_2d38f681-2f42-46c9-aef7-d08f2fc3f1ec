<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Question;
use App\Models\QuestionCategories;

class QuestionController extends Controller
{
  public function categories()
  {
    $categories = QuestionCategories::orderBy('index')->get();
    return view('admin.questions.categories', [
      'categories' => $categories
    ]);
  }

  public function categoriesCreate()
  {
    return view('admin.questions.categories-create');
  }

  public function categoriesStore(Request $r)
  {
    $validator = Validator::make($r->all(), [
      'category' => 'required'
    ]);

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }

    $category = new QuestionCategories;
    $category->category = $r->category;
    $category->save();

    return redirect()->route('questions.categories')->with('success', 'Categoria criada com sucesso!');
  }

  public function categoriesEdit($categoryId)
  { 
    $category = QuestionCategories::find($categoryId);
    return view('admin.questions.categories-edit', [
      'category' => $category
    ]);
  }

  public function categoriesUpdate(Request $r, $categoryId)
  {
    $validator = Validator::make($r->all(), [
      'category' => 'required'
    ]);

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }

    $category = QuestionCategories::find($categoryId);
    $category->category = $r->category;
    $category->save();

    return redirect()->route('questions.categories')->with('success', 'Categoria editada com sucesso!');
  }
  
  public function categoriesDestroy($categoryId)
  { 
    QuestionCategories::destroy($categoryId);
    return redirect()->route('questions.categories')->with('success', 'Categoria excluída com sucesso!');
  }

  public function updateCategoriesOrder(Request $r)
  {
    $orderIds = implode(',', $r->ids);
    $categories = QuestionCategories::whereIn('id', $r->ids)->orderByRaw("FIELD(id, $orderIds)")->get();

    foreach ($categories as $newIndex => $category) {
      $category->index = $newIndex;
      $category->save();
    }
  }

  public function questions(Request $r)
  {
    $category = '';
    $questions = [];

    if (!empty($r->titulo)) {
      $category = $r->category;
      $questions = Question::where([
        ['question', 'like', '%'. $r->titulo .'%']
      ])->get();
    } else if (!empty($r->category)) {
      $category = $r->category;
      $questions = Question::where('category_id', $category)->get();
    }


    $categories = QuestionCategories::all();
    
    return view('admin.questions.index', [
      'questions' => $questions,
      'categories' => $categories,
      'filteredCategory' => $category
    ]);
  }

  public function questionsCreate()
  {
    $categories = QuestionCategories::all();
    return view('admin.questions.create', [
      'categories' => $categories
    ]);
  }

  public function questionsStore(Request $r)
  {
    $validator = Validator::make($r->all(), [
      'question' => 'required',
      'answer' => 'required',
      'category_id' => 'required',
    ]);

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }

    $question = new Question;
    $question->question = $r->question;
    $question->answer = $r->answer;
    $question->category_id = $r->category_id;
    $question->save();

    return redirect()->route('questions.q.index')->with('success', 'Pergunta criada com sucesso!');
  }

  public function questionsEdit($questionId)
  {
    $categories = QuestionCategories::all();
    $question = Question::find($questionId);
    return view('admin.questions.edit', [
      'categories' => $categories,
      'question' => $question
    ]);
  }

  public function questionsUpdate(Request $r, $questionId)
  {
    $validator = Validator::make($r->all(), [
      'question' => 'required',
      'answer' => 'required',
      'category_id' => 'required',
    ]);

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }

    $question = Question::find($questionId);
    $question->question = $r->question;
    $question->answer = $r->answer;
    $question->category_id = $r->category_id;
    $question->save();

    return redirect()->route('questions.q.index')->with('success', 'Pergunta editada com sucesso!');
  }
  
  public function questionsDestroy($questionId)
  { 
    Question::destroy($questionId);
    return redirect()->route('questions.q.index')->with('success', 'Pergunta excluída com sucesso!');
  }

  public function updateQuestionsOrder(Request $r)
  {
    $orderIds = implode(',', $r->ids);
    $questions = Question::where('category_id', $r->category)->whereIn('id', $r->ids)->orderByRaw("FIELD(id, $orderIds)")->get();

    foreach ($questions as $newIndex => $question) {
      $question->index = $newIndex;
      $question->save();
    }
  }

}
