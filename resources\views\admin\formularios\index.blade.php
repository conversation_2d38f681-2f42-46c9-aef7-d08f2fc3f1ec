@extends('layouts.admin')
@section('title', 'Formulários de Serviços')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="fw-bold py-3 m-0">
    Formulários de Serviços
  </h4>
</div>

 <div class="card">
  <div class="card-header d-flex justify-content-between">
    <h5 class="">Formulários preenchidos no site 26 Notas.</h5>
    @if (Auth::guard('admin')->user()->type == 'admin')
        <div>
        <a href="{{ route('admin.formularios.export', request()) }}">Export XLS</a>
        </div>
    @endif
  </div>
  <div class="card-body">
        <form method="post" action="{{ route('admin.formularios.index') }}">
            @csrf
            <div class="d-flex align-items-center">
                <input type="text" class="form-control" name="busca" id="busca" placeholder="Digite o nome ou CPF/CNPJ" value="{{ request()->busca }}">
                <select type="text" name="clerk" class="form-select ms-3" id="clerk">
                    <option value="">Todos os escreventes</option>
                    @foreach ($clerks as $clerk)
                      <option value="{{ $clerk->id }}" @selected(request()->clerk == $clerk->id)>{{ $clerk->name }}</option>
                    @endforeach
                  </select>
                  <select type="text" name="servico" class="form-select ms-3" id="servico">
                    <option value="">Serviço</option>
                    @foreach ($pages as $page)
                      <option value="{{ $page->id }}" @selected(request()->servico == $page->id)>{{ $page->title }}</option>
                    @endforeach
                  </select>
                  <select type="text" name="bitrix" class="form-select ms-3" id="bitrix">
                    <option value="" @selected(request()->bitrix == "")>Bitrix status</option>
                    <option value="0" @selected(request()->bitrix === "0")>Erro no envio</option>
                    <option value="1" @selected(request()->bitrix === "1")>Enviado</option>
                  </select>
                <button class="btn btn-primary ms-3">Filtrar</button>
            </div>
        </form>
    </div>
  <div class="table-responsive">
    <table class="table table-hover">
      <thead>
        <tr>
          <th>NOME</th>
          <th>CPF/CNPJ</th>
          <th>ESCREVENTE</th>
          <th>DATA</th>
          <th>SERVIÇO</th>
          <th>BITRIX</th>
          <th>AÇÃO</th>
        </tr>
      </thead>
      <tbody class="table-border-bottom-0">
        @foreach ($forms as $form)
          <tr>
            <td>{{ json_decode($form->fields)->nome ?? '' }}</td>
            <td>{{ json_decode($form->fields)->CPF ?? json_decode($form->fields)->CNPJ ?? '' }}</td>
            <td>
            @isset(json_decode($form->fields)->Escrevente)
                @php
                    $escrevente = \App\Models\Collaborator::find(json_decode($form->fields)->Escrevente);
                    if($escrevente)
                        echo $escrevente->name;
                    else {
                        echo "-";
                    }
                @endphp
            @else
                    -
            @endisset
            </td>
            <td>{{ $form->created_at->format('d/m/Y H:i') }}</td>
            <td>{{ $form->page_title }}</td>
            <td>
            @if($form->bitrix_status == "OK")
                <i class="bx bx-check-double" title="Enviado com sucesso"></i>
            @elseif($form->bitrix_status != "")
                <i class="bx bx-error-alt txt_vermelho" title="Erro no envio"></i>
            @else
                -
            @endif
            </td>
            <td>
            <a target="_blank" href="{{route('admin.formularios.details', $form->id)}}"><i class="bx bx-show me-1"></i></a>
            </td>
          </tr>
        @endforeach
      </tbody>
    </table>
</div>
<div class="card-footer">
    {{ $forms->appends(request()->except('page'))->links() }}
</div>
</div>
@endsection
