<?php

namespace App\Http\Controllers\Admin;

use App\Bitrix\IntegracaoBitrix;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Page;
use App\Models\Question;
use App\Models\QuestionCategories;
use App\Models\CollaboratorOccupation;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class PageController extends Controller
{

    /**
     * TEMPLATES PAGES FUNCTIONS
     */
    public function templates(Request $r)
    {
        $pages = Page::where('type', 'template')->get();

        return view('admin.pages.templates', ['pages' => $pages]);
    }
    public function templatesFilter(Request $r)
    {
        $where[] = ['type', '=', 'template'];

        if (!empty($r->template))
            $where[] = ['template', '=', $r->template];

        if (!empty($r->search))
            $where[] = ['title', 'like', '%' . $r->search . '%'];


        $pages = Page::where($where)->get();
        return view('admin.pages.templates-table', ['pages' => $pages]);
    }

    public function templatesCreate()
    {
        $integracaoBitrix = new IntegracaoBitrix;
        $questionCategories = QuestionCategories::all();
        $questionCategories->load('questions');
        $collaboratorOccupations = CollaboratorOccupation::all();
        $tiposFormulariosBitrix = $integracaoBitrix->retornaTiposFormulario();

        return view('admin.pages.templates-create', [
            'questionCategories' => $questionCategories,
            'collaboratorOccupations' => $collaboratorOccupations,
            'tiposFormulariosBitrix' => $tiposFormulariosBitrix
        ]);
    }

    public function templatesFileUpload(Request $r)
    {
        $file = $r->file('file');
        $name = $file->hashName();

        if ($r->path) {
            $path = $file->storeAs($r->path, $name);
        } else {
            $path = $file->storeAs('template', $name);
        }


        return response()->json([
            'name' => $name
        ]);
    }
    public function templatesFileDownload(Request $r)
    {
        $filePath = storage_path('app/template/' . $r->file['file_name']);
        $name = $r->file['name'] . '.' . explode('.', $r->file['file_name'])[1];
        return response()->download($filePath, $name);
    }

    public function templatesStore(Request $r)
    {
        $rules = [
            'title' => 'required|unique:pages',
            'slug' => 'required|unique:pages',
            'description' => 'required',
            'template' => 'required',
            'sessions' => 'required|min:3',
            'form' => 'required_if:template,services|min:3',
        ];

        $validator = Validator::make($r->all(), $rules);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $generalSettings = [
            'banner_sidebar' => boolval($r->banner_sidebar)
        ];

        $form = json_decode($r->form);
        if ($r->template == "services") {
            $form->tipo_bitrix = $r->tipo_bitrix;
        }

        $page = new Page;
        $page->title = $r->title;
        $page->description = $r->description;
        $page->type = 'template';
        $page->slug = $r->slug;
        $page->published = boolval($r->published) ?? 0;
        $page->restricted_access = boolval($r->restricted_access) ?? 0;
        $page->template = $r->template;
        $page->template_settings = $r->sessions;
        $page->template_form_settings = json_encode($form);
        $page->general_settings = json_encode($generalSettings);
        $page->save();

        return redirect()->route('pages.templates')->with('success', 'Página criada com sucesso!')->withInput();
    }

    public function templatesEdit($pageId)
    {
        $page = Page::find($pageId);
        $integracaoBitrix = new IntegracaoBitrix;
        $page->general_settings = json_decode($page->general_settings);
        $questionCategories = QuestionCategories::all();
        $questionCategories->load('questions');
        $collaboratorOccupations = CollaboratorOccupation::all();
        $tiposFormulariosBitrix = $integracaoBitrix->retornaTiposFormulario();

        return view('admin.pages.templates-edit', [
            'page' => $page,
            'questionCategories' => $questionCategories,
            'collaboratorOccupations' => $collaboratorOccupations,
            'tiposFormulariosBitrix' => $tiposFormulariosBitrix
        ]);
    }

    public function templatesUpdate(Request $r, $pageId)
    {
        $rules = [
            'title' => [
                'required',
                Rule::unique('pages')->ignore($pageId),
            ],
            'slug' => [
                'required',
                Rule::unique('pages')->ignore($pageId),
            ],
            'description' => 'required',
            'template' => 'required',
            'sessions' => 'required|min:3',
            'form' => 'required_if:template,services|min:3',
        ];

        $validator = Validator::make($r->all(), $rules);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $form = json_decode($r->form);

        if ($r->template == "services") {
            $form->tipo_bitrix = $r->tipo_bitrix;
        }

        if ($form->model == 'd') {
            if (!empty($r->tiposAtosHidden)) {
                $tiposAtos = explode('|', $r->tiposAtosHidden);
                $form->tiposAtos = [];
                foreach ($tiposAtos as $tipoAto) {
                    if (empty($tipoAto)) continue;
                    $parts = explode('@', $tipoAto);
                    $form->tiposAtos[] = [
                        'name' => $parts[0],
                        'exibir_analise' => isset($parts[1]) && $parts[1] == 1,
                        'upload_obrigatorio' => isset($parts[2]) && $parts[2] == 1
                    ];
                }
            } else {
                $form->tiposAtos = [];
            }

            if (!empty($r->finalidadesHidden)) {
                $finalidades = explode('|', $r->finalidadesHidden);
                $form->finalidades = [];
                foreach ($finalidades as $finalidade) {
                    if (empty($finalidade)) continue;
                    $form->finalidades[] = [
                        'name' => $finalidade
                    ];
                }
            } else {
                $form->finalidades = [];
            }
        }

        $page = Page::find($pageId);
        $page->title = $r->title;
        $page->description = $r->description;
        $page->slug = $r->slug;
        $page->published = boolval($r->published);
        $page->restricted_access = boolval($r->restricted_access);
        $page->template = $r->template;
        $page->template_settings = $r->sessions;
        $page->template_form_settings = json_encode($form);

        $generalSettings = json_decode($page->general_settings, true);
        $generalSettings['banner_sidebar'] = boolval($r->banner_sidebar);
        $page->general_settings = json_encode($generalSettings);

        $page->save();

        return redirect()->back()->with('success', 'Página editada com sucesso!')->withInput();
    }

    public function templatesDestroy($id)
    {
        Page::destroy($id);
        return redirect()->back()->with('success', 'Página excluída com sucesso!');
    }

    /**
     * DYNAMICS PAGES FUNCTIONS
     */
    public function dynamics()
    {
        $pages = [
            'home',
            'price_list',
            'institutional'
        ];

        return view('admin.pages.dynamics', ['pages' => $pages]);
    }

    public function dynamicsEdit($page = 'home')
    {
        $view = 'admin.pages.dynamics-settings.' . $page;
        if (!view()->exists($view)) {
            return redirect()->back();
        }

        $page = Page::where([
            'type' => 'dynamic',
            'dynamic_page' => $page
        ])->first();

        $t = $page->dynamic_page_settings ?? [];

        $pages = Page::all();

        return view($view, [
            'page' => $page,
            'dynamic_page_settings' => $t,
            'pages' => $pages
        ]);
    }

    public function dynamicsUpdate(Request $r, $page)
    {
        switch ($page) {
            case 'home':
                return $this->dynamicsUpdateHome($r);
                break;
            case 'price_list':
                return $this->dynamicsUpdatePriceList($r);
                break;
            case 'institutional':
                return $this->dynamicsUpdateInstitutional($r);
                break;
            default:
                # code...
                break;
        }
    }

    public function dynamicsUpdateHome(Request $r)
    {
        $home = Page::where([
            'type' => 'dynamic',
            'dynamic_page' => 'home'
        ])->first();

        $settings = [];
        if (!$home) {
            $home = new Page;
        } else {
            $settings = json_decode($home->dynamic_page_settings, true);
        }



        if (!empty($r->banner_img)) {
            $settings['banner_img'] = $this->saveImage($r->banner_img);
        }

        $settings['block1'] = $r->block1;
        $settings['block2'] = $r->block2;

        for ($i = 0; $i < 6; $i++) {
            $settings['color1' . $i] = $r->{"color1" . $i};
            $settings['bold1' . $i] = $r->{"bold1" . $i};
            $settings['blank1' . $i] = $r->{"blank1" . $i};

            $settings['color2' . $i] = $r->{"color2" . $i};
            $settings['bold2' . $i] = $r->{"bold2" . $i};
            $settings['blank2' . $i] = $r->{"blank2" . $i};
        }


        $settings['shortcuts'] = $r->shortcuts;

        foreach ($r->image_shortcuts as $key => $shortcut) {
            $settings['image_shortcuts'][$key]['title'] = $r->image_shortcuts[$key]['title'];
            $settings['image_shortcuts'][$key]['link'] = $r->image_shortcuts[$key]['link'];
            $settings['image_shortcuts'][$key]['text_link'] = $r->image_shortcuts[$key]['text_link'];

            if (!empty($shortcut['image'])) {
                $settings['image_shortcuts'][$key]['image'] = $this->saveImage($shortcut['image']);
            }
        }

        $settings['cta']['text'] = $r->cta['text'];
        $settings['cta']['link'] = $r->cta['link'];
        $settings['cta']['text_link'] = $r->cta['text_link'];
        if (!empty($r->cta['image'])) {
            $settings['cta']['image'] = $this->saveImage($r->cta['image']);
        }

        $settings = json_encode($settings);

        $home->title = 'home';
        $home->description = $r->description;
        $home->restricted_access = 0;
        $home->type = 'dynamic';
        $home->slug = '';
        $home->published = 1;
        $home->dynamic_page = 'home';
        $home->dynamic_page_settings = $settings;
        $home->save();

        return redirect()->back()->with('success', 'Página editada com sucesso!');
    }

    public function saveImage($file)
    {
        $hashName = $file->hashName();
        $path = $file->storeAs('public', $hashName);
        $data = [
            'file_name' => $file->getClientOriginalName(),
            'mime_type' => $file->getClientMimeType(),
            'hash_name' => $hashName,
            'path' => $path,
            'size' => $file->getSize()
        ];

        return $data;
    }

    public function dynamicsUpdatePriceList(Request $r)
    {
        $priceList = Page::where([
            'type' => 'dynamic',
            'dynamic_page' => 'price_list'
        ])->first();

        if (!$priceList) {
            $priceList = new Page;
        }
        $priceList->title = 'Tabela de Preços';
        $priceList->description = $r->description;
        $priceList->restricted_access = boolval($r->restricted_access);
        $priceList->type = 'dynamic';
        $priceList->slug = 'custas-notariais';
        $priceList->published = 1;
        $priceList->dynamic_page = 'price_list';
        $priceList->dynamic_page_settings = $r->settings;
        $priceList->save();

        return redirect()->back()->with('success', 'Página editada com sucesso!');
    }

    public function dynamicsUpdateInstitutional(Request $r)
    {
        $institutional = Page::where([
            'type' => 'dynamic',
            'dynamic_page' => 'institutional'
        ])->first();

        $settings = [];
        if (!$institutional) {
            $institutional = new Page;
        } else {
            $settings = json_decode($institutional->dynamic_page_settings, true);
        }

        $files = $r->file('images', []);
        $images = [];
        foreach ($files as $key => $file) {
            $name = $file->hashName();
            $path = $file->storeAs('public', $name);
            $images[$key] = $name;
        }

        $settings = array_merge($settings, $r->settings, $images);

        $institutional->title = 'Institucional';
        $institutional->description = $r->description;
        $institutional->restricted_access = 0;
        $institutional->type = 'dynamic';
        $institutional->slug = 'institucional';
        $institutional->published = 1;
        $institutional->dynamic_page = 'institutional';
        $institutional->dynamic_page_settings = json_encode($settings);
        $institutional->save();

        return redirect()->back()->with('success', 'Página editada com sucesso!');
    }

    /**
     * CODED PAGES FUNCTIONS
     */
    public function coded()
    {
        $pages = Page::where('type', 'coded')->get();
        return view('admin.pages.coded', ['pages' => $pages]);
    }

    public function codedCreate()
    {
        return view('admin.pages.coded-create');
    }

    public function codedStore(Request $r)
    {
        $ep = $r->endpoint;
        while (substr($ep, 0, 1) == '/') {
            $ep = substr($ep, 1);
        }

        $slug = explode("/", $ep);
        $slug = end($slug);

        $page = new Page;
        $page->title = $r->title;
        $page->description = $r->description;
        $page->published = true;
        $page->restricted_access = boolval($r->restricted_access);
        $page->coded_endpoint = $ep;
        $page->slug = $slug;
        $page->type = 'coded';
        $page->save();
        return redirect()->route('pages.coded')->with('success', 'Página criada com sucesso!');
    }

    public function codedEdit($id)
    {
        $page = Page::find($id);
        return view('admin.pages.coded-edit', ['page' => $page]);
    }

    public function codedUpdate(Request $r, $id)
    {
        $ep = $r->endpoint;
        while (substr($ep, 0, 1) == '/') {
            $ep = substr($ep, 1);
        }

        $slug = explode("/", $ep);
        $slug = end($slug);

        $page = Page::find($id);
        $page->title = $r->title;
        $page->description = $r->description;
        $page->restricted_access = boolval($r->restricted_access);
        $page->published = true;
        $page->slug = $slug;
        $page->coded_endpoint = $ep;
        $page->save();
        return redirect()->back()->with('success', 'Página editada com sucesso!');
    }

    public function codedDestroy($id)
    {
        Page::where('id', $id)->delete();
        return redirect()->back()->with('success', 'Página editada com sucesso!');
    }
}
