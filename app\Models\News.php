<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class News extends Model
{
    use HasFactory;

  public function category()
  {
    return $this->belongsTo(NewsCategories::class);
  }

  public function author()
  {
    return $this->belongsTo(NewsAuthors: <AUTHORS>
  }
  
  public function scopePublished($query)
  {
    return $query->where('published', true);
  }
}
