<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Collaborator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Services\Bradesco\Comprador;
use App\Services\Bradesco\Endereco;
use App\Services\Bradesco\Pagamento;
use App\Models\Boleto;
use App\Mail\SendBoleto;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Auth;

class BoletoController extends Controller
{
  public function index(Request $r)
  {
    $where = [];

    if (!empty($r->protocolo))
      $where[] = ['protocol', '=', $r->protocolo];

    if (Auth::guard('admin')->user()->can('manage_boleto')) {
      $boletos = Boleto::where($where)->get();
    } else if (Auth::guard('admin')->user()->can('use_boleto')) {
      $where[] = ['clerk_id', '=', Auth::guard('admin')->user()->id];
      $boletos = Boleto::where($where)->get();
    } else {
      return redirect()->back();
    }

    return view('admin.boletos.index', [
      'boletos' => $boletos
    ]);
  }

  public function create()
  {
    $allClerks = Collaborator::where('type', 'clerk')->get();
    $clerks = [];
    foreach ($allClerks as $clerk) {
      $permissions = json_decode($clerk['permissions']);
      if (in_array("use_boleto", $permissions))
        array_push($clerks, $clerk);
    }



    return view('admin.boletos.create', [
      'clerks' => $clerks
    ]);
  }

  public function generate(Request $r)
  {
    $validator = Validator::make($r->all(), [
      'protocol' => 'required',
      'nature' => 'required',
      'clerk' => 'required',
      'name' => 'required',
      'cpf_cnpj' => 'required',
      'cep' => 'required',
      'place_type' => 'required',
      'number' => 'required',
      'city' => 'required',
      'neighborhood' => 'required',
      'uf' => 'required',
      'total' => 'required',
      'neighborhood' => 'required',
    ]);

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }

    $pd = $r->previous_deposit ?? 0;
    $tc = $r->total_costs ?? 0;
    $oc = $r->others_costs ?? 0;

    $pd = $this->unmaskBRL($pd);
    $tc = $this->unmaskBRL($tc);
    $oc = $this->unmaskBRL($oc);

    $total = $pd + $tc + $oc;

    $boletoData = $this->generateBoleto($r, $total);

    $boleto = new Boleto;
    $boleto->protocol = $r->protocol;
    $boleto->nature = $r->nature;
    $boleto->clerk_id = $r->clerk;
    $boleto->name = $r->name;
    $boleto->document = $r->cpf_cnpj;
    $boleto->cep = $r->cep;
    $boleto->place_type = $r->place_type;
    $boleto->number = $r->number;
    $boleto->city = $r->city;
    $boleto->neighborhood = $r->neighborhood;
    $boleto->uf = $r->uf;
    $boleto->complement = $r->complement;
    $boleto->previous_deposit = $pd;
    $boleto->total_costs = $tc;
    $boleto->others_costs = $oc;
    $boleto->barcode = $boletoData['linha'];
    $boleto->access_password = $boletoData['senha_acesso'];
    $boleto->link = $boletoData['url'];
    $boleto->save();

    return redirect()->route('boletos.show', ['id' => $boleto->id]);
  }

  private function unmaskBRL($val)
  {
    $val = str_replace('.', '', $val);
    return (float) str_replace(',', '.', $val);
  }

  public function generateBoleto(Request $request, $subtotal) {
    $endereco = new Endereco();
    $endereco->setCep($request->cep);
    $endereco->setLogradouro($request->place_type);
    $endereco->setNumero($request->number);
    $endereco->setComplemento($request->complement);
    $endereco->setBairro($request->neighborhood);
    $endereco->setCidade($request->city);
    $endereco->setUf($request->uf);

    $comprador = new Comprador($request, $endereco);
    $comprador->setNome($request->name);
    $comprador->setDocumento($request->cpf_cnpj);

    $valorTotal = $subtotal + 2.45;
    $valor = str_replace('.', '', number_format((float)$valorTotal, 2, '.', ''));

    $dadosDePagamento = Pagamento::generateBoletoRequest('', $valor, $comprador, null, false, null, $request->protocol);

    $strong = true;
    $bytes = openssl_random_pseudo_bytes(5, $strong);
    if($bytes === false) {
        abort(500);
    }
    $senha = bin2hex($bytes);

    return [
        'status' => 'success',
        'message' => '',
        'url' => $dadosDePagamento->url,
        'linha' => $dadosDePagamento->linha,
        'linha_formatada' => $dadosDePagamento->linha_formatada,
        'senha_acesso' => $senha
    ];
  }

  public function show($id)
  {
    $boleto = Boleto::find($id);
    return view ('admin.boletos.show', ['boleto' => $boleto]);
  }

  public function sendByMail(Request $r)
  {
    $boleto = Boleto::find($r->id);
    if (Mail::to($r->to)->send(new SendBoleto($boleto))) {
      return response()->json([], 200);
    }
  }
}
