<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('news_banners', function (Blueprint $table) {
          $table->id();
          $table->string('title')->unique();
          $table->string('button_text')->nullable();
          $table->longText('button_link')->nullable();
          $table->longText('photo')->nullable();
          $table->boolean('show')->default(1);
          $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('news_banners');
    }
};
