<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Bitrix\IntegracaoBitrix;
use Illuminate\Http\Request;
use App\Models\FormFilling;
use App\Models\Collaborator;
use App\Models\Page;
use App\Exports\FormulariosExport;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;

class FormulariosController extends Controller
{
  public function index(Request $r)
  {
    $busca = mb_strtolower($r->busca, 'UTF-8');
    $forms = FormFilling::whereRaw('ORIGEM = "Form 26" AND (LOWER(JSON_EXTRACT(fields, "$.nome")) like ? OR LOWER(JSON_EXTRACT(fields, "$.CPF")) like ? OR LOWER(JSON_EXTRACT(fields, "$.CNPJ")) like ?)', ['"%' . $busca . '%"', '"%' . $busca . '%"', '"%' . $busca . '%"']);
    $clerks = Collaborator::where('type', 'clerk')->orderBy('name')->get();
    $pages = Page::where('type', 'template')->where('template', 'services')->orderBy('title')->get();

    if($r->servico != ''){
        $title = Page::find($r->servico)->title;
        $forms = $forms->where('page_title', $title);
    }

    if($r->clerk != ''){
        $forms = $forms->whereRaw('JSON_EXTRACT(fields, "$.Escrevente") = ?', $r->clerk);
    }

    $forms = $forms->orderByDesc('id')->paginate(30);

    return view('admin.formularios.index', ['forms' => $forms, 'clerks' => $clerks, 'pages' => $pages]);
  }

  public function details($id){
    $form = FormFilling::find($id);

    if($form == null)
        exit;

    $fields = json_decode($form->fields);
    $outrosForms = null;

    if(isset($fields->CPF))
        $outrosForms = FormFilling::whereRaw('LOWER(JSON_EXTRACT(fields, "$.CPF")) = ? AND id <> ?', ['"' . $fields->CPF . '"', $id])->get();
    elseif(isset($fields->CNPJ))
        $outrosForms = FormFilling::whereRaw('LOWER(JSON_EXTRACT(fields, "$.CNPJ")) = ? AND id <> ?', ['"' . $fields->CNPJ . '"', $id])->get();

    return view('admin.formularios.show', ['fields' => $fields, 'form' => $form, 'outrosForms' => $outrosForms]);
  }

  public function delete($id){
    $news = FormFilling::find($id);
    if($news != null){
        $news->delete();
    }
    return redirect('admin/formularios')->with('success', 'Formulário excluído com sucesso!');
  }

  function reenviar_form_bitrix($id){
    $integracaoBitrix = new IntegracaoBitrix;
    $integracaoBitrix->enviarFormParaBitrix($id);
    return redirect('admin/formularios/' . $id);
  }

  public function export(Request $r){
    if(\Auth::guard('admin')->user()->type == 'admin'){
        $busca = mb_strtolower($r->busca, 'UTF-8');
        $forms = FormFilling::whereRaw('ORIGEM = "Form 26" AND (LOWER(JSON_EXTRACT(fields, "$.nome")) like ? OR LOWER(JSON_EXTRACT(fields, "$.CPF")) like ? OR LOWER(JSON_EXTRACT(fields, "$.CNPJ")) like ?)', ['"%' . $busca . '%"', '"%' . $busca . '%"', '"%' . $busca . '%"']);
        $clerks = Collaborator::where('type', 'clerk')->orderBy('name')->get();
        $pages = Page::where('type', 'template')->orderBy('title')->get();

        if($r->servico != ''){
            $title = Page::find($r->servico)->title;
            $forms = $forms->where('page_title', $title);
        }

        if($r->clerk != ''){
            $forms = $forms->whereRaw('JSON_EXTRACT(fields, "$.Escrevente") = ?', $r->clerk);
        }

        $forms = $forms->orderByDesc('id');

        $now = now()->format('Y-m-d_H-i:s');

        return Excel::download(new FormulariosExport($forms->get()), "formularios_$now.xls");
    }
  }
}
