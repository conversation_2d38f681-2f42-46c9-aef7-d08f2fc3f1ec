@extends('layouts.admin')
@section('title', 'Dinamicas')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="fw-bold py-3 m-0">
    <span class="text-muted fw-light">Páginas / </span> Dinamicas
  </h4>
</div>

 <div class="card">
  <h5 class="card-header">Dinamicas</h5>
  <div class="table-responsive text-nowrap">
    <table class="table table-hover">
      <thead>
        <tr>
          <th>Página</th>
          <th>Ações</th>
        </tr>
      </thead>
      <tbody class="table-border-bottom-0" id="sortable">
        @foreach ($pages as $page)
          <tr>
            <td><strong>{{ __('messages.' . $page) }}</strong></td>
            <td>
              <div class="dropdown">
                <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown" aria-expanded="false"><i class="bx bx-dots-vertical-rounded"></i></button>
                <div class="dropdown-menu">
                  <a class="dropdown-item" href="{{ route('pages.dynamics.edit', [$page]) }}"><i class="bx bx-edit-alt me-1"></i> Edit</a>
                </div>
              </div>
            </td>
          </tr>
        @endforeach
      </tbody>
    </table>
  </div>
</div>

  <script nonce="{{ csp_nonce() }}">
    $( "#sortable" ).sortable();

    $( ".accordion-button" ).on('click', function(){
      $(".accordion-collapse").collapse('hide');
    });
  </script>
@endsection
