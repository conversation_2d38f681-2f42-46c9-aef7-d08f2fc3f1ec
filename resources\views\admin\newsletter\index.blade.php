@extends('layouts.admin')
@section('title', 'Newsletter')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="fw-bold py-3 m-0">
    Newsletter
  </h4>
</div>

 <div class="card">
  <div class="card-header d-flex justify-content-between">
    <h5 class="">Newsletter</h5>
    @if (Auth::guard('admin')->user()->type == 'admin')
        <div>
        <a href="{{ route('admin.newsletter.export', request()) }}">Export XLS</a>
        </div>
    @endif
  </div>
  <div class="card-body">
        <form method="post" action="{{ route('admin.newsletter.index') }}">
            @csrf
            <div class="d-flex align-items-center">
                <input type="text" class="form-control" name="busca" id="busca" placeholder="Digite o nome ou e-mail" value="{{ request()->busca }}">
                <button class="btn btn-primary ms-3">Filtrar</button>
            </div>
        </form>
    </div>
  <div class="table-responsive">
    <table class="table table-hover">
      <thead>
        <tr>
          <th>NOME</th>
          <th>E-MAIL</th>
          <th>DATA</th>
          <th>AÇÃO</th>
        </tr>
      </thead>
      <tbody class="table-border-bottom-0">
        @foreach ($news as $new)
            <tr>
                <td>{{ $new->name ?? '' }}</td>
                <td>{{ $new->email ?? '' }}</td>
                <td>{{ $new->created_at->format('d/m/Y H:i') }}</td>
                <td>
                <form onsubmit="return confirm('Tem certeza de que deseja excluir esse e-mail da lista?');" action="{{route('admin.newsletter.delete', $new->id)}}?page={{request()->page}}" method="POST">
                    @csrf
                    <input type="hidden" name="page" />
                    <button class="btn"><i class="bx bx-trash"></i></button>
                </form>
                </td>
            </tr>
        @endforeach
      </tbody>
    </table>
</div>
<div class="card-footer">
    {{ $news->appends(request()->except('page'))->links() }}
</div>
</div>
@endsection
