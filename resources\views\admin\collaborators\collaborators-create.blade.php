@extends('layouts.admin')
@section('title', 'Colaboradores')

@section('content')
<h4 class="fw-bold py-3 mb-4">
  <span class="text-muted fw-light">Colaboradores / </span> Novo
</h4>

<form method="POST" action="{{ route('collaborators.collaborators.store') }}">
  @csrf

  <div class="card">
    <div class="card-header">
      <h5>Colaborador</h5>
      <small>Campos marcados com <span class="text-danger">*</span> são obrigatórios</small>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-5">
          <label class="form-label">Nome <span class="text-danger">*</span></label>
          <input type="text" class="form-control @error('name') is-invalid @enderror" placeholder="Insira o nome" name="name" value="{{ old('name') }}" required>
        </div>
        <div class="mb-3 col-sm-4">
          <label class="form-label">E-mail <span class="text-danger">*</span></label>
          <input type="email" class="form-control @error('email') is-invalid @enderror" placeholder="Insira o e-mail" name="email" value="{{ old('email') }}" required>
        </div>
        <div class="mb-3 col-sm-3">
          <label class="form-label">Telefone</label>
          <input type="phone" class="form-control @error('phone') is-invalid @enderror" placeholder="Insira o telefone" name="phone" value="{{ old('phone') }}">
        </div>
      </div>

      <div class="row">
        <div class="col-sm-3">
          <label class="form-label">Ocupações</label>
          <select class="form-select" name="occupations[]" multiple>
            @foreach ($occupations as $occupation)
              <option value="{{ $occupation->id }}" @selected(in_array($occupation->id, old('occupations', [])))>{{ $occupation->name }}</option>
            @endforeach
          </select>
        </div>

        <div class="mb-3 col-sm-3">
          <label class="form-label">Tipo <span class="text-danger">*</span></label>
          <select class="form-select" id="type" name="type" onchange="toggleSysAccess()">
            <option value="">Selecionar...</option>
            @if (Auth::guard('admin')->user()->type == 'admin')
              <option value="admin" @selected(old('type') == 'admin')>Administrador</option>
            @endif
            <option value="clerk" @selected(old('type') == 'clerk')>Escrevente</option>
            <option value="assistant" @selected(old('type') == 'assistant')>Auxiliar</option>
            <option value="notary" @selected(old('type') == 'notary')>Tabelião</option>
            <option value="substitute_notary" @selected(old('type') == 'substitute_notary')>Tabelião Substituto</option>
            <option value="other" @selected(old('type') == 'other')>Outro</option>
          </select>
        </div>

        <div class="mb-3 col-sm-3">
          <div id="password-container" @if(old('sys_access') != 'on') style="display: none;" @endif>
            <label class="form-label">Senha <span class="text-danger">*</span></label>
            <input type="password" class="form-control @error('password') is-invalid @enderror" placeholder="Insira a senha" name="password" id="password" value="{{ old('password') }}">
          </div>
          <br>
          <input type="checkbox" class="form-check-input" name="sys_access" id="sys_access" @checked(old('sys_access') == 'on') onchange="toggleSysAccess()">
          <label class="form-label" for="sys_access">Acesso ao sistema?</label>
        </div>
      </div>
    </div>
  </div>

  <div id="permissions" class="card mt-4" @if(old('sys_access') != 'on') style="display: none;" @endif>
    <div class="card-header">
      <h5>Permissões</h5>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-5">
          <input type="checkbox" class="form-check-input" name="permissions[manage_collaborators]" id="manage_collaborators" @checked(old('manage_collaborators'))>
          <label class="ps-1" for="manage_collaborators">Gerenciar outros colaboradores (exceto admins)</label>
          <br><br>

          <input type="checkbox" class="form-check-input" name="permissions[manage_boleto]" id="manage_boleto" @checked(old('permissions.manage_boleto'))>
          <label class="ps-1" for="manage_boleto">Gerenciar módulo de boletos (operador)</label>
          <br><br>

          <input type="checkbox" class="form-check-input" name="permissions[manager_newsletter]" id="manager_newsletter" @checked(old('permissions.manager_newsletter'))>
          <label class="ps-1" for="manager_newsletter">Gerenciar Newsletter</label>
          <br><br>

          <input type="checkbox" class="form-check-input" name="permissions[manager_formularios]" id="manager_formularios" @checked(old('permissions.manager_formularios'))>
          <label class="ps-1" for="manager_formularios">Gerenciar Formulários</label>
          <br><br>

          <input type="checkbox" class="form-check-input" name="permissions[manager_crm]" id="manager_crm" @checked(old('manager_crm'))>
          <label class="ps-1" for="manager_crm">Gerenciar CRM</label>
          <br><br>

          <input type="checkbox" class="form-check-input" name="permissions[use_boleto]" id="use_boleto" @checked(old('permissions.use_boleto'))>
          <label class="ps-1" for="use_boleto">Utilizar módulo de boletos (escrevente)</label>
          <br><br>

          <input type="checkbox" class="form-check-input" name="permissions[use_documents]" id="use_documents" @checked(old('permissions.use_documents')) onchange="enableSetting(this, 'settings-manage-docs-of')">
          <label class="ps-1" for="use_documents">Utilizar módulo de documentos</label>
          <br><br>

          <input type="checkbox" class="form-check-input" name="permissions[pedidos_certidao]" id="pedidos_certidao" @checked(old('permissions.pedidos_certidao'))>
          <label class="ps-1" for="pedidos_certidao">Pedidos Certidão</label>
        </div>

        <div class="mb-3 col-sm-5">
            Marque a opção do colaborador no <b>Bitrix.</b><br />
            Usuário Administrador recebe leads caso o operador selecionado pelo cliente não esteja cadastrado no Bitrix.
            <br><br>

            <input type="checkbox" class="form-check-input" name="settings[perfil_bitrix][]" value="operador" id="perfil_bitrix_operador" @checked(in_array('operador', old('settings.perfil_bitrix', [])))>
            <label class="ps-1" for="perfil_bitrix_operador">Operador Bitrix</label>
            <br><br>

            <input type="checkbox" class="form-check-input" name="settings[perfil_bitrix][]" value="administrador" id="perfil_bitrix_administrador" @checked(in_array('administrador', old('settings.perfil_bitrix', [])))>
            <label class="ps-1" for="perfil_bitrix_administrador">Administrador / Operador Bitrix</label>
        </div>
      </div>
    </div>
  </div>

  <div id="settings" class="card mt-4" @if(old('sys_access') != 'on') style="display: none;" @endif>
    <div class="card-header">
      <h5>Configurações</h5>
      <small>Campos marcados com <span class="text-danger">*</span> são obrigatórios.</small><br>
      <small>Algumas permissões necessitam de configuração adicional, estas aparecerão aqui.</small>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-sm-3" id="settings-manage-docs-of" @if(old('permissions.use_documents') != 'on') style="display: none;" @endif>
          <label class="form-label">Gerencia documentos do(s) escrevente(s):</label>
          <select class="form-select" name="settings[manage_docs_of][]" multiple>
            @foreach ($clerks as $clerk)
              <option value="{{ $clerk->id }}" @selected(in_array($clerk->id, old('settings.manage_docs_of', [])))>{{ $clerk->name }}</option>
            @endforeach
          </select>
        </div>

        <div class="mb-3 col-sm-5">

        </div>
      </div>
    </div>
  </div>


  <button class="btn btn-primary mt-4">Criar</button>
</form>

<br><br><br>

@endsection

<script nonce="{{ csp_nonce() }}">

  function toggleSysAccess() {
    if ($('#sys_access').is(":checked")) {
      $('#password-container').show();
      $('#settings').show();
    } else {
      $('#password-container').hide();
      $('#settings').hide();
    }

    if ($('#sys_access').is(":checked") && $('#type').val() != 'admin' &&  $('#type').val() != '') {
      $('#permissions').show();
      $('#settings').show();
    } else {
      $('#permissions').hide();
      $('#settings').hide();
    }

    $('#password').val('');
  }

  function enableSetting(checkbox, settings) {
    settings = $('#' + settings);
    checkbox = $(checkbox);

    if (checkbox.is(":checked")) {
      settings.show();
      console.log(settings);
    } else {
      settings.hide();
    }

  }
</script>
