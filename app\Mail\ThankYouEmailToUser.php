<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Mail\Mailables\Address;

class ThankYouEmailToUser extends Mailable
{
    use Queueable, SerializesModels;

    public $subject;
    public $clientFirstName;
    private $template;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($subject, $name, $template)
    {
      $this->subject = $subject;
      $this->clientFirstName = $name;
      $this->template = $template;
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
          from: new Address(env('MAIL_FROM_ADDRESS'), env('MAIL_FROM_NAME')),
          subject: $this->subject,
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(
            view: 'emails.' . $this->template,
        );
    }
}
