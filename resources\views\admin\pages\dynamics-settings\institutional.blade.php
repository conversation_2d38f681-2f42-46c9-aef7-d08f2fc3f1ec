@extends('layouts.admin')
@section('title', 'Institucional')

@section('content')
@php
  if (!empty($dynamic_page_settings))
    $settings = json_decode($dynamic_page_settings);
@endphp
<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="fw-bold py-3 m-0">
    <span class="text-muted fw-light">Páginas / Dinamicas / </span> Institucional
  </h4>
</div>

<form method="POST" action="{{ route('pages.dynamics.update', ['institutional']) }}" enctype="multipart/form-data">
  @csrf

  <div class="card">
    <h5 class="card-header">Configurações</h5>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Titulo</label>
          <input type="text" class="form-control @error('title') is-invalid @enderror" placeholder="Digite o título" name="title" value="Institucional" required readonly>
        </div>
      </div>
      <div class="row">
        <div class="mb-3 col-sm-5">
          <label class="form-label">Descrição breve</label>
          <textarea type="text" class="form-control @error('description') is-invalid @enderror" placeholder="Digite a descrição" name="description" rows="4">{{ $page->description ?? '' }}</textarea>
        </div>
      </div>
    </div>
  </div>

  <div class="card mt-4">
    <h5 class="card-header">Conheça</h5>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Conteúdo</label>
          <div class="editor" id="settings[conheca]">{!! $settings->conheca ?? '' !!}</div>
        </div>
      </div>
    </div>
  </div>

  <div class="card mt-4">
    <h5 class="card-header">O tabelionato</h5>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Imagem</label>
          <input type="file" class="form-control" id="images[tabelionato_img]" name="images[tabelionato_img]"/>
          @isset($settings->tabelionato_img)
            <img src="{{ asset('storage/app/public/' . $settings->tabelionato_img) }}" style="margin-top: 16px; max-width:150px;">
          @endisset
        </div>
      </div>
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Conteúdo</label>
          <div class="editor" id="settings[tabelionato]">{!! $settings->tabelionato ?? '' !!}</div>
        </div>
      </div>
    </div>
  </div>

  <div class="card mt-4">
    <h5 class="card-header">O tabelião</h5>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Imagem</label>
          <input type="file" class="form-control" id="images[tabeliao_img]" name="images[tabeliao_img]"/>
          @isset($settings->tabeliao_img)
            <img src="{{ asset('storage/app/public/' . $settings->tabeliao_img) }}" style="margin-top: 16px; max-width:150px;">
          @endisset
        </div>
      </div>
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Conteúdo</label>
          <div class="editor" id="settings[tabeliao]">{!! $settings->tabeliao ?? '' !!}</div>
        </div>
      </div>
    </div>
  </div>

  <div class="card mt-4">
    <h5 class="card-header">LGPD</h5>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Descrição</label>
          <div class="editor" id="settings[lgpd_desc]">{!! $settings->lgpd_desc ?? '' !!}</div>
        </div>
      </div>
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Conteúdo/tabelas</label>
          <div class="editor" id="settings[lgpd]">{!! $settings->lgpd ?? '' !!}</div>
        </div>
      </div>
    </div>
  </div>

  <div class="card mt-4">
    <h5 class="card-header">Código de ética e disciplina notarial</h5>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Conteúdo</label>
          <div class="editor" id="settings[codigo_etica]">{!! $settings->codigo_etica ?? '' !!}</div>
        </div>
      </div>
    </div>
  </div>

  <div class="card mt-4">
    <h5 class="card-header">Premios</h5>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Título #1</label>
          <input type="text" class="form-control" placeholder="Insira o título" name="settings[premio_title_1]" required value="{{ $settings->premio_title_1 ?? '' }}">
        </div>
      </div>
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Imagem #1</label>
          <input type="file" class="form-control" name="images[premio_img_1]">
          @isset($settings->premio_img_1)
            <img src="{{ asset('storage/app/public/' . $settings->premio_img_1) }}" style="margin-top: 16px; max-width:150px;">
          @endisset
        </div>
      </div>
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Conteúdo #1</label>
          <div class="editor" id="settings[premio_content_1]">{!! $settings->premio_content_1 ?? '' !!}</div>
        </div>
      </div>
      <hr>
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Título #2</label>
          <input type="text" class="form-control" placeholder="Insira o título" name="settings[premio_title_2]" required value="{{ $settings->premio_title_2 ?? '' }}">
        </div>
      </div>
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Imagem #2</label>
          <input type="file" class="form-control" name="images[premio_img_2]">
          @isset($settings->premio_img_2)
          <img src="{{ asset('storage/app/public/' . $settings->premio_img_2) }}" style="margin-top: 16px; max-width:150px;">
          @endisset
        </div>
      </div>
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Conteúdo #2</label>
          <div class="editor" id="settings[premio_content_2]">{!! $settings->premio_content_2 ?? '' !!}</div>
        </div>
      </div>
      <hr>
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Título #3</label>
          <input type="text" class="form-control" placeholder="Insira o título" name="settings[premio_title_3]" required value="{{ $settings->premio_title_3 ?? '' }}">
        </div>
      </div>
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Imagem #3</label>
          <input type="file" class="form-control" name="images[premio_img_3]">
          @isset($settings->premio_img_3)
          <img src="{{ asset('storage/app/public/' . $settings->premio_img_3) }}" style="margin-top: 16px; max-width:150px;">
          @endisset
        </div>
      </div>
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Conteúdo #3</label>
          <div class="editor" id="settings[premio_content_3]">{!! $settings->premio_content_3 ?? '' !!}</div>
        </div>
      </div>
    </div>
  </div>

  <div class="card mt-4">
    <h5 class="card-header">Parcerias</h5>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Conteúdo</label>
          <div class="editor" id="settings[parcerias]">{!! $settings->parcerias ?? '' !!}</div>
        </div>
      </div>
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Imagem</label>
          <input type="file" class="form-control" id="images[parcerias_img]" name="images[parcerias_img]"/>
          @isset($settings->parcerias_img)
          <img src="{{ asset('storage/app/public/' . $settings->parcerias_img) }}" style="margin-top: 16px; max-width:150px;">
          @endisset
        </div>
      </div>
    </div>
  </div>







  <button class="btn btn-primary mt-5">Salvar</button>
</form>

<script src="{{asset("tinymce\\tinymce.min.js")}}"></script>
<br><br><br><br><br><br><br>
<script nonce="{{ csp_nonce() }}">
    function initEditor() {
        tinymce.init({
            selector: ".editor",
            theme: "silver",
            content_css: '/css/tinyTextArea-min.css',
            plugins: ['advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                    'anchor', 'searchreplace', 'visualblocks', 'fullscreen',
                    'insertdatetime', 'media', 'table', 'code', 'help', 'wordcount', 'hr'
            ],
            height: 300,
            resize: 'vertical',
            valid_elements : '*[*]',
            extended_valid_elements: 'script[language|type|src]',
            advcode_inline: true,
            tinymce_allow_script_urls: true,
            menubar: false,
            toolbar: 'undo redo | blocks | bold italic backcolor link | ' +
                    'alignleft aligncenter alignright alignjustify | ' +
                    'hr bullist numlist outdent indent | removeformat | code | help',
            language: 'pt_BR',
            setup: function(editor) {
                editor.on('keyup change undo', function () {
                editor.targetElm.innerHTML = editor.getContent();
                editor.targetElm.dispatchEvent(new Event("change"));
                });
            }
        });
    }
    // function initEditor() {
    //   $('.editor').tinymce({
    //     height: 300,
    //     resize: 'vertical',
    //     menubar: false,
    //     tinymce_allow_script_urls: true,
    //     valid_elements : '*[*]',
    //     extended_valid_elements: 'script[language|type|src]',
    //     plugins: [
    //       'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
    //       'anchor', 'searchreplace', 'visualblocks', 'fullscreen',
    //       'insertdatetime', 'media', 'table', 'code', 'help', 'wordcount', 'hr'
    //     ],
    //     toolbar: 'undo redo | blocks | bold italic backcolor link | ' +
    //       'alignleft aligncenter alignright alignjustify | ' +
    //       'hr bullist numlist outdent indent | removeformat | code | help',
    //     setup: function(editor) {
    //       editor.on('keyup change undo', function () {
    //         editor.targetElm.innerHTML = editor.getContent();
    //         editor.targetElm.dispatchEvent(new Event("change"));
    //       });
    //     }
    //   });
    // }

    $( document ).ready(function() {
      initEditor();
    });

    $('input[type="file"]').on('change', function() {
      $(this).siblings('img').remove();
    });
</script>
@endsection
