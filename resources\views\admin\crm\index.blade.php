@extends('layouts.admin')
@section('title', 'CRM 26 Notas')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="fw-bold py-3 m-0">
    CRM 26 Notas
  </h4>
</div>

 <div class="card">
  <div class="card-header d-flex justify-content-between">
    <h5 class="">Clientes</h5>
    @if (Auth::guard('admin')->user()->type == 'admin')
        <div>
        <a href="{{ route('admin.crm.export', request()) }}">Export XLS</a>
        </div>
    @endif
  </div>
  <div class="card-body">
        <form method="post" action="{{ route('admin.crm.index') }}">
            @csrf
            <div class="d-flex align-items-center">
                <input type="text" class="form-control" name="busca" id="busca" placeholder="Digite o nome, CPF/CNPJ ou e-mail" value="{{ request()->busca }}">
                <select type="text" name="origem" class="form-select ms-2" id="origem">
                    <option value="">Origem</option>
                    <option value="API" @selected(request()->origem === "API")>API</option>
                    <option value="Bitrix" @selected(request()->origem === "Bitrix")>Bitrix</option>
                    <option value="Form 26" @selected(request()->origem === "Form 26")>Form 26</option>
                  </select>
                <input type="date" class="form-control ms-2" name="traslado" id="traslado" placeholder="Traslado" value="{{ request()->traslado }}">
                <select type="text" name="clerk" class="form-select ms-2" id="clerk">
                    <option value="">Todos os escreventes</option>
                    @foreach ($clerks as $clerk)
                      <option value="{{ $clerk->escrevente }}" @selected(request()->clerk == $clerk->escrevente)>{{ $clerk->escrevente }}</option>
                    @endforeach
                  </select>
                  <select type="text" name="bitrix" class="form-select ms-2" id="bitrix">
                    <option value="" @selected(request()->bitrix == "")>Bitrix status</option>
                    <option value="WON" @selected(request()->bitrix === "WON")>Ganho</option>
                    <option value="LOSE" @selected(request()->bitrix === "LOSE")>Perdido</option>
                  </select>
                <button class="btn btn-primary ms-3">Filtrar</button>
            </div>
        </form>
    </div>
  <div class="table-responsive">
    <table class="table table-hover">
      <thead>
        <tr>
          <th>NOME</th>
          <th>CPF DA PARTE</th>
          <th>ORIGEM</th>
          <th>ESCREVENTE</th>
          <th>TRANSLADO</th>
          <th>BITRIX</th>
          <th>AÇÃO</th>
        </tr>
      </thead>
      <tbody class="table-border-bottom-0">
        @foreach ($forms as $form)
          <tr>
            <td>{{ json_decode($form->fields)->nome ?? json_decode($form->fields)->Nome ??  json_decode($form->fields)->Nome_Completo ?? '-' }}</td>
            <td>{{ json_decode($form->fields)->CPF ?? json_decode($form->fields)->CNPJ ?? '-' }}</td>
            <td>{{ $form->origem }}</td>
            <td>
            @isset(json_decode($form->fields)->escrevente_nome)
                {{json_decode($form->fields)->escrevente_nome}}
            @else
                -
            @endisset

            </td>
            <td>
                @isset(json_decode($form->fields)->concluido)
                    {{DateTime::createFromFormat('Y-m-d H:i:s', json_decode($form->fields)->ultimoStatusData)->format('d/m/Y H:i:s')}}
                @else
                    -
                @endisset
            </td>
            <td>
                @if((json_decode($form->fields)->ganho_ou_perda ?? "") == "LOSE")
                    Perdido
                @elseif((json_decode($form->fields)->ganho_ou_perda ?? "") == "WON")
                    Ganho
                @else
                    -
                @endif
            <td>
            <a target="_blank" href="{{route('admin.crm.details', $form->id)}}"><i class="bx bx-show me-1"></i></a>
            </td>
          </tr>
        @endforeach
      </tbody>
    </table>
</div>
<div class="card-footer">
    {{ $forms->appends(request()->except('page'))->links() }}
</div>
</div>
@endsection
