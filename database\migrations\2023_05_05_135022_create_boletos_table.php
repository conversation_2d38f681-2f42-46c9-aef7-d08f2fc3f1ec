<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('boletos', function (Blueprint $table) {
          $table->id();
          $table->string('protocol');
          $table->string('nature');
          $table->unsignedBigInteger('clerk_id');

          $table->string('name');
          $table->string('document');
          $table->string('cep');
          $table->string('place_type');
          $table->string('number');
          $table->string('city');
          $table->string('neighborhood');
          $table->string('uf');
          $table->string('complement')->nullable();
          

          $table->float('previous_deposit', 8, 2)->default(0);
          $table->float('total_costs', 8, 2)->default(0);
          $table->float('others_costs', 8, 2)->default(0);

          $table->text('barcode');
          $table->string('access_password')->unique();
          $table->text('link');

          
          $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
      Schema::dropIfExists('boletos');
    }
};
