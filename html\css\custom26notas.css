/* app.css */

html {
   scroll-behavior: smooth;
 }

 b a,
 strong a {
   font-weight: bold;
 }

/* links com font padrão do conteudo */
.contentServico a,
.content-institucional a {
   font-family: 'Open Sans';
   color: var(--azul);
}

.content-institucional .boxWidgetSidebar a {
   color: var(--cinza);
}

.pagenav {
   display: block;
   text-align: center;
}

/* adicionado ponto final nos checks de lgpd */
.formControl label[for="accordingly_checkbox"]:after {
   content: ".";
}

.formControl.check a {
   font-family: 'Roboto Condensed';
}

/* app.css 1138 */
.formControl.check input {
   appearance: unset;
   width: 15px;
   height: 15px;
   border: 1px solid var(--cinza);
   background-color: transparent;
   border-radius: 100%;
   margin: 0;
   margin-right: 10px;
   margin-top: 5px;
   display: inline-flex;
   padding: 7px;
}

.formControl.file input {
   font-family: 'Open Sans';
   color: #8f8f8f !important;
}



/* navegação por tab */
a:focus-visible {
   outline-offset: 2px;
   outline: max(2px, 0.1em) dashed red;
}


input:focus-visible,
textarea:focus-visible,
button:focus-visible {
   outline: none;
}

/*
form:focus-visible {
   border: 1px solid red;
}


.searchForm:target:before,
#searchFaq:target:before,
.searchForm:focus,
.searchForm:target,
.searchForm:active ,
.searchForm:hover--,
.searchForm:focus-visible{
   border:1px solid red !important;
}
*/

/*
input:focus-visible,
textarea:focus-visible,
button:focus-visible {
   outline-offset: 2px;
   outline: max(2px, 0.1em) dashed red;
 }
 */

 /* Qdo. ativado vLibras o elemento interfere o flex */
 #areaCliente .vw-text {
   display: flex;
 }

 .breadcrumb,
 .breadcrumb a {
   font-family: 'Roboto Condensed';
 }




/* ADICIONAR NO CORE.CSS
* Para que as tabelas que não possuem linha e/ou apenas 1 não quebrem visualmente ao clicar no três pontinhos.
*/
.card .table {
   margin-bottom: 50px;
}



/* TABELAS LGPD */
/* HEADER */
.bloco-tabela {
   margin-bottom: 50px;
}

.bloco-tabela .bloco-tabela-header {
   background: var(--azul);
   padding: 10px 15px;
}

.bloco-tabela .bloco-tabela-header :is(p, h3) {
   color: #fff;
   padding: 0;
   margin: 0 0 5px 0;
}

.bloco-tabela .bloco-tabela-header p{
   font-weight: 200;
}

/* CONTENT BODY */
/* ADICIONAR NA DIV bloco-tabela-content A CLASSE row */
.bloco-tabela-content {
   padding: 2rem;
   background-color: #F1F1F1;
}

section.content-institucional .bloco-tabela-content :is(li,p, th),
.bloco-tabela-content dt,
.bloco-tabela-mobile dl dd {
   font-family: 'Open Sans';
   font-size: 14px;
}

.bloco-tabela-content dl {
   margin-bottom: 20px;
}

.bloco-tabela-content dt {
   font-weight: bold;
}

.bloco-tabela-content ul {
  margin-left: 0;
  padding-left: 21px;
  text-indent: -21px
}

/* FOOTER */
.bloco-tabela-content table,
.bloco-tabela-content table tr td:last-child  {
   text-align: left;
   border-spacing: 0;
}


.bloco-tabela-content table thead tr th {
   vertical-align: bottom;
   border-bottom: 2px solid #ddd;
}

.bloco-tabela-content table th {
   padding: 0 7px 5px 0;
}

.bloco-tabela-content .bloco-tabela-mobile dd {
   margin-bottom: 10px;
}

.bloco-tabela-mobile hr {
   border: 0;
   border-bottom: 1px solid #e3e3e3;
   margin-bottom: 25px;
}

.visible-xs { display: none; }
.hidden-xs { display: block;}

@media screen and (max-width: 767px) {
   .visible-xs { display: block; }
   .hidden-xs { display: none;}
}



/* TABELAS TABELA DE PREÇO / GERAL */
.table {
   border: 0;
   border-spacing: 0;
   font-size: 14px;
   margin-bottom: 20px;
   margin-top: 3rem;
}

table.table p {
   font-size: 14px;
   line-height: 21px;
}

.table .table-heading td {
   border-bottom: 2px solid var(--azul);
}

table.table td {
   padding: 0 8px !important;
   border-bottom: 1px solid #ddd;
}

table.table tr td:last-child {
   text-align: left;
}

table.table tbody tr:nth-child(odd) {background: #f1f1f1}
table.table tbody tr:nth-child(even) {background: #fff}


/******* fim tabelas ************************************************************/


/* home */

.bannerDestaque li a {
   display: unset;
}

.lingueta {
   padding: 0 20px 0 20px; /* Termo muito grande, fora do grid. ajuste */
}

.mensalistas .lingueta {
   padding: 0 20px 0 20px; /* pag mensalistas Imagem topo, texto chamada*/
}

/* mensagem do banner com código HTML */

.escrituraOnline .lingueta p {
   max-width: 630px;
}

.lingueta .txt_verde {
   text-transform: uppercase;
   color: var(--azul);
   font-size: 20px;
   display: block;
}

.lingueta .txt_titulo {
   display: block;
   font-size: 32px;
   margin-bottom: 20px;
}

.lingueta .txt_desc {
   font-size: 20px;
}

/* Box ícones */
.blocoAtalhoDefault .iconeAtalho {
   width: 50px;
   font-size: 45px;
}

section.newsletter p {
   margin: 0;
}

section.newsletter form .line input[type="submit"] { 
   cursor: pointer;
}



section.previewBlog h2,
section.previewBlog .blocoBlog {
   margin-bottom: 50px;
}

.blocoBlog p {
   font-size: 22px;
   line-height: 27px;
   margin: 15px 0;
}

.blocoBlog .imageBlog {
   height: 204px;
}

.blocoBlog .imageBlog img {
   border-radius: 10px;
}

@media screen and (max-width: 425px) {
   .blocoBlog .imageBlog {
      margin: 0;
      width: auto;
      height: auto;
   }
}

/* Dúvidas frequentes em serviços contém um checkbox oculto
 onde saia do grid
 */
.tabs input {
   /* width: 1px; */
   max-width: 722px;
}


@media screen and (max-width: 820px) {
   .tabs input { width: 535px;  }
}

@media screen and (max-width: 425px) {
   .tabs input { width: 345px;  }
}

@media screen and (max-width: 320px) {
   .tabs input { width: 275px;  }
}



.itemDuvida .tabs input { /* somente em duvidas frequentes - não em dúvidas de serviços */
   display: block;
    height: 51px;
}

.tab-content p {
  margin: 0 0 20px 0;
}


/* Ouvidoria */
.boxWidgetSidebarFormContato {
   font-family: 'Roboto Condensed';
    font-weight: 400;
    font-size: 16px;
    color: #808080;
}

.boxWidgetSidebarFormContato .row:not(:last-child) {
   margin-bottom: 30px;
}

.boxWidgetSidebarFormContato .col-icon {
   width: 30px;
   font-size: 25px;
   margin-right: 10px;
}

.boxWidgetSidebarFormContato .col-info {
   max-width: calc(100% - 40px);
}


/* Página Seu Ato Notarial Agora - Box de imagens com testes */
.listAtalhosTestes .infoAtalho p {
   margin-bottom: 0;
}


/* Página Faça sua Ata Notarial*/
/* se for título normal, este estilo abaixo. Se for Destaque usar a de baixo 
a.btnVoltarPagTestes {
   display: inline-block;
   margin-right: 0;
   position: absolute;
   right: 21px;
   font-size: 15px;
}
*/

a.btnVoltarPagTestes {
   display: inline-block;
   position: relative;
   right: -94%;
   font-size: 15px;
   top: -24px;
}

a.btnVoltarPagTestes:before {
   display: inline-block;
   text-rendering: auto;
   -webkit-font-smoothing: antialiased;
   font-family: "Font Awesome 5 Free";
   font-weight: 900;
   content: "\f062";
   padding-right: 8px;
}

@media screen and (max-width: 768px) {
   a.btnVoltarPagTestes {
      right: -90%;
   }
}

@media screen and (max-width: 425px) {
   a.btnVoltarPagTestes {
      right: -83%;
   }
}


/* Página Testar Webcam .../webcam-teste */
#frameWebCam {
   margin: 0px auto;
   max-width: 500px;
   height: auto;
   
}

#videoWebCam {
   max-width: 500px;
   height: auto;
   border: 1px #D7D7D7 solid;
   width: 100%;
}

#videoMensagem {
   text-align: center;
   margin-bottom: 25px;
   color: var(--azul);
   font-weight: bold;
}


/* Agendamento - loading */
#agendamento_loading {
   color: var(--azul);
}

#agendamento_loading:before {
   content: url(../img/carregando-circulo.gif);
   width: 20px;
    height: 20px;
    display: inline-block;
    margin-right: 15px;
    position: relative;
    top: 8px;
}


/**
* Em todas os modelos de páginas, sejam dinâmicas ou fixas,
* classe global
*/
/*
section.txtcontent { 
   overflow: hidden;  /* usado para o texto não expandir maior que a tela (mobile). Ao incluir erro no menu styck de serviços
}
*/



/* ajuste de ul / ol > li com bullets onde o texto fica alinhado correto 
* Pode ser organizada por meio da classe acima .txtcontent
*/
section ul:not(.menuBanner),
section ol {
   margin-left: 0;
   padding-left: 21px;
   text-indent: -21px;
}


/* Páginas de listagens de serviços */
section.txtcontent ul.subMenu,
section.txtcontent ul.subMenu ul,
section.txtcontent ul.subMenu ul a{
   text-indent: 0;
   padding-left: 0;
}

section.txtcontent .container-fluid .row img { max-width: 100%;}

.subMenu li ul li {
   font-size: 14px;
   line-height: 24px;
   color: var(--cinza);
   text-transform: unset;
}

/* toggle */
/* ajuste quando aberto o conteudo do FAQ */
input:checked ~ .tab-content {
   max-height: 3000px;
}

.tab-content { /* adicionar a app.css na linha 665 */
   font-family: 'Open Sans';
}



/**
* MODELOS Menu / Pagina de listagem
* por haver muitos modelos de contratos, oculta-se
* os links dos modelos no menu deixando exibir apenas o primeiro
**/
#menuTopo .hasSubMenu .subMenuCol_modelos .subMenuColItem li:not(.subMenuColItemTitle, .subMenuColItemLinkFirst ) { display: none; }

/* página intermediária - esconde o primeiro item */
body.facilidades .contentIncludeMenu ul.subMenu li.subItemCol_modelos ul li.subItemColLinkFirst { display: none;}

/* página de listagem final (modelos) */
body.modelos .contentIncludeMenu ul.subMenu > li:nth-child(2) { display: none;}

/* Imagens formato */
section.txtcontent .container-fluid picture img {
   max-width: 100%;
}


/* Menu perosnalizado por meio do painel > classes */
/* .color-padrao {} /* cinza padrao */

.color-azul,
.color-azul:before { color: var(--azul) !important;}

.color-vermelho,
.color-vermelho:before { color: #E83946 !important;}

.strong { font-weight: 700;}



.boxWidgetSidebar {
   margin-bottom: 41px;
}

.boxWidgetSidebar .btnDefault,
.boxWidgetSidebar a {
   font-family: 'Roboto Condensed';
}


/* Link âncora com margem */
:target:before {
   content:"";
   display:block;
   height:140px; /* fixed header height*/
   margin:-140px 0 0; /* negative fixed header height */
}

body.institucional :target:before {
   content:"";
   display:block;
   height:100px;
   margin:-140px 0 0; 
}



@media screen and (max-width: 768px) {
   :target:before {
      height:110px; 
      margin:-110px 0 0; 
   }
}


/* página institucional - seções alteradas */
.sec_content{
   padding: 0; 
   background:#fff;
}

.sec_content_color {
   background: #e5e4e4;
}

.sec_content .container-fluid {
   padding: 40px 0 60px 0;
}

#lgpdContent.sec_content {
   margin-bottom: 25px;
}

#tabeliaoContent.sec_content h2 {
   padding-left: 1rem;
}

.linkInstAnchor {
   position: relative;
   top: 42px;
}

/* Primeiro link conheca está duplicado devido ao link ancora no mobile, veja .btnOnly425 */
.content-institucional .content-sidebar .boxWidgetSidebar a{
   display: block;
   width: 100%;
}

.btnOnly425 {
   display: none !important;
}

@media screen and (max-width: 1024px) {
   .linkInstAnchor {
      top: 30px;
   }

   body.home .txtcontent {
      padding-top: 0;
   }

   footer nav,
   footer .btnsFooter { display: none;}

   footer .clCenter { margin: 0 0 15px 0;}
   footer .redesSociais { margin-top: 10px;}

   footer .copyright { padding-bottom: 0 ;}
   footer .infoCopyright { margin-bottom: 10px;}
}



/* página institucional, remove sidebar fixa */
@media screen and (max-width: 425px) {

   .content-institucional .content-sidebar .boxWidgetSidebar a:first-child {
      display: none;
   }

   .btnOnly425 {
      display: block !important;
   }

   .content-sidebar {
      position: inherit;
      width: 100%;
   }

   .boxWidgetSidebar {
      position: inherit;
   }

   .bannerDestaque .lingueta::after {
      bottom: -39px;
   }
}

.institucional .titlePage {
   margin-bottom: 25px;
}

.institucional_col01 {
   padding-left: 0;
}

/* Tabela de Preço / Custas notariais */
.custas-notariais-result {
   background: #F1F1F1;
   border-radius: 8px 8px 0 0;
   padding: 15px;
   margin: 25px 0 0 0;
}

.custas-notariais-result h2 {
   margin-top: 20px !important;
}

/*
.custas-notariais-btn-recalculo {}
.custas-notariais-infos {}
*/

.custas-notariais-sendmail {
   width: 100%;
   background: #e5e4e4;
   border-radius: 0 0 8px 8px;
   padding: 30px 15px 15px 15px;
}

.custas-notariais-result-mobile {
   display: none
}

@media screen and (max-width: 425px) {
   .custas-notariais-result-mobile {
      display: block;
   }

   .custas-notariais-result table {
      display: none;
   }
}


/* header.css */

/* ajuste da busca, mais para cima */
@media screen and (max-width: 1024px) {

   header.header-fixed {
      transform: translateX(0); /* Menu mobile com gap de ficar para cima... */
   }
   .searchForm {
      /*top: 110px; */
      top: 90px;
      max-width: 400px;
   }

   .menuHeader {
      padding-top: 70px;
   }

   header .hasSubMenu .subMenu {
      padding-bottom: 40px;
      top: 70px;
      height: -webkit-fill-available !important;
      padding-bottom: 70px;
   }

   /* .subMenu > li {
      min-height: 280px;
   } */

   .subMenu > li:first-child {
      min-height: auto;
   }

   .subMenu li:last-child ul li:last-child {
      margin-bottom: 100px;
   }
   
}

.menuHeader li a {
   align-items: flex-start;
}


@media screen and (max-width: 375px) {
   searchForm {
      width: 100%;
   }
}

/* Padding top reduzido */
section.titlePage,
section.txtcontent {
      padding-top: 20px;
   }

/* no menu 4 colunas expandido, sombra na lateral direita. Isso influencia o menu 5 colunas tb....
header .subMenu:after {
   box-shadow: 0px 5px 5px 0px rgba(0,0,0,0.1);
}
*/

/* Tabela de preço, paragrafo, espaçamento*/
section.txtcontent .txtcontenttable p { 
   margin-top: 0;
}


/* tabela de preço, tabela mobile com scroll horizontal */
section.txtcontent .txtcontenttable { 
   overflow-x: auto;
   width: 100%;
}

section.txtcontent .txtcontenttable table p { 
   margin: 5px 0;
}

section.txtcontent .txtcontenttable table { 
   margin-top: 0;
}

/* Facilidades Modelos (contratos) */
section.contentDownload .downloads {
   margin-top: 0;
}


/* menu mobile - seta voltar */
.closeSubMenu span::before {
   display: inline-block;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    content: "\f060";
    padding-right: 8px;
}


/* Links */
/*
.container-fluid a {
   color: var(--cinza);
    text-decoration: underline;
}

.container-fluid a:hover,
.container-fluid a:visited {
   text-decoration: none;
}
*/



/* USAR A CLASSE private-page para Cadeado */

a.private-page:before {
   display: inline-block;
   font-style: normal;
   font-variant: normal;
   text-rendering: auto;
   -webkit-font-smoothing: antialiased;
   font-family: "Font Awesome 5 Free";
   font-weight: 900;
   content: "\f023";
   font-size: 14px;;
   margin-right: 6px;
   color: #727272;
 }


 /* Duvidas frequentes - abrir suave a categoria */
 body.duvidas .boxWidgetSidebar { display: none;} /* Falar com gabriel */

 .itemDuvida summary {
   display: inline-block;
   font-family: 'Open Sans';
 }

 details summary {
   cursor: pointer;
   transition: margin 250ms ease-out;
 }
 
 details[open] {
   margin-bottom: 20px;
}

 details[open] summary {
   margin-bottom: 10px;
 }

 #searchFaq {
   top:0;
   position: inherit;
   z-index: inherit;
   max-width: unset;
   margin-bottom: 24px;
   transform: unset;
   transition: unset;
 }

 .btnFaqLimparFiltro {
   display: flex;
   justify-content: center;
   min-width:fit-content;
   margin-left:16px;
   font-size:15px;
 }


/* Resultado de busca */
/* label página de referência */
.itemSearch summary label {
   background: #f5f5f5;
   color: #00909dc7;
   padding: 3px 8px;
   border-radius: 5px;
   font-size: 13px;
   margin-left: 6px;
}

/* Busca suggestion */
.uneedForm .result,
.searchForm .result {
   top: 40px;
}

.uneedForm .result ul li a,
.searchForm .result ul li a {
   color: var(--cinza);
}

/* Minha conta - área restrita */
/* Lateral com nomes dos escreventes */
.contentServicesAccount .item:not(:last-child) {
   margin-bottom: 2px;
}

.item_personalize p {
   margin-top: 0;
}

.item_personalize form :is(input, select) {
   background-color: #f7f7f7 !important;
}

/* Acesso cliente - restrito */
.anchorLogin {
   display: block;
   height: 80px;
   width: 1px;
   margin-top: -50px;
}


/* Resultado Busca Atos 26 */
.resultadoBuscaAtos26 table.registros tr.head th:first-child { width: 20px;}

.resultadoBuscaAtos26 {
   overflow-x: auto;
}

#requestCertificate { position: absolute;}


section.bannerDisclaimer {
   margin-top: 80px;
}

section.bannerDisclaimer span {
   margin: 0 auto;
}

section.bannerDisclaimer span img {
   max-width: 100%;
   border-radius: 10px;
}

/* .bannerDisclaimer_desktop {} */
.bannerDisclaimer_mobile { display: none;}

@media screen and (max-width: 425px) {
   .bannerDisclaimer_desktop { display: none;}
   .bannerDisclaimer_mobile { display: block;}
}



/******* MODAL LGPD *********/
.cookie-info-banner {
 position: fixed;
 bottom: 32px;
 left: 10%;
 right: 10%;
 padding: 10px;
 max-width: 1080px;
 margin: 0 auto;
 background-color: #fff;
 border-radius: 5px;
 box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.2);
 border: 1px solid rgb(0 0 0 / 13%);
 display: flex;
 justify-content: space-between;
 font-family: 'Open Sans', sans-serif;
 z-index: 9;
}

.cookie-info-banner,
.cookie-info-banner a,
.cookie-info-banner:visited {
   font-size: 12px;
}

.cookie-info-banner :is(div):is(button):not(a) {
   display: flex;
}

.cookie-info-banner button {
  height: 20px;
  margin-left: 10px;
  background-color: #777;
  border: none;
  color: white;
  border-radius: 2px;
  cursor: pointer;
}

.cookie-info-banner button:hover {
   background-color: #ccc;
}


/* 
* Iphone ZOOM em campos input
*
* New: IOS will still zoom, unless you use 16px on the input without the focus.
* https://stackoverflow.com/questions/2989263/disable-auto-zoom-in-input-text-tag-safari-on-iphone
*
*/
@media screen and (-webkit-min-device-pixel-ratio:0) { 
  select,
  textarea,
  input {
    font-size: 16px;
  }
}