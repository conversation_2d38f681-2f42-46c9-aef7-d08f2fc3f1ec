<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

class Document extends Model
{
    use HasFactory;

    public function clerk()
    {
      return $this->hasOne(Collaborator::class, 'id', 'clerk_id');
    }

    public function client()
    {
      return $this->hasOne(Client::class, 'id', 'client_id');
    }

    public function fileSentAt($file) {
      if (isset($file->sent_at)) {
        return Carbon::parse($file->sent_at)->format('d/m/Y');
      }
      return '-';
    }
}
