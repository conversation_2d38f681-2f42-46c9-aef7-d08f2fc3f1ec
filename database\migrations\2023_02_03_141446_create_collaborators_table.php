<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('collaborators', function (Blueprint $table) {
          $table->id();
          $table->string('name')->unique();
          $table->string('phone')->nullable();
          $table->string('email')->unique();
          $table->string('password')->nullable();
          $table->string('type')->default('clerk');
          $table->json('permissions');
          $table->json('settings');
          $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('collaborators');
    }
};
