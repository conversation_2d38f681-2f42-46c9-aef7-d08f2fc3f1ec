@extends('layouts.admin')
@section('title', 'Clientes->Serviços')

@section('content')
    @php
        if (!empty($dynamic_page_settings)) {
            $settings = json_decode($dynamic_page_settings);
        }
    @endphp

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h4 class="fw-bold py-3 m-0">
            <span class="text-muted fw-light">Menus / </span> Clientes/Restrito
        </h4>
    </div>

    <form method="POST" action="{{ route('clientes.servicos.update') }}" enctype="multipart/form-data">
        @csrf

        <div class="card mt-4">
            <div class="card-body">
                <div class="row">
                    <div class="mb-3 col-sm-4">
                        @for ($i = 0; $i < 6; $i++)
                            <label class="form-label">Página {{ $i + 1 }}</label>
                            <select class="form-select" name="block1[]">
                                <option value="">Selecionar...</option>
                                @foreach ($pages as $thePage)
                                    <option value="{{ $thePage->id }}"
                                        @if (isset($settings->block1[$i]) && $settings->block1[$i] == $thePage->id) selected="selected" @endif>{{ $thePage->title }}
                                    </option>
                                @endforeach
                            </select>
                            <div class="row" style="margin-bottom: 1.3rem">
                                <div class="col-md-12">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-check mt-2">
                                                <input class="form-check-input" type="radio"
                                                    @if (isset($settings->{'color1' . $i}) && $settings->{'color1' . $i} == 'padrao') checked="checked" @endif
                                                    value="padrao" name="color1{{ $i }}">
                                                <label class="form-check-label">
                                                    Cor padrão
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check mt-2">
                                                <input class="form-check-input" type="radio" value="azul" id="corazul"
                                                    @if (isset($settings->{'color1' . $i}) && $settings->{'color1' . $i} == 'azul') checked="checked" @endif
                                                    name="color1{{ $i }}">
                                                <label class="form-check-label">
                                                    Cor azul
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check mt-2">
                                                <input class="form-check-input" type="radio" value="vermelho"
                                                    id="corvermelho"
                                                    @if (isset($settings->{'color1' . $i}) && $settings->{'color1' . $i} == 'vermelho') checked="checked" @endif
                                                    name="color1{{ $i }}">
                                                <label class="form-check-label">
                                                    Cor vermelho
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-check mt-2">
                                                <input class="form-check-input" type="checkbox" x-model="link.bold"
                                                    @if (isset($settings->{'bold1' . $i}) && $settings->{'bold1' . $i} == 'on') checked="checked" @endif
                                                    name="bold1{{ $i }}">
                                                <label class="form-check-label">
                                                    Negrito
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-5">
                                            <div class="form-check mt-2">
                                                <input class="form-check-input" type="checkbox" x-model="link.blank"
                                                    @if (isset($settings->{'blank1' . $i}) && $settings->{'blank1' . $i} == 'on') checked="checked" @endif
                                                    name="blank1{{ $i }}">
                                                <label class="form-check-label">
                                                    Abrir em nova guia
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endfor
                    </div>
                    <div class="mb-3 col-sm-4">
                        @for ($i = $i; $i < 12; $i++)
                            <label class="form-label">Página {{ $i + 1 }}</label>
                            <select class="form-select" name="block1[]">
                                <option value="">Selecionar...</option>
                                @foreach ($pages as $thePage)
                                    <option value="{{ $thePage->id }}"
                                        @if (isset($settings->block1[$i]) && $settings->block1[$i] == $thePage->id) selected="selected" @endif>{{ $thePage->title }}
                                    </option>
                                @endforeach
                            </select>
                            <div class="row" style="margin-bottom: 1.3rem">
                                <div class="col-md-12">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-check mt-2">
                                                <input class="form-check-input" type="radio"
                                                    @if (isset($settings->{'color1' . $i}) && $settings->{'color1' . $i} == 'padrao') checked="checked" @endif
                                                    value="padrao" name="color1{{ $i }}">
                                                <label class="form-check-label">
                                                    Cor padrão
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check mt-2">
                                                <input class="form-check-input" type="radio" value="azul" id="corazul"
                                                    @if (isset($settings->{'color1' . $i}) && $settings->{'color1' . $i} == 'azul') checked="checked" @endif
                                                    name="color1{{ $i }}">
                                                <label class="form-check-label">
                                                    Cor azul
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check mt-2">
                                                <input class="form-check-input" type="radio" value="vermelho"
                                                    id="corvermelho"
                                                    @if (isset($settings->{'color1' . $i}) && $settings->{'color1' . $i} == 'vermelho') checked="checked" @endif
                                                    name="color1{{ $i }}">
                                                <label class="form-check-label">
                                                    Cor vermelho
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-check mt-2">
                                                <input class="form-check-input" type="checkbox" x-model="link.bold"
                                                    @if (isset($settings->{'bold1' . $i}) && $settings->{'bold1' . $i} == 'on') checked="checked" @endif
                                                    name="bold1{{ $i }}">
                                                <label class="form-check-label">
                                                    Negrito
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-5">
                                            <div class="form-check mt-2">
                                                <input class="form-check-input" type="checkbox" x-model="link.blank"
                                                    @if (isset($settings->{'blank1' . $i}) && $settings->{'blank1' . $i} == 'on') checked="checked" @endif
                                                    name="blank1{{ $i }}">
                                                <label class="form-check-label">
                                                    Abrir em nova guia
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endfor
                    </div>
                </div>
            </div>
        </div>
        <button class="btn btn-primary mt-5">Salvar</button>
    </form>


    <br><br><br><br><br><br><br>
    <script nonce="{{ csp_nonce() }}">
        $('input').removeAttr('required'); // TODO: remove
    </script>
@endsection
