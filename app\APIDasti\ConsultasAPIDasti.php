<?php

namespace App\APIDasti;

class ConsultasAPIDasti{
    protected $urlAPI;
    protected $tokenAPI;
    protected $curl;

    function __construct()
    {
        $this->urlAPI = env('URL_API_DASTI');
        $this->tokenAPI = env('TOKEN_API_DASTI');
        $this->curl = curl_init();
        curl_setopt($this->curl, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($this->curl, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($this->curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($this->curl, CURLOPT_HTTPHEADER, array(
        'Authorization:Bearer ' . $this->tokenAPI,
        ));
    }

    function retornaConsulta($urlConsulta){
        curl_setopt($this->curl, CURLOPT_URL, $this->urlAPI . $urlConsulta);
        $retorno = curl_exec($this->curl);
        $erro = curl_error($this->curl);
        curl_close($this->curl);
        return $retorno;
    }

    function pesquisaSituacaoProtocolo($protocolo, $cpfCnpj){
        return $this->retornaConsulta("/notas/escritura-procuracao/escritura-protocolo-cpf?protocolo=$protocolo&documento=$cpfCnpj");
    }

    function pesquisaAtosData($data){
        return $this->retornaConsulta("/notas/atos/datalavraturas?data=$data");
    }

    function pesquisarLivroFolha($livro, $folha){
        return $this->retornaConsulta("/notas/escritura-procuracao/livro-folha?livro=$livro&folha=$folha");
    }

    function pesquisarOutorganteOutorgadoPorNome($outorgante, $outorgado){
        $outorgante = urlencode($outorgante);
        $outorgado = urlencode($outorgado);

        return $this->retornaConsulta("/notas/indicador-pessoal/outorgante-outorgado?outorgante=$outorgante&outorgado=$outorgado");
    }

    function pesquisarProtocolosPorDocumento($documento){
        return $this->retornaConsulta("/notas/indicador-pessoal/documento?documento=$documento");
    }

    function pesquisarOutorganteOutorgadoPorCpfOuCnpj($cpfOuCnpjOutorgante, $cpfOuCnpjOutorgado){
        return $this->retornaConsulta("/notas/indicador-pessoal/cpfcnpj?CpfCnpjOutorgante=$cpfOuCnpjOutorgante&CpfCnpjOutorgado=$cpfOuCnpjOutorgado");
    }

    function ConsultarCertidaoNotasPorCPFCNPJLivroFolha($cpfCnpj, $livro, $folha){
        $cpfCnpj = str_replace('.', '', $cpfCnpj);
        $cpfCnpj = str_replace('-', '', $cpfCnpj);
        $cpfCnpj = str_replace('/', '', $cpfCnpj);
        return $this->retornaConsulta("/notas/certidao/andamento-cpfcnpj-livrofolha?cpfCnpj=$cpfCnpj&livro=$livro&folha=$folha");
    }

    function consultarSinalPublico($escrevente){
        $escrevente = urlencode($escrevente);
        return $this->retornaConsulta("/assina/sinal-publico/consulta?escrevente=$escrevente");
    }

    function consultarCartaoAssinaturaPorNome($nome){
        $nome = urlencode($nome);
        return $this->retornaConsulta("/assina/cartao-assinatura/nome?nome=$nome");
    }

    function consultarCartaoAssinaturaPorCPF($cpf){
        return $this->retornaConsulta("/assina/cartao-assinatura/cpf?cpf=$cpf");
    }

}
