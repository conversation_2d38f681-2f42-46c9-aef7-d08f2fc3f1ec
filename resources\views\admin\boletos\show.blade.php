@extends('layouts.admin')
@section('title', 'Boletos')

@section('content')
<h4 class="fw-bold py-3 mb-4">
  <span class="text-muted fw-light">Boletos / </span> Informações
</h4>

<div class="card">
  <div class="card-header">
    <h5>Dad<PERSON> do Ato</h5>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="mb-3 col-sm-6">
        <label class="form-label">Protocolo</label>
        <input type="text" class="form-control" value="{{ $boleto->protocol }}" readonly>
      </div>
      <div class="mb-3 col-sm-6">
        <label class="form-label">Natureza</label>
        <input type="text" class="form-control @error('nature') is-invalid @enderror" placeholder="Digite a natureza do ato" name="nature" value="{{ $boleto->nature }}" readonly>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-sm-6">
        <label class="form-label">Escrevente</label>
        <input type="text" class="form-control" value="{{ $boleto->clerk->name ?? "" }}" readonly>
      </div>
    </div>
  </div>
</div>

<div class="card mt-4">
  <div class="card-header">
    <h5>Dados da Parte</h5>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="mb-3 col-sm-6">
        <label class="form-label">Nome / Razão social</label>
        <input type="text" class="form-control @error('name') is-invalid @enderror" placeholder="Digite nome/razão" name="name" value="{{ $boleto->name }}" readonly>
      </div>
      <div class="mb-3 col-sm-6">
        <label class="form-label">CPF / CNPJ</label>
        <input type="text" class="form-control @error('cpf_cnpj') is-invalid @enderror" placeholder="Digite CPF/CNPJ" name="cpf_cnpj" value="{{ $boleto->document }}" readonly>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-sm-4">
        <label class="form-label">CEP</label>
        <div class="input-group align-items-center">
          <input type="text" class="form-control" value="{{ $boleto->cep }}" data-mask="00000-000" pattern=".{8,}" readonly>
        </div>
      </div>
      <div class="mb-3 col-sm-4">
        <label class="form-label">Logradouro (rua, avenida, travessa)</label>
        <input type="text" id="place_type" class="form-control @error('place_type') is-invalid @enderror" placeholder="Digite o logradouro" name="place_type" value="{{ $boleto->place_type }}" readonly>
      </div>
      <div class="mb-3 col-sm-4">
        <label class="form-label">Número</label>
        <input type="text" class="form-control @error('number') is-invalid @enderror" placeholder="Digite o número" name="number" value="{{ $boleto->number }}" readonly>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-sm-4">
        <label class="form-label">Cidade</label>
        <input type="text" class="form-control @error('city') is-invalid @enderror" placeholder="Digite a cidade" name="city" id="city" value="{{ $boleto->city }}" readonly>
      </div>
      <div class="mb-3 col-sm-4">
        <label class="form-label">Bairro</label>
        <input type="text" id="neighborhood" class="form-control @error('neighborhood') is-invalid @enderror" placeholder="Digite o logradouro" name="neighborhood" value="{{ $boleto->neighborhood }}" readonly>
      </div>
      <div class="mb-3 col-sm-4">
        <label class="form-label">UF</label>
        <input type="text" id="uf" class="form-control @error('uf') is-invalid @enderror" placeholder="Digite o uf" name="uf" value="{{ $boleto->uf }}" readonly>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-sm-12">
        <label class="form-label">Complemento</label>
        <input type="text" class="form-control @error('complement') is-invalid @enderror" placeholder="Digite o complemento" name="complement" value="{{ $boleto->complement }}" readonly>
      </div>
    </div>
  </div>
</div>

<div class="card mt-4">
  <div class="card-header">
    <h5>Valor do Ato</h5>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="mb-3 col-sm-4">
        <div id="previous-deposit-container" class="mb-3" @if($boleto->previous_deposit <= 0) style="display: none;" @endif>
          <label class="form-label">Depósito prévio </label>
          <input type="text" class="form-control money" placeholder="Digite o valor" name="previous_deposit" id="previous_deposit" value="{{ number_format($boleto->previous_deposit, 2) }}" readonly onkeyup="updateTotal()">
        </div>
        <input type="checkbox" class="form-check-input" @checked($boleto->previous_deposit > 0) disabled>
        <label class="form-label">Cobrar depósito prévio?</label>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-sm-4">
        <label class="form-label">Custas Integrais</label>
        <input type="text" id="total_costs" class="form-control money @error('total_costs') is-invalid @enderror" placeholder="Digite o valor" name="total_costs" value="{{ number_format($boleto->total_costs, 2) }}" readonly onkeyup="updateTotal()">
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-sm-4">
        <label class="form-label">Outros (registros, tributos, certidões, diligências etc.)</label>
        <input type="text" id="others_costs" class="form-control money" value="{{ number_format($boleto->others_costs, 2) }}" readonly>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-sm-4">
        <label class="form-label">Taxa de emissão</label>
        <input type="text" class="form-control" value="R$ 2,45" readonly>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-sm-4">
        <label class="form-label">Total</label>
        <input type="text" id="total" class="form-control money @error('total') is-invalid @enderror" placeholder="R$ 0,00" name="total" readonly>
      </div>
    </div>
  </div>
</div>

<div class="card mt-4">
  <div class="card-header">
    <h5>Boleto</h5>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="mb-3 col-sm-6">
        <label class="form-label">Senha de acesso</label>
        <div class="input-group align-items-center">
          <input type="text" class="form-control" value="{{ $boleto->access_password }}" id="access_password" readonly>
          <button class="btn btn-primary" onclick="copyField(this, 'access_password')" data-clipboard-target="#access_password">Copiar</button>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-sm-6">
        <label class="form-label">Código de barras</label>
        <div class="input-group align-items-center">
          <input type="text" class="form-control" value="{{ $boleto->barcode }}" id="barcode" readonly>
          <button class="btn btn-primary" onclick="copyField(this, 'barcode')" data-clipboard-target="#barcode">Copiar</button>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-sm-6">
        <label class="form-label">Enviar por email</label>
        <div class="input-group align-items-center">
          <input type="text" class="form-control" id="email" placeholder="Informe o e-mail">
          <button class="btn btn-primary" onclick="sendMail(this)">Enviar</button>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-sm-4">
        <a href="{{ $boleto->link }}" target="blank" class="btn btn-primary text-white mt-2">Baixar Boleto</a>
      </div>
    </div>
  </div>
</div>

<br><br><br>

@endsection

<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.4/clipboard.min.js"></script>
<script nonce="{{ csp_nonce() }}">
  setTimeout(() => {
    updateTotal();
  }, 600);

  function updateTotal()
  {
    var pd = removeMask($('#previous_deposit').val());
    var tc = removeMask($('#total_costs').val());
    var oc = removeMask($('#others_costs').val());
    var t = +oc + +tc + +pd + 2.45;
    t = t.toLocaleString("pt-BR", {style:"currency", currency:"BRL"});
    $('#total').val(t);
  }

  function removeMask(val)
  {
    val = val.replaceAll('.', '');
    return val.replaceAll(',', '.');
  }

  function copyField(button, fieldID)
  {
    $(button).html('Copiado!');
  }

  function sendMail(button)
  {
    $.get("{{ route('boletos.send_mail') }}", {
      to: $('#email').val(),
      id: "{{ $boleto->id }}"
    }, function() {
      $(button).html('Enviado!');
    });
  }
</script>
