<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Mail\Mailables\Address;
use App\Models\Boleto;

class SendBoleto extends Mailable
{
    use Queueable, SerializesModels;

    public $boleto;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Boleto $boleto)
    {
      $this->boleto = $boleto;
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
          from: new Address(env('MAIL_FROM_ADDRESS'), env('MAIL_FROM_NAME')),
          subject: 'Boleto para pagamento - 26notas',
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(
            view: 'emails.boleto',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}
