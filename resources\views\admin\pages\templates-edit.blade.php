@extends('layouts.admin')
@section('title', 'Modelos')

@section('content')
    <h4 class="fw-bold py-3 mb-4">
        <span class="text-muted fw-light">Páginas / </span><span class="text-muted fw-light">Modelos /</span> Editar
    </h4>

    <form method="POST" action="{{ route('pages.templates.update', [$page->id]) }}">
        @csrf

        <div class="card">
            <h5 class="card-header">Configurações</h5>
            <div class="card-body">
                <div class="row">
                    <div class="mb-3 col-sm-8">
                        <label class="form-label">Titulo</label>
                        <input type="text" class="form-control @error('title') is-invalid @enderror"
                            placeholder="Digite o título" name="title" value="{{ $page->title }}" required>
                    </div>
                    <div class="mb-3 col-sm-4">
                        <label class="form-label">Slug</label>
                        <input type="text" class="form-control @error('slug') is-invalid @enderror"
                            placeholder="Digite o slug" name="slug" value="{{ $page->slug }}" required>
                    </div>
                </div>

                <div class="row">
                    <div class="mb-3 col-sm-5">
                        <label class="form-label">Descrição breve</label>
                        <textarea type="text" class="form-control @error('description') is-invalid @enderror"
                            placeholder="Digite a descrição" name="description" required rows="4">{{ $page->description }}</textarea>
                    </div>

                    <div class="col-sm-3">
                        <label class="form-label">Template:</label>
                        <select class="form-select" name="template" id="template" onchange="ShowHideTipoBitrix(this.value)"
                            x-data x-model="$store.constructor.template">
                            <option value="">Selecione um template</option>
                            <option value="services" @selected($page->template == 'services')>Serviços</option>
                            <option value="contracts" @selected($page->template == 'contracts')>Contratos</option>
                            <option value="default" @selected($page->template == 'default')>Padrão</option>
                        </select>
                    </div>

                    <div class="mb-3 col-sm-3">
                        <div class="form-check mt-3">
                            <input class="form-check-input" type="checkbox" @checked($page->published) id="published"
                                name="published">
                            <label class="form-check-label" for="published">
                                Publicada
                            </label>
                        </div>
                        <div class="form-check mt-3">
                            <input class="form-check-input" type="checkbox" @checked($page->restricted_access)
                                id="restricted_access" name="restricted_access">
                            <label class="form-check-label" for="restricted_access">
                                Página Restrita
                            </label>
                        </div>
                        <template x-data x-if="$store.constructor.template == 'default'">
                            <div class="form-check mt-3">
                                <input class="form-check-input" type="checkbox"
                                    @if ($page->general_settings->banner_sidebar ?? false) checked @endif id="banner_sidebar"
                                    name="banner_sidebar">
                                <label class="form-check-label" for="banner_sidebar">
                                    Exibir banners de novidades
                                </label>
                            </div>
                        </template>
                    </div>
                    <div class="mb-3 col-sm-3" id="divTipoBitrix">
                        <label class="form-label">Tipo do formulário no Bitrix:</label>
                        <select class="form-select" name="tipo_bitrix">
                            <option value="">Selecione um tipo</option>
                            @foreach ($tiposFormulariosBitrix as $tipoFormulariosBitrix)
                                <option value="{{ $tipoFormulariosBitrix->STATUS_ID }}"
                                    @if ((json_decode($page->template_form_settings)->tipo_bitrix ?? '') == $tipoFormulariosBitrix->STATUS_ID) selected @endif>{{ $tipoFormulariosBitrix->NAME }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <template x-data x-if="$store.constructor.template == 'services'">
            <div class="card mt-4" x-data="formComponent(@js($page->template_form_settings))">
                <h5 class="card-header">Formulário</h5>
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1 me-3">
                            <label class="form-label">Titulo</label>
                            <input type="text" class="form-control @error('form_title') is-invalid @enderror"
                                placeholder="Digite o título" name="form_title" x-model="form.title">
                        </div>
                        <div class="form-check mt-3">
                            <input class="form-check-input" type="checkbox" id="form_show" name="form_show"
                                x-model="form.show">
                            <label class="form-check-label" for="form_show">Exibir no site</label>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="mb-3 col-sm-6">
                            <label class="form-label">E-mails para receber o formulário, separe por ponto e vírgula</label>
                            <input type="text" class="form-control @error('form_recipients') is-invalid @enderror"
                                placeholder="<EMAIL>; <EMAIL>; <EMAIL>;" name="form_recipients"
                                x-model="form.recipients">
                        </div>

                        <div class="col-sm-3">
                            <label class="form-label">Formulário</label>
                            <select type="text" class="form-select" x-model="form.model"
                                x-on:change="modelSelected()">
                                <option value="">Selecionar...</option>
                                <option value="a">a</option>
                                <option value="b">b</option>
                                <option value="c">c</option>
                                <option value="d">d</option>
                                <option value="e">e</option>
                                <option value="f">f</option>
                                <option value="g">g</option>
                                <option value="h">h</option>
                                <option value="i">i</option>
                                <option value="j">j</option>
                            </select>
                        </div>

                        <div class="col-sm-3">
                            <label class="form-label">Template de resposta (email)</label>
                            <select type="text" class="form-select" x-model="form.email_template">
                                <option value="">Selecionar...</option>
                                <option value="servico-resposta-padrao-user">Básico - sem assinatura</option>
                                <option value="servico-resposta-colaboradores01-user">Assinatura Colaborador - 01</option>
                                <option value="servico-resposta-colaboradores02-user">Assinatura Colaborador - 02</option>
                                <option value="servico-carta-sentenca-notarial-user">Assinatura Colaborador - 03</option>
                                <option value="servico-escritura-testamento-publico-user">Assinatura Colaborador - 04
                                </option>
                                <option value="servico-autorizacao-viagem-menores-user">Assinatura Colaborador - 05
                                </option>
                                <option value="certificate-request-protocol">Pedido Certidão Notarial</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="mb-3 col-sm-6">
                            <label class="form-label">Texto auxílio análise</label>
                            <input type="text" class="form-control"
                                placeholder="Para auxiliar na análise ..." name="texto_auxilio_analise"
                                x-model="form.texto_auxilio_analise">
                        </div>
                    </div>

                    <template x-if="form.model == 'a' || form.model == 'f' || form.model == 'g'">
                        <div class="row">
                            <div class="col-sm-3">
                                <label class="form-label">Informe o título da caixa de seleção</label>
                                <input type="text" class="form-control" placeholder="ex: Objeto da Constatação"
                                    x-model="form.settings.select_name">
                            </div>
                            <div class="col-sm-9">
                                <label class="form-label">Informe as opções da caixa de seleção, separadas por ponto e
                                    vírgula</label>
                                <input type="text" class="form-control" placeholder="opção 1; opção 2; opção 3;"
                                    x-model="form.settings.select_options">
                            </div>
                        </div>
                    </template>

                    <template
                        x-if="form.model == 'e' || form.model == 'f' || form.model == 'g' || form.model == 'h' || form.model == 'j'">
                        <div class="row">
                            <div class="col-sm-6">
                                <label class="form-label">Selecione as áreas(de escreventes) disponíveis</label>
                                <select type="text" class="form-control" x-model="form.settings.areas" required
                                    multiple>
                                    <option>Selecionar</option>
                                    @foreach ($collaboratorOccupations as $occupation)
                                        <option value="{{ $occupation->id }}">{{ $occupation->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </template>
                    <template x-if="form.model == 'd'">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="row">
                                    <div class="col-sm-11">
                                        <label class="form-label">Tipos</label>
                                        <select class="form-select" name="type" onchange="ValidaTipoAto(this.value)"
                                            name="tiposAtos" id="tiposAtos">
                                            <option value="">Selecione o tipo</option>
                                        </select>
                                    </div>
                                    <div class="col-sm-1">
                                        <label class="form-label">&nbsp;</label><br>
                                        <button class="btn btn-danger" id="btnExcluirTipoAto" style="display: none"
                                            onclick="ExcluirItemTipoAto();" type="button">-</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-7 mt-3">
                                <label class="form-label">Novo Tipo</label>
                                <input type="text" id="txtNovoTipo" class="form-control">
                            </div>
                            <div class="col-sm-2">
                                <label class="form-label" label-for="chkExibirAnalise"></label><br /><br />
                                <input type="checkbox" class="form-checkbox" id="chkExibirAnalise"
                                    name="chkExibirAnalise" value="1">
                                <b><label for="chkExibirAnalise">Exibir Análise</label></b>
                            </div>
                            <div class="col-sm-2">
                                <label class="form-label" label-for="chkUploadObrigatorio"></label><br /><br />
                                <input type="checkbox" class="form-checkbox" id="chkUploadObrigatorio"
                                    name="chkUploadObrigatorio" value="1">
                                <b><label for="chkUploadObrigatorio">Upload obrigatório</label></b>
                            </div>
                            <div class="col-sm-1">
                                <label class="form-label">&nbsp;</label><br>
                                <button class="btn btn-success" onclick="AdicionarItemTipoAto();"
                                    type="button">+</button>
                            </div>
                            <div class="row mt-3 mb-3">
                                <div class="col-sm-12">
                                    <span id="mensagemSucesso" style="display: none; font-weight: bold;"></span>
                                </div>
                            </div>
                            <div class="col-sm-12">
                                <div class="row">
                                    <div class="col-sm-11">
                                        <label class="form-label">Finalidades</label>
                                        <select class="form-select" name="type"
                                            onchange="ValidaFinalidade(this.value)" name="finalidades" id="finalidades">
                                            <option value="">Selecione a finalidade</option>
                                        </select>
                                    </div>
                                    <div class="col-sm-1">
                                        <label class="form-label">&nbsp;</label><br>
                                        <button class="btn btn-danger" id="btnExcluirFinalidade" style="display: none"
                                            onclick="ExcluirItemFinalidade();" type="button">-</button>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-sm-11">
                                        <label class="form-label">Nova Finalidade</label>
                                        <input type="text" id="txtNovaFinalidade" class="form-control">
                                    </div>
                                    <div class="col-sm-1">
                                        <label class="form-label">&nbsp;</label><br>
                                        <button class="btn btn-success" onclick="AdicionarItemFinalidade();"
                                            type="button">+</button>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="ol-sm-12">
                                        <span id="mensagemSucessoFinalidade"
                                            style="display: none; font-weight: bold;"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>

                <input type="hidden" name="form" x-model="JSON.stringify(form)">
                <input type="hidden" name="tiposAtosHidden" id="tiposAtosHidden">
                <input type="hidden" name="finalidadesHidden" id="finalidadesHidden">
            </div>

        </template>

        <div x-data="builderComponent(@js($questionCategories), @js($page->template_settings))">
            <h4 class="mt-5">Construtor</h4>

            <div id="sessionsContainer">
                <template x-for="(session, index) in sessions">
                    <div class="session" draggable="true" x-on:dragstart="dragStartIndex = index"
                        x-on:dragover.prevent="indexHover = index"
                        x-on:drop="dragEndIndex = index; changePosition(dragStartIndex, dragEndIndex); setTimeout(() => indexHover = null, 200)">
                        <template x-if="session.type == 'content'">
                            <div class="card mt-4">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between mb-1">
                                        <div class="d-flex">
                                            <i class='bx bxs-sort-alt cursor-move me-2'></i>
                                            <h5>Conteúdo</h5>
                                        </div>
                                        <i class='bx bx-trash text-danger cursor-pointer'
                                            x-on:click="removeSession(index, 'content')"></i>
                                    </div>
                                    <div class="row">
                                        <div class="mb-3 col-sm-5">
                                            <label class="form-label">Titulo</label>
                                            <input type="text" class="form-control" placeholder="Digite o título"
                                                x-model="session.title">
                                        </div>
                                        <div class="mb-3 col-sm-7 d-flex align-items-center">
                                            <div class="form-check mt-3" x-id="['red_title']">
                                                <input class="form-check-input" type="checkbox"
                                                    x-model="session.red_title" :id="$id('red_title')">
                                                <label class="form-check-label" :for="$id('red_title')">
                                                    Título Vermelho
                                                </label>
                                            </div>
                                            <div class="form-check mt-3 ms-3" x-id="['highlighted_title']">
                                                <input class="form-check-input" type="checkbox"
                                                    x-model="session.highlighted_title" :id="$id('highlighted_title')">
                                                <label class="form-check-label" :for="$id('highlighted_title')">
                                                    Título em Destaque
                                                </label>
                                            </div>
                                            <div class="form-check mt-3 ms-3" x-id="['content_block']">
                                                <input class="form-check-input" type="checkbox"
                                                    x-model="session.content_block" :id="$id('content_block')">
                                                <label class="form-check-label" for="content_block"
                                                    :for="$id('content_block')">
                                                    Conteúdo blocado
                                                </label>
                                            </div>
                                            <template x-if="$store.constructor.template != 'default'">
                                                <div class="form-check mt-3 ms-3" x-id="['add_sidebar']">
                                                    <input class="form-check-input" type="checkbox"
                                                        x-model="session.add_sidebar" :id="$id('add_sidebar')">
                                                    <label class="form-check-label" for="add_sidebar"
                                                        :for="$id('add_sidebar')">
                                                        Adicionar à sidebar
                                                    </label>
                                                </div>
                                            </template>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="mb-3 col-sm-12">
                                            <div class="editor" x-on:change="editorContentChanges($event, index)"
                                                x-html="session.content"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>

                        <template x-if="session.type == 'numered_box'">
                            <div class="card mt-4">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between mb-1">
                                        <div class="d-flex">
                                            <i class='bx bxs-sort-alt cursor-move me-2'></i>
                                            <h5>Box numerado</h5>
                                        </div>
                                        <i class='bx bx-trash text-danger cursor-pointer'
                                            x-on:click="removeSession(index)"></i>
                                    </div>
                                    <div class="row">
                                        <div class="mb-3 col-sm-6">
                                            <label class="form-label">Titulo</label>
                                            <input type="text" class="form-control " placeholder="Digite o título"
                                                x-model="session.title">
                                        </div>
                                        <div class="mb-3 col-sm-6 d-flex align-items-center">
                                            <div class="form-check mt-3" x-id="['red_title']">
                                                <input class="form-check-input" type="checkbox"
                                                    x-model="session.red_title" :id="$id('red_title')">
                                                <label class="form-check-label" for="red_title" :for="$id('red_title')">
                                                    Título Vermelho
                                                </label>
                                            </div>
                                            <div class="form-check mt-3 ms-3" x-id="['highlighted_title']">
                                                <input class="form-check-input" type="checkbox"
                                                    x-model="session.highlighted_title" :id="$id('highlighted_title')">
                                                <label class="form-check-label" :for="$id('highlighted_title')">
                                                    Título em Destaque
                                                </label>
                                            </div>
                                            <div class="form-check mt-3 ms-3" x-id="['add_sidebar']">
                                                <input class="form-check-input" type="checkbox"
                                                    x-model="session.add_sidebar" :id="$id('add_sidebar')">
                                                <label class="form-check-label" :for="$id('add_sidebar')">
                                                    Adicionar à sidebar
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <template x-for="(text, textIndex) in session.texts">
                                            <div class="d-flex justify-content-between align-items-center mb-3"
                                                draggable="true" x-on:dragstart="session.dragStartIndex = textIndex"
                                                x-on:drop="session.dragEndIndex = textIndex; changeItemPosition(session.dragStartIndex, session.dragEndIndex, index, 'numered_box');">
                                                <div class="w-100 me-3">
                                                    <i class="bx bxs-sort-alt cursor-move"></i>
                                                    <label class="form-label">Texto</label>
                                                    <input type="text" class="form-control "
                                                        placeholder="Digite o texto" x-model="text.title">
                                                </div>
                                                <i class='bx bx-trash text-danger cursor-pointer'
                                                    x-on:click="removeTextFromNumeredBox(index, textIndex)"></i>
                                            </div>
                                        </template>
                                    </div>
                                    <button type="button" class="btn btn-primary mt-3"
                                        x-on:click="addTextToNumeredBox(index)">Adicionar texto</button>
                                </div>
                            </div>
                        </template>

                        <template x-if="session.type == 'image_box'">
                            <div class="card mt-4">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between mb-1">
                                        <div class="d-flex">
                                            <i class='bx bxs-sort-alt cursor-move me-2'></i>
                                            <h5>Box com Imagem</h5>
                                        </div>
                                        <i class='bx bx-trash text-danger cursor-pointer'
                                            x-on:click="removeSession(index)"></i>
                                    </div>
                                    <div class="d-flex flex-wrap">
                                        <template x-for="(block, blockIndex) in session.blocks">
                                            <div class="d-flex justify-content-between align-items-center mb-3 flex-wrap"
                                                draggable="true"
                                                style="background: #f2f2f2;border-radius: 5px;padding: 16px;"
                                                x-on:dragstart="session.dragStartIndex = blockIndex"
                                                x-on:drop="session.dragEndIndex = blockIndex; changeItemPosition(session.dragStartIndex, session.dragEndIndex, index, 'image_box');">
                                                <div class="d-flex w-100">
                                                    <i class="bx bxs-sort-alt cursor-move me-2"></i>
                                                    <h6 x-text="'Box ' + (blockIndex + 1)"></h6>
                                                </div>
                                                <div class="row flex-grow-1 me-2">
                                                    <div class="col-sm-6">
                                                        <label class="form-label">Título</label>
                                                        <input class="form-control" x-model="block.title">
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <label class="form-label">Descrição breve:</label>
                                                        <input class="form-control" x-model="block.desc">
                                                    </div>
                                                </div>
                                                <div class="row flex-grow-1 me-2 mt-3 w-100">
                                                    <div class="col-sm-6">
                                                        <label class="form-label">Link</label>
                                                        <input class="form-control" x-model="block.link">
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <label class="form-label">Texto do Link</label>
                                                        <input class="form-control" x-model="block.text_link">
                                                    </div>
                                                </div>
                                                <div class="row flex-grow-1 mt-3">
                                                    <div class="col-sm-6">
                                                        <label class="form-label">Arquivo</label>
                                                        <input class="form-control" type="file" id="formFile"
                                                            x-on:change="imageBlockChosen($event, index, blockIndex)">
                                                        <span x-text="block.original_name"></span>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <i class='bx bx-trash text-danger cursor-pointer'
                                                            x-on:click="removeBlockFromImageBox(index, blockIndex)"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                    </div>
                                    <button type="button" class="btn btn-primary mt-3"
                                        x-on:click="addBlocksToImageBoxSession(index)">Adicionar Box</button>
                                </div>
                            </div>
                        </template>

                        <template x-if="session.type == 'faq'">
                            <div class="card mt-4">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between mb-1">
                                        <div class="d-flex">
                                            <i class='bx bxs-sort-alt cursor-move me-2'></i>
                                            <h5>FAQ</h5>
                                        </div>
                                        <i class='bx bx-trash text-danger cursor-pointer'
                                            x-on:click="removeSession(index)"></i>
                                    </div>
                                    <div class="row">
                                        <div class="mb-3 col-sm-6">
                                            <label class="form-label">Titulo</label>
                                            <input type="text" class="form-control " placeholder="Digite o título"
                                                x-model="session.title">
                                        </div>
                                        <div class="mb-3 col-sm-6 d-flex align-items-center">
                                            <div class="form-check mt-3" x-id="['red_title']">
                                                <input class="form-check-input" type="checkbox"
                                                    x-model="session.red_title" :id="$id('red_title')">
                                                <label class="form-check-label" :for="$id('red_title')">
                                                    Título Vermelho
                                                </label>
                                            </div>
                                            <div class="form-check mt-3 ms-3" x-id="['highlighted_title']">
                                                <input class="form-check-input" type="checkbox"
                                                    x-model="session.highlighted_title" :id="$id('highlighted_title')">
                                                <label class="form-check-label" :for="$id('highlighted_title')">
                                                    Título em Destaque
                                                </label>
                                            </div>
                                            <div class="form-check mt-3 ms-3" x-id="['add_sidebar']">
                                                <input class="form-check-input" type="checkbox"
                                                    x-model="session.add_sidebar" :id="$id('add_sidebar')">
                                                <label class="form-check-label" :for="$id('add_sidebar')">
                                                    Adicionar à sidebar
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <template x-for="(question, questionIndex) in session.questions">
                                        <div class="d-flex justify-content-between align-items-center mb-3"
                                            draggable="true" x-on:dragstart="session.dragStartIndex = questionIndex"
                                            x-on:drop="session.dragEndIndex = questionIndex; changeItemPosition(session.dragStartIndex, session.dragEndIndex, index, 'faq');">
                                            <div class="w-100 me-3">
                                                <i class="bx bxs-sort-alt cursor-move"></i>
                                                <label class="form-label">Pergunta</label>
                                                <input type="text" class="form-control " readonly
                                                    x-model="question.question">
                                            </div>
                                            <i class='bx bx-trash text-danger cursor-pointer'
                                                x-on:click="removeQuestionFromFAQ(index, questionIndex)"></i>
                                        </div>
                                    </template>

                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <label class="form-label">Categoria</label>
                                            <select type="text" class="form-select "
                                                x-model="session.question_selector.category_id">
                                                <option value="">Selecione uma categoria</option>
                                                <template x-for="category in questionCategories">
                                                    <option x-model="category.id" x-text="category.category"></option>
                                                </template>
                                            </select>
                                        </div>

                                        <div class="col-sm-6">
                                            <template x-for="category in questionCategories">
                                                <template x-if="session.question_selector.category_id == category.id">
                                                    <div>
                                                        <label class="form-label">Pergunta</label>
                                                        <select type="text" class="form-select "
                                                            x-model="session.question_selector.question">
                                                            <option value="">Selecione uma pergunta</option>
                                                            <template x-for="question in category.questions">
                                                                <option x-model="JSON.stringify(question)"
                                                                    x-text="question.question"></option>
                                                            </template>
                                                        </select>
                                                    </div>
                                                </template>
                                            </template>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-primary mt-3"
                                        x-on:click="addQuestionToFAQ(index)">Adicionar dúvida</button>
                                </div>
                            </div>
                        </template>

                        <template x-if="session.type == 'files'">
                            <div class="card mt-4">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between mb-1">
                                        <div class="d-flex">
                                            <i class='bx bxs-sort-alt cursor-move me-2'></i>
                                            <h5>Arquivos</h5>
                                        </div>
                                        <i class='bx bx-trash text-danger cursor-pointer'
                                            x-on:click="removeSession(index)"></i>
                                    </div>
                                    <div class="row">
                                        <div class="mb-3 col-sm-6">
                                            <label class="form-label">Titulo</label>
                                            <input type="text"
                                                class="form-control @error('title') is-invalid @enderror"
                                                placeholder="Digite o título" x-model="session.title" required>
                                        </div>
                                        <div class="mb-3 col-sm-6 d-flex align-items-center">
                                            <div class="form-check mt-3" x-id="['red_title']">
                                                <input class="form-check-input" type="checkbox"
                                                    x-model="session.red_title" :id="$id('red_title')">
                                                <label class="form-check-label" :for="$id('red_title')">
                                                    Título Vermelho
                                                </label>
                                            </div>
                                            <div class="form-check mt-3 ms-3" x-id="['highlighted_title']">
                                                <input class="form-check-input" type="checkbox"
                                                    x-model="session.highlighted_title" :id="$id('highlighted_title')">
                                                <label class="form-check-label" :for="$id('highlighted_title')">
                                                    Título em Destaque
                                                </label>
                                            </div>
                                            <div class="form-check mt-3 ms-3" x-id="['add_sidebar']">
                                                <input class="form-check-input" type="checkbox"
                                                    x-model="session.add_sidebar" :id="$id('add_sidebar')">
                                                <label class="form-check-label" :for="$id('add_sidebar')">
                                                    Adicionar à sidebar
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <template x-for="(file, fileIndex) in session.files">
                                            <div class="d-flex justify-content-between align-items-center mb-3"
                                                draggable="true" x-on:dragstart="session.dragStartIndex = fileIndex"
                                                x-on:drop="session.dragEndIndex = fileIndex; changeItemPosition(session.dragStartIndex, session.dragEndIndex, index, 'files');">
                                                <div class="row flex-grow-1 me-2">
                                                    <div class="col-sm-6">
                                                        <i class="bx bxs-sort-alt cursor-move"></i>
                                                        <label class="form-label">Nome do arquivo</label>
                                                        <input class="form-control" x-model="file.name">
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <label class="form-label">Arquivo</label>
                                                        <input class="form-control" type="file" id="formFile"
                                                            x-on:change="fileChosen($event, index, fileIndex)">
                                                        <span x-text="file.original_name"></span>
                                                    </div>
                                                </div>
                                                <i class='bx bx-trash text-danger cursor-pointer'
                                                    x-on:click="removeFileFromFiles(index, fileIndex)"></i>
                                            </div>
                                        </template>
                                    </div>
                                    <button type="button" class="btn btn-primary mt-3"
                                        x-on:click="addFileToFilesSession(index)">Adicionar arquivo</button>
                                </div>
                            </div>
                        </template>
                    </div>
                </template>
            </div>
            <div class="mt-3">
                <button type="button" class="btn btn-secondary text-white"
                    x-on:click="addContentSession">Conteúdo</button>
                <template x-if="$store.constructor.template == 'services'">
                    <button type="button" class="btn btn-secondary text-white" x-on:click="addNumeredBoxSession">Box
                        numerado</button>
                </template>
                <template x-if="$store.constructor.template == 'services' || $store.constructor.template == 'default'">
                    <button type="button" class="btn btn-secondary text-white" x-on:click="addImageBox">Box com
                        imagem</button>
                </template>
                <template x-if="$store.constructor.template == 'services'">
                    <button type="button" class="btn btn-secondary text-white" x-on:click="addFAQSession">FAQ</button>
                </template>
                <template x-if="$store.constructor.template == 'services' || $store.constructor.template == 'contracts'">
                    <button type="button" class="btn btn-secondary text-white" x-on:click="addFilesSession">Download de
                        Arquivos</button>
                </template>
            </div>
            <input type="hidden" name="sessions" x-model="JSON.stringify(sessions)">

            <button class="btn btn-primary mt-5 save-button">Salvar</button>
        </div>


    </form>

    <br><br><br><br><br><br><br>
    <br><br><br><br><br><br><br>
    <br><br><br><br><br><br><br>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

    <script nonce="{{ csp_nonce() }}">
        var dynamicPageSettings = @json($page->template_form_settings);
        if (typeof dynamicPageSettings === 'string') {
            dynamicPageSettings = JSON.parse(dynamicPageSettings);
        }

        function ShowHideTipoBitrix(selecao) {
            if (selecao == 'services')
                $('#divTipoBitrix').show();
            else
                $('#divTipoBitrix').hide();
        }

        document.addEventListener('alpine:init', () => {
            Alpine.store('constructor', {
                template: "{{ $page->template }}"
            })
        });

        function initEditor() {
            tinymce.init({
                selector: ".editor",
                theme: "silver",
                content_css: '/css/tinyTextArea-min.css',
                plugins: ['advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                    'anchor', 'searchreplace', 'visualblocks', 'fullscreen',
                    'insertdatetime', 'media', 'table', 'code', 'help', 'wordcount', 'hr'
                ],
                height: 300,
                resize: 'vertical',
                valid_elements: '*[*]',
                extended_valid_elements: 'script[language|type|src]',
                advcode_inline: true,
                tinymce_allow_script_urls: true,
                menubar: false,
                toolbar: 'undo redo | blocks | bold italic backcolor link | ' +
                    'alignleft aligncenter alignright alignjustify | ' +
                    'hr bullist numlist outdent indent | removeformat | code | help',
                language: 'pt_BR',
                setup: function(editor) {
                    editor.on('keyup change undo', function() {
                        editor.targetElm.innerHTML = editor.getContent();
                        editor.targetElm.dispatchEvent(new Event("change"));
                    });
                }
            });
            // $('.editor').tinymce({
            //   height: 300,
            //   resize: 'vertical',
            //   menubar: false,
            //   tinymce_allow_script_urls: true,
            //   valid_elements : '*[*]',
            //   extended_valid_elements: 'script[language|type|src]',
            //   plugins: [
            //     'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            //     'anchor', 'searchreplace', 'visualblocks', 'fullscreen',
            //     'insertdatetime', 'media', 'table', 'code', 'help', 'wordcount', 'hr'
            //   ],
            //   toolbar: 'undo redo | blocks | bold italic backcolor link | ' +
            //     'alignleft aligncenter alignright alignjustify | ' +
            //     'hr bullist numlist outdent indent | removeformat | code | help',
            //   setup: function(editor) {
            //     editor.on('keyup change undo', function () {
            //       editor.targetElm.innerHTML = editor.getContent();
            //       editor.targetElm.dispatchEvent(new Event("change"));
            //     });
            //   }
            // });
        }

        $(document).ready(function() {
            ShowHideTipoBitrix($('#template').val());
            initEditor();
            CarregaTiposAtos();
            CarregaFinalidades();
        });

        function formComponent(initialForm = null) {
            return {
                form: {
                    title: '',
                    model: '',
                    show: true,
                    recipients: '',
                    email_template: '',
                    settings: {},
                },
                init() {
                    if (initialForm != null)
                        this.form = JSON.parse(initialForm);
                },
                modelSelected() {
                    var settings = {};

                    switch (this.form.model) {
                        case 'a':
                            settings = {
                                select_title: '',
                                select_options: ''
                            }
                            break;

                        default:
                            break;
                    }

                    this.form.settings = settings;
                }
            }
        };

        function builderComponent(questionCategories, initialSessions = []) {
            return {
                sessions: initialSessions,
                questionCategories,
                dragStartIndex: 0,
                dragEndIndex: 0,
                indexHover: null,
                btnCross: null,
                changePosition(a, b) {
                    [this.sessions[a], this.sessions[b]] = [this.sessions[b], this.sessions[a]];

                    if (this.sessions[a].type == 'content' && this.sessions[b].type == 'content') {
                        $('#sessionsContainer').children('.session').each((i, session) => {
                            var editor = tinymce.get($(session).find('.editor').attr('id'));

                            if (i == a)
                                editor.setContent(this.sessions[a].content);

                            if (i == b)
                                editor.setContent(this.sessions[b].content);
                        });
                    } else {
                        this.rebootEditors();
                    }
                },
                editorContentChanges(e, index) {
                    this.sessions[index].content = e.srcElement.innerHTML;
                },
                init() {
                    if (initialSessions.length > 0)
                        this.sessions = JSON.parse(initialSessions);
                },
                addContentSession() {
                    var session = {
                        type: 'content',
                        title: '',
                        red_title: false,
                        highlighted_title: false,
                        content_block: false,
                        add_sidebar: false,
                        content: ''
                    };

                    this.sessions.push(session);

                    setTimeout(() => {
                        initEditor();
                    }, 1);
                },
                addNumeredBoxSession() {
                    var session = {
                        type: 'numered_box',
                        title: '',
                        red_title: false,
                        highlighted_title: false,
                        add_sidebar: false,
                        texts: [],
                        dragStartIndex: null,
                        dragEndIndex: null
                    };
                    this.sessions.push(session);
                },
                changeItemPosition(a, b, index, type = "") {
                    if (type == "numered_box")
                        [this.sessions[index].texts[a], this.sessions[index].texts[b]] = [this.sessions[index].texts[b],
                            this.sessions[index].texts[a]
                        ];

                    if (type == "faq")
                        [this.sessions[index].questions[a], this.sessions[index].questions[b]] = [this.sessions[index]
                            .questions[b], this.sessions[index].questions[a]
                        ];

                    if (type == "files")
                        [this.sessions[index].files[a], this.sessions[index].files[b]] = [this.sessions[index].files[b],
                            this.sessions[index].files[a]
                        ];

                    if (type == "image_box")
                        [this.sessions[index].blocks[a], this.sessions[index].blocks[b]] = [this.sessions[index].blocks[b],
                            this.sessions[index].blocks[a]
                        ];
                },
                addTextToNumeredBox(index) {
                    this.sessions[index].texts.push({
                        title: ''
                    })
                },
                removeTextFromNumeredBox(sessionIndex, textIndex) {
                    this.sessions[sessionIndex].texts.splice(textIndex, 1);
                },

                addFAQSession() {
                    var session = {
                        type: 'faq',
                        title: '',
                        red_title: false,
                        highlighted_title: false,
                        add_sidebar: false,
                        questions: [],
                        question_selector: {
                            'category_id': 0,
                            'question': []
                        }
                    };
                    this.sessions.push(session);
                },
                addQuestionToFAQ(sessionIndex) {
                    var session = this.sessions[sessionIndex];

                    if (session.question_selector.question.length > 0)
                        session.questions.push(JSON.parse(session.question_selector.question));
                },
                removeQuestionFromFAQ(sessionIndex, questionIndex) {
                    this.sessions[sessionIndex].questions.splice(questionIndex, 1);
                },

                addImageBox() {
                    var session = {
                        type: 'image_box',
                        blocks: [{
                            title: '',
                            link: '',
                            text_link: '',
                            desc: '',
                            original_name: '',
                            file_name: ''
                        }],
                    };
                    this.sessions.push(session);
                },
                addBlocksToImageBoxSession(index) {
                    this.sessions[index].blocks.push({
                        title: '',
                        link: '',
                        text_link: '',
                        desc: '',
                        original_name: '',
                        file_name: ''
                    })
                },
                imageBlockChosen(event, sessionIndex, fileIndex) {
                    const data = new FormData();
                    data.append('file', event.target.files[0]);
                    data.append('path', 'public');

                    axios.interceptors.request.use((config) => {
                        $('.save-button').attr('disabled', true);
                        $('.save-button').html('Salvando arquivos, aguarde.');
                        return config;
                    });


                    axios.post("{{ route('pages.templates.file.upload') }}", data)
                        .then((r) => {
                            const file = this.sessions[sessionIndex].blocks[fileIndex];
                            file.file_name = r.data.name;
                            file.original_name = event.target.files[0].name;
                            $('.save-button').attr('disabled', false);
                            $('.save-button').html('Salvar');
                        });

                    console.log(this.sessions);
                },
                removeBlockFromImageBox(sessionIndex, blockIndex) {
                    this.sessions[sessionIndex].blocks.splice(blockIndex, 1);
                },

                addFilesSession() {
                    var session = {
                        type: 'files',
                        title: '',
                        red_title: false,
                        highlighted_title: false,
                        add_sidebar: false,
                        files: [{
                            name: '',
                            original_name: '',
                            file_name: ''
                        }],
                    };
                    this.sessions.push(session);
                },
                addFileToFilesSession(index) {
                    this.sessions[index].files.push({
                        name: '',
                        original_name: '',
                        file_name: ''
                    })
                },
                fileChosen(event, sessionIndex, fileIndex) {
                    const data = new FormData();
                    data.append('file', event.target.files[0]);

                    axios.interceptors.request.use((config) => {
                        $('.save-button').attr('disabled', true);
                        $('.save-button').html('Salvando arquivos, aguarde.');
                        return config;
                    });

                    axios.post("{{ route('pages.templates.file.upload') }}", data)
                        .then((r) => {
                            const file = this.sessions[sessionIndex].files[fileIndex];
                            file.file_name = r.data.name;
                            file.original_name = event.target.files[0].name;
                            $('.save-button').attr('disabled', false);
                            $('.save-button').html('Salvar');
                        });
                },
                removeFileFromFiles(sessionIndex, fileIndex) {
                    this.sessions[sessionIndex].files.splice(fileIndex, 1);
                },

                removeSession(index, session = 'any') {

                    if (!confirm('Tem certeza que deseja excluir essa sessão?')) return;

                    this.sessions.splice(index, 1);
                    this.rebootEditors()
                },
                rebootEditors() {
                    $('#sessionsContainer').find('.editor').each((index, editorContainer) => {
                        var editor = tinymce.get(editorContainer.id);
                        if (editor)
                            editor.destroy();
                    });

                    setTimeout(() => {
                        initEditor();
                    }, 1);
                }
            }
        }

        function AdicionarItemTipoAto() {
            var tipoAto = document.getElementById('txtNovoTipo').value;
            var chkExibirAnalise = document.getElementById('chkExibirAnalise').checked ? 1 : 0;
            var chkUploadObrigatorio = document.getElementById('chkUploadObrigatorio').checked ? 1 : 0;

            if (tipoAto.trim() === '') {
                alert('Por favor, insira um tipo de ato.');
                document.getElementById('txtNovoTipo').focus();
                return;
            }

            $("#tiposAtos").append(
                $("<option></option>")
                .attr("value", tipoAto)
                .text(tipoAto + (chkExibirAnalise ? ' (Análise)' : '') + (chkUploadObrigatorio ? ' - (Upload Obrigatório)' : ''))
            );
            document.getElementById('txtNovoTipo').value = '';
            document.getElementById('chkExibirAnalise').checked = false;
            document.getElementById('chkUploadObrigatorio').checked = false;

            $("#tiposAtosHidden").val(($("#tiposAtosHidden").val() + "|" + tipoAto + (chkExibirAnalise ? '@1' : '@0') + (chkUploadObrigatorio ? '@1' : '@0'))
                .replace('||', '|'));
            $("#mensagemSucesso").html('Tipo adicionado na lista. Para gravar é necessário clicar em Salvar!').show('fast')
                .delay(5000).hide('slow');

            OrdenaTiposAtos();

        }

        function AdicionarItemFinalidade() {
            var finalidade = document.getElementById('txtNovaFinalidade').value;

            if (finalidade.trim() === '') {
                alert('Por favor, insira uma finalidade.');
                document.getElementById('txtNovaFinalidade').focus();
                return;
            }

            $("#finalidades").append(
                $("<option></option>")
                .attr("value", finalidade)
                .text(finalidade)
            );

            document.getElementById('txtNovaFinalidade').value = '';

            $("#finalidadesHidden").val(($("#finalidadesHidden").val() + "|" + finalidade)
                .replace('||', '|'));
            $("#mensagemSucessoFinalidade").html(
                    'Finalidade adicionada na lista. Para gravar é necessário clicar em Salvar!').show('fast')
                .delay(5000).hide('slow');

            OrdenaFinalidades();

        }

        function CarregaTiposAtos() {
            var tiposAtos = dynamicPageSettings.tiposAtos || [];
            var select = document.getElementById('tiposAtos');

            // Limpa as opções existentes
            select.innerHTML = '';

            // Adiciona a opção padrão
            var option = document.createElement('option');
            option.value = '';
            option.textContent = 'Selecione o tipo';
            select.appendChild(option);

            // Adiciona as opções dos tipos de atos
            tiposAtos.forEach(function(tipoAto) {
                console.log(tipoAto);
                var option = document.createElement('option');
                option.value = tipoAto.name;
                option.textContent = tipoAto.name + (tipoAto.exibir_analise ? ' (Análise)' : '') + (tipoAto.upload_obrigatorio ? ' - (Upload Obrigatório)' : '');
                select.appendChild(option);
                $("#tiposAtosHidden").val(($("#tiposAtosHidden").val() + "|" + tipoAto.name + (tipoAto
                        .exibir_analise ? '@1' : '@0') + (tipoAto.upload_obrigatorio ? '@1' : '@0'))
                    .replace('||', '|'));
            });

            OrdenaTiposAtos();
        }

        function CarregaFinalidades() {
            var finalidades = dynamicPageSettings.finalidades || [];
            var select = document.getElementById('finalidades');

            // Limpa as opções existentes
            select.innerHTML = '';

            // Adiciona a opção padrão
            var option = document.createElement('option');
            option.value = '';
            option.textContent = 'Selecione a finalidade';
            select.appendChild(option);

            // Adiciona as opções dos tipos de atos
            finalidades.forEach(function(finalidade) {
                var option = document.createElement('option');
                option.value = finalidade.name;
                option.textContent = finalidade.name;
                select.appendChild(option);
                $("#finalidadesHidden").val(($("#finalidadesHidden").val() + "|" + finalidade.name)
                    .replace('||', '|'));
            });

            OrdenaFinalidades();
        }

        function ExcluirItemTipoAto() {
            var select = document.getElementById('tiposAtos');
            var selectedOption = select.options[select.selectedIndex];

            if (selectedOption.value === '') {
                alert('Por favor, selecione um tipo de ato para excluir.');
                return;
            }

            // Remove a opção selecionada

            // Atualiza o campo oculto
            var tiposAtosHidden = $("#tiposAtosHidden").val().split('|').filter(function(item) {
                return item.split("@")[0] !== selectedOption.value && item !== '';
            });
            $("#tiposAtosHidden").val(tiposAtosHidden.join('|'));

            select.remove(select.selectedIndex);

            $('#btnExcluirTipoAto').hide();

            $("#mensagemSucesso").html('Tipo removido da lista. Para gravar é necessário clicar em Salvar!').show('fast')
                .delay(5000).hide('slow');
        }

        function ExcluirItemFinalidade() {
            var select = document.getElementById('finalidades');
            var selectedOption = select.options[select.selectedIndex];

            if (selectedOption.value === '') {
                alert('Por favor, selecione uma finalidade para excluir.');
                return;
            }

            // Remove a opção selecionada

            // Atualiza o campo oculto
            var finalidadesHidden = $("#finalidadesHidden").val().split('|').filter(function(item) {
                return item !== selectedOption.value && item !== '';
            });

            $("#finalidadesHidden").val(finalidadesHidden.join('|'));

            select.remove(select.selectedIndex);

            $('#btnExcluirFinalidade').hide();

            $("#mensagemSucessoFinalidade").html('Finalidade removida da lista. Para gravar é necessário clicar em Salvar!')
                .show('fast')
                .delay(5000).hide('slow');
        }

        function OrdenaTiposAtos() {
            var select = document.getElementById('tiposAtos');
            var options = Array.from(select.options)
                .filter(option => option.value !== ""); // ignora o "Selecione o tipo"

            // Ordena alfabeticamente pelo texto
            options.sort(function(a, b) {
                return a.text.localeCompare(b.text, 'pt-BR', {
                    sensitivity: 'base'
                });
            });

            // Remove todas as opções, exceto a primeira (Selecione o tipo)
            select.innerHTML = '';
            var defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = 'Selecione o tipo';
            select.appendChild(defaultOption);

            // Adiciona as opções ordenadas
            options.forEach(function(option) {
                select.appendChild(option);
            });
        }

        function OrdenaFinalidades() {
            var select = document.getElementById('finalidades');
            var options = Array.from(select.options)
                .filter(option => option.value !== ""); // ignora o "Selecione o tipo"

            // Ordena alfabeticamente pelo texto
            options.sort(function(a, b) {
                return a.text.localeCompare(b.text, 'pt-BR', {
                    sensitivity: 'base'
                });
            });

            // Remove todas as opções, exceto a primeira (Selecione o tipo)
            select.innerHTML = '';
            var defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = 'Selecione a finalidade';
            select.appendChild(defaultOption);

            // Adiciona as opções ordenadas
            options.forEach(function(option) {
                select.appendChild(option);
            });
        }

        function ValidaTipoAto(value) {
            if (value === '') {
                $('#btnExcluirTipoAto').hide();
            } else {
                $('#btnExcluirTipoAto').show();
            }
        }

        function ValidaFinalidade(value) {
            if (value === '') {
                $('#btnExcluirFinalidade').hide();
            } else {
                $('#btnExcluirFinalidade').show();
            }
        }
    </script>
@endsection
