@extends('layouts.my-account')

@section('content')
    <section class="account txtcontent">

        <div class="container-fluid">

            <div class="row">

                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">

                    <h1>Meus Serviços</h1>
                    <ul class="menuConta">
                        @php
                            $i = 0;
                        @endphp
                        @foreach ($settings->block1 as $thePage)
                            @php
                                $css = '';
                                $target = '_self';
                                if (isset($settings->{'color1' . $i})) {
                                    $css = 'color-' . $settings->{'color1' . $i};
                                }
                                if (isset($settings->{'bold1' . $i}) && $settings->{'bold1' . $i} == 'on') {
                                    $css .= ' strong';
                                }
                                if (isset($settings->{'blank1' . $i}) && $settings->{'blank1' . $i} == 'on') {
                                    $target = '_blank';
                                }
                                $i++;
                            @endphp
                            @if ($thePage->type == 'dynamic' || $thePage->template == 'default')
                                <li><a href='{{ url("/{$thePage->slug}") }}' class="{{ $css }}"
                                        target="{{ $target }}">{{ $thePage->title }}</a></li>
                            @elseif ($thePage->type == 'coded')
                                <li><a href='{{ url("/{$thePage->coded_endpoint}") }}' class="{{ $css }}"
                                        target="{{ $target }}">{{ $thePage->title }}</a></li>
                            @else
                                <li><a href='{{ url("/" . __('messages.slug.' . $thePage->template) . "/" . $thePage->slug) }}' class="{{ $css }}"
                                            target="{{ $target }}">{{ $thePage->title }}</a></li>
                            @endif

                        @endforeach
                    </ul>
                    <div class="notificacao" style="position: relative;">
                        <h3 style="font-size:22px;">Push26 - Informações do registro imobiliário por e-mail</h3>
                        <p>Cadastre seu e-mail e receba todas as atualizações dos seus atos.</p>
                        <div class="d-flex">
                            <a href="{{ route('queries.notarial.act') }}" class="red">Cadastrar agora</a>
                            &nbsp;&nbsp;&nbsp;
                            <a href="{{ route('push26.show') }}" class="red">Visualizar Registros</a>
                        </div>
                    </div>

                </div>

                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">

                    <div class="contentServicesAccount">

                        <div class="item">

                            <div class="d-flex justify-content-between align-items-center">
                                <h2 class="red">Meu 26º</h2>
                                <a href="{{ route('my.acc') }}">Configurar</a>
                            </div>

                        </div>

                        @foreach ($occupations as $occupation)
                            @if (count($occupation->my_clerks) > 0)
                                <div class="item">
                                    <h2>{{ $occupation->name }}</h2>
                                    @foreach ($occupation->my_clerks as $clerk)
                                        <div class="d-flex justify-content-between align-items-center">
                                            <p>{{ $clerk->name }}<br>{{ $clerk->email }}<br>{{ $clerk->phone }}</p>
                                            <form action="{{ route('my.clerks.remove', [$clerk->id]) }}" method="POST"
                                                class="d-flex align-items-end" style="align-items: flex-end;">
                                                @csrf
                                                @method('DELETE')
                                                <button href="" type="submit"
                                                    onclick="return confirm('Tem certeza que deseja excluir esse escrevente?')"
                                                    class="btnAsLink">Excluir</button>
                                            </form>
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        @endforeach
                    </div>

                </div>

            </div>

        </div>

    </section>
@endsection
