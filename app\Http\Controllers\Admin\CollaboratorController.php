<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Collaborator;
use App\Models\CollaboratorOccupation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;

class CollaboratorController extends Controller
{
  public function collaborators()
  {
    $collaborators = Collaborator::orderBy('name')->get();
    return view('admin.collaborators.collaborators', [
      'collaborators' => $collaborators
    ]);
  }

  public function collaboratorsCreate()
  {
    $occupations = CollaboratorOccupation::all();
    $clerks = Collaborator::orderBy('name')->get();
    return view('admin.collaborators.collaborators-create', [
      'occupations' => $occupations,
      'clerks' => $clerks
    ]);
  }

  public function collaboratorsStore(Request $r)
  {
    $validator = Validator::make($r->all(), [
      'name' => 'required',
      'email' => 'required|unique:collaborators',
      'phone' => 'required',
      'password' => 'required_if:sys_access,on',
      'type' => 'required'
    ]);

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }

    $collaborator = new Collaborator;
    $collaborator->name = $r->name;
    $collaborator->email = $r->email;
    $collaborator->phone = $r->phone;
    $collaborator->password = $r->password != '' ? Hash::make($r->password) : null;
    $collaborator->type = $r->type;

    $permissions = $r->permissions ?? [];
    $collaborator->permissions = json_encode(array_keys($permissions));

    $settings = $r->settings ?? [];
    $collaborator->settings = json_encode($settings);

    if (Auth::guard('admin')->user()->cannot('manage_this_user', $collaborator)) {
      return redirect()->route('collaborators.collaborators');
    }

    $collaborator->save();

    $occupations = $r->occupations ?? [];
    $collaborator->occupations()->attach(array_values($occupations));

    return redirect()->route('collaborators.collaborators')->with('success', 'Colaborador criado com sucesso!');
  }

  public function collaboratorsEdit($id)
  {
    $occupations = CollaboratorOccupation::all();
    $clerks = Collaborator::orderBy('name')->get();


    $collaborator = Collaborator::find($id);
    $collaborator['occupationsIds'] = $collaborator->occupations->pluck('pivot.occupation_id')->toArray();

    if (Auth::guard('admin')->user()->cannot('manage_this_user', $collaborator)) {
      return redirect()->back();
    }

    return view('admin.collaborators.collaborators-edit', [
      'occupations' => $occupations,
      'collaborator' => $collaborator,
      'permissions' => json_decode($collaborator->permissions),
      'settings' => json_decode($collaborator->settings, true),
      'clerks' => $clerks
    ]);
  }

  public function collaboratorsUpdate(Request $r, $id)
  {
    $validator = Validator::make($r->all(), [
      'name' => 'required',
      'email' => [
        'required',
        Rule::unique('collaborators')->ignore($id),
      ],
      'phone' => [
        'required'
      ],
      'password' => 'required_if:sys_access,on',
      'type' => 'required'
    ]);

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }
    
    $collaborator = Collaborator::find($id);

    if (Auth::guard('admin')->user()->cannot('manage_this_user', $collaborator)) {
      return redirect()->back();
    }

    if ($r->sys_access == 'on') {
      if ($r->password != $collaborator->password) {
        $collaborator->password = Hash::make($r->password);
      }
    } else {
      $collaborator->password = null;
    }

    $collaborator->name = $r->name;
    $collaborator->email = $r->email;
    $collaborator->phone = $r->phone;
    $collaborator->type = $r->type;

    $permissions = $r->permissions ?? [];
    $collaborator->permissions = json_encode(array_keys($permissions));

    $settings = $r->settings ?? [];
    $collaborator->settings = json_encode($settings);

    $collaborator->save();

    $occupations = $r->occupations ?? [];
    $collaborator->occupations()->sync(array_values($occupations));

    return redirect()->route('collaborators.collaborators')->with('success', 'Colaborador editado com sucesso!');
  }

  public function collaboratorsDestroy($id)
  {
    $collaborator = Collaborator::find($id);

    if (Auth::guard('admin')->user()->cannot('manage_this_user', $collaborator)) {
      return redirect()->back();
    }

    $collaborator->occupations()->detach();
    $collaborator->delete();
    return redirect()->route('collaborators.collaborators')->with('success', 'Colaborador excluído com sucesso!');
  }

  public function collaboratorsProfile()
  {
    return view('admin.collaborators.profile', [
      'user' => Auth::guard('admin')->user()
    ]);
  }

  public function collaboratorsProfileUpdate(Request $r)
  {
    if (!empty($r->password)) {
      $collaborator = Collaborator::find(Auth::guard('admin')->user()->id);
      $collaborator->password = Hash::make($r->password);
      $collaborator->save();

      return redirect()->route('collaborator.profile')->with('success', 'Senha alterada com sucesso!');
    }

    return redirect()->route('collaborator.profile');
  }

  public function occupations()
  {
    $occupations = CollaboratorOccupation::orderBy('name')->get();
    return view('admin.collaborators.occupations', [
      'occupations' => $occupations
    ]);
  }

  public function occupationsStore(Request $r)
  {
    $validator = Validator::make($r->all(), [
      'name' => 'required',
    ]);

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }
    
    $occupation = new CollaboratorOccupation;
    $occupation->name = $r->name;
    $occupation->save();

    return redirect()->back()->with('success', 'Area de atuação criada com sucesso!');
  }

  public function occupationsEdit($id)
  {
    $occupation = CollaboratorOccupation::find($id);
    return view('admin.collaborators.occupations-edit', [
      'occupation' => $occupation,
    ]);
  }

  public function occupationsUpdate(Request $r, $id)
  {
    $validator = Validator::make($r->all(), [
      'name' => 'required',
    ]);

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }
    
    $occupation = CollaboratorOccupation::find($id);
    $occupation->name = $r->name;
    $occupation->save();
    return redirect()->route('collaborators.occupations')->with('success', 'Colaborador editado com sucesso!');
  }

  public function occupationsDestroy($id)
  {
    CollaboratorOccupation::destroy($id);
    return redirect()->route('collaborators.occupations')->with('success', 'Colaborador excluído com sucesso!');
  }
}
