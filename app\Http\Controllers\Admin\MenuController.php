<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Menu;
use App\Models\Submenu;
use App\Models\SubmenuLinks;
use App\Models\Page;

class MenuController extends Controller
{
  public function header()
  {
    $menus = Menu::where('position', 'header')->orderBy('index')->get();
    return view('admin.menus.header.index', [
      'menus' => $menus
    ]);
  }

  public function createHeader()
  {
    return view('admin.menus.header.create');
  }

  public function storeHeader(Request $r)
  {
    $rules = [
      'title' => 'required',
      'type' => 'required',
      'slug' => 'required_if:type,slug|unique:menus,slug|regex:/^[a-z0-9]+(\-{1}[a-z0-9]+)*$/',
      'link' => 'required_if:type,link|regex:/^(?:http(s)?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:\?#[\]@!\/$&\'\(\)\*\+,;=.]+$/',
      'submenus.*.title' => 'sometimes|required',
      'submenus.*.slug' => 'sometimes|required|regex:/^[a-z0-9]+(\-{1}[a-z0-9]+)*$/',
    ];

    $validator = Validator::make($r->all(), $rules);

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }

    $nextIndex = Menu::where('position', 'header')->orderBy('index', 'desc')->first();
    $nextIndex = (isset($nextIndex->index)) ? $nextIndex->index + 1 : 0;

    $menu = new Menu;
    $menu->title = $r->title;
    $menu->type = $r->type;
    $menu->slug = $r->slug;
    $menu->link = $r->link;
    $menu->position = 'header';
    $menu->index = $nextIndex;
    $menu->save();

    if (isset($r->submenus)) {
      $index = 0;
      foreach ($r->submenus as $submenu) {
        $newSubmenu = new Submenu;
        $newSubmenu->title = $submenu['title'];
        $newSubmenu->slug = $submenu['slug'];
        $newSubmenu->index = $index;
        $newSubmenu->menu_id = $menu->id;
        $newSubmenu->save();

        $index++;
      }
    }

    return redirect()->route('menus.header');
  }

  public function footer()
  {
    $menus = Menu::where('position', 'footer')->orderBy('index')->get();
    return view('admin.menus.footer', [
      'menus' => $menus
    ]);
  }


  public function createFooter()
  {
    $pages = Page::all();
    return view('admin.menus.footer-create', [
      'pages' => $pages
    ]);
  }

  public function storeFooter(Request $r)
  {
    $rules = [
      'title' => 'required',
      'type' => 'required',
    ];

    $validator = Validator::make($r->all(), $rules);

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }

    $nextIndex = Menu::where('position', 'footer')->orderBy('index', 'desc')->first();
    $nextIndex = (isset($nextIndex->index)) ? $nextIndex->index + 1 : 0;

    $menu = new Menu;
    $menu->title = $r->title;
    $menu->page_id = $r->page;
    $menu->link = $r->link;
    $menu->position = 'footer';
    $menu->type = $r->type;
    $menu->index = $nextIndex;
    $menu->save();

    return redirect()->route('menus.footer')->with('success', 'Item do menu criado com sucesso!');
  }

  public function editFooter($id)
  {
    $menu = Menu::find($id);
    $pages = Page::all();

    return view('admin.menus.footer-edit', [
      'menu' => $menu,
      'pages' => $pages
    ]);
  }

  public function updateFooter(Request $r, $id)
  {
    $rules = [
      'title' => 'required',
      'type' => 'required',
    ];

    $validator = Validator::make($r->all(), $rules);

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }

    $menu = Menu::find($id);
    $menu->title = $r->title;
    $menu->page_id = $r->page;
    $menu->link = $r->link;
    $menu->position = 'footer';
    $menu->type = $r->type;
    $menu->save();

    return redirect()->route('menus.footer')->with('success', 'Item do menu editado com sucesso!');
  }

  public function destroyFooter($id)
  {
    Menu::where('id', $id)->delete();
    return redirect()->route('menus.footer');
  }

  public function updateMenuOrder(Request $r)
  {
    $orderIds = implode(',', $r->ids);
    $menus = Menu::whereIn('id', $r->ids)->orderByRaw("FIELD(id, $orderIds)")->get();

    foreach ($menus as $newIndex => $menu) {
      $menu->index = $newIndex;
      $menu->save();
    }
  }

  public function links(Request $r)
  {
    $menus = Menu::where('position', 'header')->get();
    $pages = Page::all();

    $links = [];
    if ($r->submenu) {
      $links = SubmenuLinks::where('submenu_id', $r->submenu)->orderBy('index')->get();
    }

    return view('admin.menus.links', [
      'menus' => $menus,
      'pages' => $pages,
      'selectedMenu' => $r->menu ?? null,
      'selectedSubmenu' => $r->submenu ?? null,
      'links' => $links
    ]);
  }

  public function linksStore(Request $r)
  {
    $rules = [
      'submenu' => 'required',
      'links.*.title' => 'sometimes|required',
      'links.*.link' => 'sometimes|required|regex:/^(?:http(s)?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:\?#[\]@!\/$&\'\(\)\*\+,;=.]+$/',
      'links.*.page_id' => 'sometimes|required',
      'links.*.blank' => 'sometimes|required',
    ];

    $validator = Validator::make($r->all(), $rules);

    SubmenuLinks::where('submenu_id', $r->submenu)->delete();

    if (isset($r->links)) {
      $index = 0;
      foreach ($r->links as $link) {
        $newLink = new SubmenuLinks;
        $newLink->title = $link['title'] ?? null;
        $newLink->type = $link['type'];
        $newLink->blank = isset($link['blank']) ? 1 : 0;
        $newLink->bold = isset($link['bold']) ? 1 : 0;
        $newLink->page_id = $link['page_id'] ?? null;
        $newLink->link = $link['link'] ?? null;
        $newLink->submenu_id = $r->submenu;
        $newLink->index = $index;
        $newLink->color = $link['color'] ?? null;
        $newLink->save();

        $index++;
      }
    }

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }



    return redirect()->back()->with('success', 'Links salvos com sucesso!');
  }

  public function showHeader($id)
  {
    $menu = Menu::find($id);
    return view('admin.menus.header.show', [
      'menu' => $menu
    ]);
  }

  public function updateHeader(Request $r, $id)
  {
    $rules = [
      'title' => 'required',
      'type' => 'required',
      'slug' => 'required_if:type,slug|regex:/^[a-z0-9]+(\-{1}[a-z0-9]+)*$/',
      'link' => 'required_if:type,link|regex:/^(?:http(s)?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:\?#[\]@!\/$&\'\(\)\*\+,;=.]+$/',
      'submenus.*.title' => 'sometimes|required',
      'submenus.*.slug' => 'sometimes|required|regex:/^[a-z0-9]+(\-{1}[a-z0-9]+)*$/',
    ];
    $validator = Validator::make($r->all(), $rules);

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }

    $menu = Menu::find($id);
    $menu->title = $r->title;
    $menu->type = $r->type;
    $menu->slug = $r->slug;
    $menu->link = $r->link;
    $menu->save();

    Submenu::destroy(json_decode($r->removed_submenus));

    if (isset($r->submenus)) {
      $index = 0;
      foreach ($r->submenus as $submenu) {
        $newSubmenu = Submenu::find($submenu['id']);
        $newSubmenu = ($newSubmenu) ?: new Submenu; // ....

        $newSubmenu->title = $submenu['title'];
        $newSubmenu->slug = $submenu['slug'];
        $newSubmenu->index = $index;
        $newSubmenu->menu_id = $menu->id;
        $newSubmenu->save();

        $index++;
      }
    }

    return redirect()->back()->with('success', 'Cabeçalho editado com sucesso!');
  }

  public function destroyHeader($id) {
    Menu::destroy($id);
    return redirect()->back()->with('success', 'Menu excluído com sucesso!');
  }

}
