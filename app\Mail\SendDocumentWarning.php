<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Mail\Mailables\Address;
use App\Models\Document;

class SendDocumentWarning extends Mailable
{
    use Queueable, SerializesModels;

    public $doc;
    public $subject;
    public $clientName;
    public $clerkName;
    public $view;
    public $content;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Document $doc, $to, $subjectType = 'new')
    {
      $this->doc = $doc;
      $this->clientName = explode(" ", $doc->client->name)[0];
      $this->clerkName = explode(" ", $doc->clerk->name)[0];

      if ($to == 'client') {
        $this->view = 'emails.send-document-warning-user';
        $this->subject = $this->clientName . ' documentos enviados com sucesso';
        $this->content = 'Documentos enviados - ';
      } else {
        $this->content = 'Documentos recebidos - ';
        $this->view = 'emails.send-document-warning';
        if ($subjectType == 'new') {
          $this->subject = 'Um novo envio de documentos de ' . $this->clientName . ' recebido';
        } else {
          $this->subject = 'Envio de documentos de ' . $this->clientName . ' atualizado';
        }
      }
      

      if ($doc->status == 'opened') {
        $this->content .= 'Ticket ' . $doc->id . ' - Em aberto (aguardando complemento de documentos).';
      } else {
        $this->content .= 'Ticket ' . $doc->id . ' - Fechado (documentação completa enviada).';
      }
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
      return new Envelope(
        from: new Address(env('MAIL_FROM_ADDRESS'), env('MAIL_FROM_NAME')),
        subject: $this->subject,
      );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(
            view: $this->view,
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}
