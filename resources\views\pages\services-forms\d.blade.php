<div id="form-anchor"></div>
<form id="d" class="includePage" action="{{ route('forms.send') . '#anchor' }}" method="POST"
    enctype="multipart/form-data" style="position: relative; @if (session('form_success')) display:none; @endif">
    @csrf
    <div class="hidden">
        <input type="hidden" name="form" value="d">
        <input type="hidden" name="título" value="{{ $page->title }}">
        <input type="hidden" name="Número do Pedido" value="">
        <input type="hidden" name="recipients" value="{{ $form->recipients }}">
        <input type="hidden" value="{{ number_format($certificateSettings->value, 2, '.', ',') }}" name="Valor"
            id="Valor">
        <input type="hidden" value="{{ old('Taxa_de_entrega') }}" name="Taxa de entrega" id="delivery-fee">
        <input type="hidden" value="{{ old('Opção_digital') }}" name="Opção digital" id="Opção_digital">
        <input type="hidden" value="{{ $page->title }}" name="page_title">
        <input type="hidden" name="email_template" value="{{ $form->email_template }}">
        <input type="hidden" name="service" value="{{ $page->title }}">
        <input type="hidden" name="bitrix" value="{{ $form->tipo_bitrix ?? '' }}">
    </div>
    <div id="dadosAtoContainer">
        <div class="dados-ato-bloco">
            <input type="hidden" name="hdnIndice[]" id="hdnIndice" value="1" />
            <fieldset class="formFields">
                <h3>Dados do Ato</h3>
                <div class="formControl">
                    <label>Tipo do ato</label>
                    @php
                        $tiposAtosOrdenados = collect($form->tiposAtos)->sortBy('name');
                    @endphp
                    <select onchange="actTypeSelected(this)" required name="Tipo[]">
                        <option value="" @selected(old('Tipo') == '')>Selecione...</option>
                        @foreach ($tiposAtosOrdenados as $tipoAto)
                            <option
                                value="{{ $tipoAto->name }}|{{ $tipoAto->exibir_analise ? 1 : 0 }}|{{ $tipoAto->upload_obrigatorio ? 1 : 0 }}"
                                @selected(old('Tipo') != null && explode('|', old('Tipo'))[0] == $tipoAto->name)>
                                {{ $tipoAto->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="formControl">
                    <label>Informe a finalidade da solicitação</label>
                    @php
                        $finalidadesOrdenados = collect($form->finalidades)->sortBy('name');
                    @endphp
                    <select required name="Finalidade da solicitação[]">
                        <option value="" @selected(old('Finalidade da solicitação') == '')>Selecione...</option>
                        @foreach ($finalidadesOrdenados as $finalidade)
                            <option value="{{ $finalidade->name }}" @selected(old('Finalidade da solicitação') == $finalidade->name)>
                                {{ $finalidade->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                @if (old('Tipo') == 'Documento arquivado')
                    <div class="formControl" id="actTypeTextAreaContainer">
                        <label>Informe os dados do documento</label>
                        <textarea type="text" rows="5" name="Dados do documento[]" required>{{ old('Dados_do_documento') }}</textarea>
                    </div>
                @elseif (old('Tipo') == 'Busca')
                    <div class="formControl" id="actTypeTextAreaContainer">
                        <label>Informe os dados para a busca</label>
                        <textarea type="text" rows="5" name="Dados para busca[]" required>{{ old('Dados_para_busca') }}</textarea>
                    </div>
                @else
                    <div class="formControl" id="actTypeTextAreaContainer" style="display: none;">
                        <label></label>
                        <textarea type="text" rows="5"></textarea>
                    </div>
                @endif

                <div class="formControl" id="divAnalise" style="display: none;">
                    <label>{{ $form->texto_auxilio_analise ?? "" }}</label>
                    <div class="fileInputsContainer">
                        <div class="input-group mb-2 file-input-group">
                            <input type="file" name="arquivos_1[]" class="form-control" />
                        </div>
                    </div>
                    <div class="col-md-12" style="padding-left: 0px;">
                        <div class="formControl d-flex gap-2 align-items-center mt-2 file-actions">
                            <button type="button" class="btnDefault" onclick="addFileInput(this)">Adicionar outro
                                arquivo</button>&nbsp;
                            <button type="button" class="btnDefault red btnRemoverArquivo"
                                onclick="removeLastFileInput(this)" style="display: none;">Remover</button>
                        </div>
                    </div>
                </div>

                <div class="formControl">
                    <label>Nomes das partes do ato</label>
                    <textarea type="text" name="Partes[]" required rows="5">{{ old('Partes', request()->partes) }}</textarea>
                </div>
                <div class="formControl">
                    <label>Livro <small>(exemplo: 4949)</small></label>
                    <input type="text" name="Livro[]" id="livro" value="{{ old('Livro', request()->livro) }}"
                        pattern="\d{1,4}" maxlength="4" inputmode="numeric"
                        oninput="this.value = this.value.replace(/\D/g, '').slice(0, 4);"
                        @if (old('Tipo') != 'Busca' && old('Tipo') != 'Documento arquivado') required @endif>
                </div>
                <div class="formControl">
                    <label>Folha <small>(exemplo: 133 - indicada na primeira página do ato notarial)</small></label>
                    <input type="text" name="Folha[]" id="folha"
                        value="{{ old('Folha', request()->folha) }}" pattern="\d{1,3}" maxlength="3"
                        inputmode="numeric" oninput="this.value = this.value.replace(/\D/g, '').slice(0, 3);"
                        @if (old('Tipo') != 'Busca' && old('Tipo') != 'Documento arquivado') required @endif>
                </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12" style="padding-left: 0px;">
            <div class="formControl d-flex gap-2 align-items-center">
                <button type="button" class="btnDefault" onclick="adicionarPedidoCertidao()">+ pedido de
                    certidão</button>&nbsp;
                <button type="button" class="btnDefault red" id="btnRemoverAto"
                    onclick="removerUltimoPedidoCertidao()" style="display: none;">- pedido de certidão</button>
            </div>
        </div>
    </div>
    <h3 style="margin-top: 32px;">Dados do Solicitante</h3>
    <div class="formControl">
        <label>Tipo do Solicitante</label>
        <select onchange="solicitanteSelected(this)" required name="Tipo_pessoa">
            <option value="">Selecione...</option>
            <option value="pf" @selected(old('Tipo_pessoa') == 'pf')>Pessoa Física</option>
            <option value="pj" @selected(old('Tipo_pessoa') == 'pj')>Pessoa Jurídica</option>
        </select>
    </div>
    <div class="formControl" id="pf" @if (old('Tipo_pessoa') != 'pf') style="display: none;" @endif>
        <label>CPF</label>
        <input type="text" name="CPF" onkeyup="ValidaNumero(1, this.value)"
            onblur="ValidaNumero(1, this.value)" data-mask="000.000.000-00" pattern=".{11,}"
            @if (old('Tipo_pessoa') == 'pf') required @endif value="{{ old('CPF') }}">
        <small class="email-input-info red" style="display: none" id="msgCpfInvalido">Número informado
            inválido.</small>
    </div>
    <div class="formControl" id="pj" @if (old('Tipo_pessoa') != 'pj') style="display: none;" @endif>
        <label>CNPJ</label>
        <input type="text" name="CNPJ" onkeyup="ValidaNumero(2, this.value)"
            onblur="ValidaNumero(2, this.value)" data-mask="00.000.000/0000-00" pattern=".{17,}"
            @if (old('Tipo_pessoa') == 'pj') required @endif value="{{ old('CNPJ') }}">
        <small class="email-input-info red" style="display: none" id="msgCnpjInvalido">Número informado
            inválido.</small>
    </div>
    <div class="formControl">
        <label>Nome</label>
        <input type="text" name="nome" value="{{ old('nome') }}" required>
    </div>
    <div class="formControl">
        <label>E-mail</label>
        <input type="text" name="email" value="{{ old('email') }}" required>
    </div>
    <div class="formControl">
        <label>Telefone</label>
        <input type="text" class="phone" name="telefone" value="{{ old('telefone') }}" required>
    </div>

    <h3 style="margin-top: 32px;">Dados de Entrega</h3>
    <div class="formControl">
        <label>Tipo da Certidão</label>
        <select onchange="typeSelected(this)" required name="Tipo da Certidão">
            <option value="">Selecione...</option>
            <option value="papel" @selected(old('Tipo_da_Certidão') == 'papel')>papel</option>
            <option value="digital" @selected(old('Tipo_da_Certidão') == 'digital')>digital</option>
        </select>
    </div>

    <div class="formControl">
        <label>Forma de Entrega</label>
        <select id="deliveryForm" onchange="deliveryFormSelected(this)" required name="Forma de Entrega">
            <option value="">Selecione a forma de entrega</option>

            @if (old('Tipo_da_Certidão') == 'papel')
                <option value="retirar" @selected(old('Forma_de_Entrega') == 'retirar')>Retirar no cartório</option>
                <option value="correios" @selected(old('Forma_de_Entrega') == 'correios')>Envio pelos correios</option>
            @elseif(old('Tipo_da_Certidão') == 'digital')
                <option value="eletronica" @selected(old('Forma_de_Entrega') == 'eletronica') selected>Eletrônica</option>
            @endif
        </select>
    </div>

    <div class="formControl" id="deliveryType" onchange="getDeliveryFee()"
        @if (old('Forma_de_Entrega') != 'correios') style="display: none;" @endif name="Tipo de Envio">
        <label>Tipo de Envio</label>
        <select name="tipo_envio_select">
            <option value="">Selecione o tipo de envio</option>
            <option value="carta_registrada" @selected(old('tipo_envio_select') == 'carta_registrada')>Carta Registrada</option>
            {{-- <option value="sedex" @selected(old('tipo_envio_select') == 'sedex')>Sedex</option> --}}
        </select>
    </div>
    <div id="divEndereco">
        <div class="formControl">
            <label>CEP</label>
            <input type="text" name="CEP" maxlength="9" id="cep" onkeyup="searchAddressByCEP()"
                value="{{ old('CEP') }}" required>
        </div>
        <div class="formControl" style="display:none">
            <button type="button" class="btnDefault buscar-cep" onclick="searchAddressByCEP()"
                style="font-size:15px;">Buscar endereço</button>
        </div>

        <div class="formControl">
            <label>Logradouro (rua, avenida, travessa)</label>
            <input type="text" class="dados-endereco" name="Logradouro" id="logradouro"
                value="{{ old('Logradouro') }}" required>
        </div>

        <div class="formControl">
            <label>Número</label>
            <input type="text" name="Número" value="{{ old('Número') }}" required>
        </div>

        <div class="formControl">
            <label>Complemento</label>
            <input type="text" name="Complemento" value="{{ old('LograComplementodouro') }}">
        </div>

        <div class="formControl">
            <label>Bairro</label>
            <input type="text" class="dados-endereco" name="Bairro" id="bairro" value="{{ old('Bairro') }}"
                required>
        </div>

        <div class="formControl">
            <label>País</label>
            <input type="text" readonly name="Pais" id="Pais" value="Brasil" required>
        </div>

        <div class="formControl">
            <label>UF</label>
            <select class="dados-endereco" onchange="updateCities(this, '')" name="UF" id="uf" required>
                <option value="">Selecione o estado</option>
                <option value="AC" @selected(old('UF') == 'AC')>Acre</option>
                <option value="AL" @selected(old('UF') == 'AL')>Alagoas</option>
                <option value="AP" @selected(old('UF') == 'AP')>Amapá</option>
                <option value="AM" @selected(old('UF') == 'AM')>Amazonas</option>
                <option value="BA" @selected(old('UF') == 'BA')>Bahia</option>
                <option value="CE" @selected(old('UF') == 'CE')>Ceará</option>
                <option value="DF" @selected(old('UF') == 'DF')>Distrito Federal</option>
                <option value="ES" @selected(old('UF') == 'ES')>Espírito Santo</option>
                <option value="GO" @selected(old('UF') == 'GO')>Goiás</option>
                <option value="MA" @selected(old('UF') == 'MA')>Maranhão</option>
                <option value="MT" @selected(old('UF') == 'MT')>Mato Grosso</option>
                <option value="MS" @selected(old('UF') == 'MS')>Mato Grosso do Sul</option>
                <option value="MG" @selected(old('UF') == 'MG')>Minas Gerais</option>
                <option value="PA" @selected(old('UF') == 'PA')>Pará</option>
                <option value="PB" @selected(old('UF') == 'PB')>Paraíba</option>
                <option value="PR" @selected(old('UF') == 'PR')>Paraná</option>
                <option value="PE" @selected(old('UF') == 'PE')>Pernambuco</option>
                <option value="PI" @selected(old('UF') == 'PI')>Piauí</option>
                <option value="RJ" @selected(old('UF') == 'RJ')>Rio de Janeiro</option>
                <option value="RN" @selected(old('UF') == 'RN')>Rio Grande do Norte</option>
                <option value="RS" @selected(old('UF') == 'RS')>Rio Grande do Sul</option>
                <option value="RO" @selected(old('UF') == 'RO')>Rondônia</option>
                <option value="RR" @selected(old('UF') == 'RR')>Roraima</option>
                <option value="SC" @selected(old('UF') == 'SC')>Santa Catarina</option>
                <option value="SP" @selected(old('UF') == 'SP')>São Paulo</option>
                <option value="SE" @selected(old('UF') == 'SE')>Sergipe</option>
                <option value="TO" @selected(old('UF') == 'TO')>Tocantins</option>
            </select>
        </div>

        <div class="formControl">
            <label>Cidade</label>
            <select class="dados-endereco" name="Cidade" id="cidade" required>
                <option value="">Selecione a cidade</option>
                <!-- As opções de cidades serão preenchidas dinamicamente -->
            </select>
        </div>
    </div>
    <div class="formControl check">
        <input type="checkbox" required
            name="Responsabilizo-me pela preservação da vida privada das partes (CF, art. 5º, inciso X)."
            value="sim" id="chkResponsabilizo">
        <label for="chkResponsabilizo">Responsabilizo-me pela preservação da vida privada das partes (CF, art. 5º,
            inciso X).</label>
    </div>
    <div class="formControl check mb-1">
        <input type="checkbox" id="accordingly_checkbox" name="accordingly_checkbox" required value="sim">
        <label for="accordingly_checkbox">Li, estou de acordo com a <a href="/politica-de-privacidade"
                target="_blank">política de privacidade</a></label>
    </div>
    <div class="formControl check mt-1">
        <input type="checkbox" id="want_messages_checkbox" name="want_messages_checkbox">
        <label for="want_messages_checkbox">Desejo receber mensagens informativas do 26º Tabelionato de
            Notas.</label>
    </div>
    <div class="reCaptcha">
        {!! htmlFormSnippet() !!}
    </div>

    <div class="formControl">
        <button class="btnDefault red">Enviar </button>
    </div>
    </fieldset>


    @if (session('form_error') || $errors->any())
        <div id="anchor"></div>
        <div class="message error">
            <p>Formulário não enviado.</p>
            @if ($errors->any())
                <ul class="mt-2">
                    @foreach ($errors->all() as $error)
                        <li style="color:#a94442;">{{ $error }}</li>
                    @endforeach
                </ul>
            @endif
        </div>
    @endif
</form>

<div id="anchor"></div>
<div class="boxBgGray boxAfterForm summaryOfRequest" id="summary" style="display: none">
    <h3>SOLICITAÇÃO ENVIADA COM SUCESSO!</h3>
    <p>Sua solicitação foi recebida e será avaliada pelo setor de certidões quanto ao cumprimento dos requisitos da
        <b>LGPD (Lei Geral de Proteção de Dados)</b> e dos <b>Provimentos CNJ nºs 134/2022 e 149/2023.</b>
    </p>
    <div class="d-flex">
        <p class="mt-0 mb-0 d-flex">
            <strong>Certidão:</strong>&nbsp;
        <p class="mt-0 mb-0" id="valorCertidao">R$ {{ number_format(old('Valor'), 2, ',', '.') }}</p>
        </p>
    </div>
    <div class="d-flex">
        <p class="mt-0 mb-0 d-flex">
            <strong id="labelTipoEntrega">Opção digital:</strong>&nbsp;
        <p class="mt-0 mb-0" id="valorEntrega">R$ {{ number_format(old('Opção_digital'), 2, ',', '.') }}</p>
        </p>
    </div>
    <div class="d-flex">
        <p class="mt-0 mb-0 d-flex">
            <strong>Total:</strong>&nbsp;
        <p class="mt-0 mb-0" id="valorTotal">R$ {{ number_format(old('Valor') + old('Opção_digital'), 2, ',', '.') }}
        </p>
        </p>
    </div>
    <p>
    <ul>
        <li>Se o pedido estiver <b>em conformidade</b>, você receberá um e-mail com os dados bancários do <b>26º
                Tabelionato de Notas</b> para prosseguir com o pagamento.</li>
        <li>Caso haja <b>alguma inconsistência</b>, um e-mail será enviado informando esta situação.</li>
    </ul>
    <br>
    Obrigado por usar os serviços do 26!<br><br>
    <a href="/" class="btnDefault">OK</a>
    </p>
</div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.16/jquery.mask.min.js"
    integrity="sha512-pHVGpX7F/27yZ0ISY+VVjyULApbDlD0/X0rgGbTqCE7WFW5MezNTWG/dnhtbBuICzsd0WQPgpE4REBLv+UqChw=="
    crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script nonce="{{ csp_nonce() }}">
    $(document).ready(function() {
        // console.log($('a[href="#form-anchor"]'));
        $('a[href="#form-anchor"]').on('click', function() {
            if ($('.summaryOfRequest').length > 0) {
                resetRequest();
            }
        });
    });

    function updateCities(select, selecionado) {
        var uf = select.value;
        var citySelect = document.getElementById('cidade');
        citySelect.innerHTML = '<option value="">Selecione a cidade</option>'; // Limpa as opções atuais

        if (uf) {
            // Requisição à API do IBGE para obter os municípios de um estado
            $.get('https://servicodados.ibge.gov.br/api/v1/localidades/estados/' + uf + '/municipios', function(data) {
                data.forEach(function(city) {
                    var option = document.createElement('option');
                    option.value = city.nome;
                    option.text = city.nome;
                    citySelect.appendChild(option);
                });

                // Seleciona a cidade previamente selecionada, se houver
                var oldCity = selecionado || "{{ old('Cidade') }}";
                if (oldCity) {
                    citySelect.value = oldCity;
                }
            });
        }
    }

    function resetRequest() {
        $('fieldset.formFields input[type="text"]').val('');
        $('fieldset.formFields textarea').val('');
        $('fieldset.formFields select').val('').trigger('change');
        $('fieldset.formFields input[type="checkbox"]').prop('checked', false);

        $('.summaryOfRequest').remove();
        $('#payment-method').remove();
        $('#generate-boleto').remove();
        $('#show-pix').remove();
        $('#d').show();
    }

    function solicitanteSelected(select) {
        if ($(select).val() == 'pf') {
            $('#pf').show();
            $('#pf').children('input').attr('required', true);

            $('#pj').hide();
            $('#pj').children('input').attr('required', false);
        } else if ($(select).val() == 'pj') {
            $('#pj').show();
            $('#pj').children('input').attr('required', true);

            $('#pf').hide();
            $('#pf').children('input').attr('required', false);
        } else {
            $('#pf').hide();
            $('#pf').children('input').attr('required', false);
            $('#pj').hide();
            $('#pj').children('input').attr('required', false);
        }
    }

    function typeSelected(select) {
        var options = '<option value="">Selecione a forma de entrega</option>';
        if ($(select).val() == 'papel') {
            options += '<option value="retirar">Retirar no cartório</option>';
            options += '<option value="correios">Envio pelos correios</option>';
            $('#divEndereco').show();
            $('#divEndereco').find('input, select, textarea').attr('required', true);
        } else if ($(select).val() == 'digital') {
            options += '<option value="eletronica" selected>Eletrônica</option>';
            $('#divEndereco').hide();
            $('#divEndereco').find('input, select, textarea').removeAttr('required');
            $('#deliveryType').hide();
            $('#deliveryType').children('select').attr('required', false);
            $('#deliveryForm').html(options);
            return;
        }

        $('#deliveryType').hide();
        $('#deliveryType').children('select').attr('required', true);

        $('#deliveryForm').html(options);
    }

    function deliveryFormSelected(select) {
        var val = $(select).val();
        if (val == 'correios') {
            $('#deliveryType').show();
            $('#deliveryType').children('select').attr('required', true);
        } else {
            $('#deliveryType').hide();
            $('#deliveryType').children('select').attr('required', false);
        }
    }

    function searchAddressByCEP() {
        $('.dados-endereco').val('');
        var citySelect = document.getElementById('cidade');
        citySelect.innerHTML = '<option value="">Selecione a cidade</option>';
        let cep = $('#cep').val().replace('-', '');

        if (cep.length < 8) return;

        $('#cep').val(cep);

        $.get('https://viacep.com.br/ws/' + $('#cep').val() + '/json', function(data) {
            console.log(data)
            $('#logradouro').val(data.logradouro)
            $('#bairro').val(data.bairro)
            $('#uf').val(data.uf);
            updateCities($("#uf")[0], data.localidade);
            $('#cidade').val(data.localidade)
            getDeliveryFee();
        }).fail(function() {
            alert(
                'Não foi possível encontrar dados de endereço para o CEP fornecido. Deve informar endereço em território nacional.'
            )
        });
    }

    function getDeliveryFee() {
        var type = $('#deliveryType').children('select').val();
        if (type != '' && $('#cep').val() != '') {
            $.get("{{ route('forms.correios.delivery.fee') }}", {
                cep: $('#cep').val(),
                type_correios: type
            }, function(fee) {
                $('#delivery-fee').val(fee);
            });
        }
    }

    function proceed(btnProceed) {
        $(btnProceed).hide();
        $('#payment-method').show();
    }

    function paymentMethodSelected(select) {
        var val = $(select).val();
        if (val == 'pix') {
            $('#generate-boleto').hide();
            $('#show-pix').show();
        } else if (val == 'boleto') {
            $('#generate-boleto').show();
            $('#show-pix').hide();
        } else {
            $('#generate-boleto').hide();
            $('#show-pix').hide();
        }
    }

    function printProtocol(certificate_id) {
        var url = new URL("{{ route('forms.print.certificate.protocol') }}");
        url.searchParams.set('id', certificate_id);
        window.open(url, '_blank').focus();
    }

    function actTypeSelected(select) {
        var val = select.value;
        // Encontra o bloco pai correto
        var bloco = select.closest('.dados-ato-bloco');
        var container = $(bloco).find('#actTypeTextAreaContainer');
        var label = container.children('label');
        var area = container.children('textarea');
        var divAnalise = $(bloco).find('#divAnalise');

        $(bloco).find('#livro').removeAttr('required');
        $(bloco).find('#folha').removeAttr('required');

        // Exibe ou esconde o campo de upload só neste bloco
        if (val.split('|')[1] == '1') {
            divAnalise.show();
        } else {
            divAnalise.hide();
        }

        if (val.indexOf('Documento arquivado') > -1) {
            label.html('Informe os dados do documento');
            area.attr('name', 'Dados do documento_' + bloco.querySelector('#hdnIndice').value);
            area.attr('required', true);
            container.show();
        } else if (val == 'Busca') {
            label.html('Informe os dados para a busca');
            area.attr('name', 'Dados para busca[]');
            area.attr('required', true);
            container.show();
        } else {
            label.html('');
            area.removeAttr('name');
            area.removeAttr('required');
            container.hide();
            $(bloco).find('#livro').attr('required', true);
            $(bloco).find('#folha').attr('required', true);
        }
    }

    function ValidaNumero(tipo, valor) {
        $("#msgCpfInvalido").hide();
        $("#msgCnpjInvalido").hide();
        if (tipo == 1 && valor.length == 14) {
            if (!validarCPF(valor)) {
                $("#msgCpfInvalido").show('fast');
            }
        } else if (valor.length == 18) {
            if (!validarCNPJ(valor)) {
                $("#msgCnpjInvalido").show('fast');
            }
        }
    }

    // Função para validar CPF
    function validarCPF(cpf) {
        cpf = cpf.replace(/[^\d]+/g, ''); // Remove caracteres não numéricos
        if (cpf.length !== 11 || /^(\d)\1+$/.test(cpf)) {
            return false;
        }
        let soma = 0;
        for (let i = 0; i < 9; i++) {
            soma += parseInt(cpf.charAt(i)) * (10 - i);
        }
        let resto = 11 - (soma % 11);
        if (resto === 10 || resto === 11) {
            resto = 0;
        }
        if (resto !== parseInt(cpf.charAt(9))) {
            return false;
        }
        soma = 0;
        for (let i = 0; i < 10; i++) {
            soma += parseInt(cpf.charAt(i)) * (11 - i);
        }
        resto = 11 - (soma % 11);
        if (resto === 10 || resto === 11) {
            resto = 0;
        }
        return resto === parseInt(cpf.charAt(10));
    }

    // Função para validar CNPJ
    function validarCNPJ(cnpj) {
        cnpj = cnpj.replace(/[^\d]+/g, ''); // Remove caracteres não numéricos
        if (cnpj.length !== 14 || /^(\d)\1+$/.test(cnpj)) {
            return false;
        }
        let tamanho = cnpj.length - 2;
        let numeros = cnpj.substring(0, tamanho);
        let digitos = cnpj.substring(tamanho);
        let soma = 0;
        let pos = tamanho - 7;
        for (let i = tamanho; i >= 1; i--) {
            soma += numeros.charAt(tamanho - i) * pos--;
            if (pos < 2) {
                pos = 9;
            }
        }
        let resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
        if (resultado !== parseInt(digitos.charAt(0))) {
            return false;
        }
        tamanho = tamanho + 1;
        numeros = cnpj.substring(0, tamanho);
        soma = 0;
        pos = tamanho - 7;
        for (let i = tamanho; i >= 1; i--) {
            soma += numeros.charAt(tamanho - i) * pos--;
            if (pos < 2) {
                pos = 9;
            }
        }
        resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
        return resultado === parseInt(digitos.charAt(1));
    }

    function addFileInput(btn) {
        // Busca o container de arquivos apenas no bloco do botão clicado
        var bloco = btn.closest('.dados-ato-bloco');
        var container = bloco.querySelector('.fileInputsContainer');
        var group = document.createElement('div');
        group.className = 'input-group mb-2 file-input-group';

        var input = document.createElement('input');
        input.type = 'file';
        input.name = 'arquivos_' + bloco.querySelector('#hdnIndice').value + '[]';
        input.className = 'form-control';

        var fileNameSpan = document.createElement('span');
        fileNameSpan.className = 'ms-2 file-name';
        fileNameSpan.style.fontSize = '1.5em';

        input.onchange = function() {
            fileNameSpan.textContent = input.files.length ? input.files[0].name : '';
        };

        group.appendChild(input);
        group.appendChild(fileNameSpan);

        container.appendChild(group);

        atualizarBotaoRemoverArquivo(bloco);
    }

    function removeLastFileInput(btn) {
        var bloco = btn.closest('.dados-ato-bloco');
        var container = bloco.querySelector('.fileInputsContainer');
        var groups = container.querySelectorAll('.file-input-group');
        if (groups.length > 1) {
            groups[groups.length - 1].remove();
        }
        atualizarBotaoRemoverArquivo(bloco);
    }

    function atualizarBotaoRemoverArquivo(bloco) {
        // Atualiza o botão remover apenas no bloco informado
        var container = bloco.querySelector('.fileInputsContainer');
        var groups = container.querySelectorAll('.file-input-group');
        var btnRemover = bloco.querySelector('.btnRemoverArquivo');
        if (btnRemover) {
            btnRemover.style.display = (groups.length > 1) ? 'inline-block' : 'none';
        }
    }

    // Ao carregar a página, atualize todos os blocos
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.dados-ato-bloco').forEach(function(bloco) {
            atualizarBotaoRemoverArquivo(bloco);
            // Adiciona exibição do nome do arquivo para o campo inicial
            var input = bloco.querySelector('.fileInputsContainer input[type="file"]');
            if (input) {
                var fileNameSpan = document.createElement('span');
                fileNameSpan.className = 'ms-2 file-name';
                fileNameSpan.style.fontSize = '1.5em';
                input.parentNode.insertBefore(fileNameSpan, input.nextSibling);
                input.onchange = function() {
                    fileNameSpan.textContent = input.files.length ? input.files[0].name : '';
                };
            }
        });
    });

    function adicionarPedidoCertidao() {
        var container = document.getElementById('dadosAtoContainer');
        var blocos = container.getElementsByClassName('dados-ato-bloco');
        var ultimoBloco = blocos[blocos.length - 1];
        var novoBloco = ultimoBloco.cloneNode(true);

        // Limpa os valores dos inputs/selects/textareas do novo bloco
        $(novoBloco).find('input, select, textarea').each(function() {
            if (this.tagName === 'SELECT' || this.tagName === 'TEXTAREA') {
                this.value = '';
            } else if (this.type === 'text') {
                this.value = '';
            }
        });

        novoBloco.querySelector('#hdnIndice').value = parseInt(ultimoBloco.querySelector('#hdnIndice').value) + 1;

        // Remove o campo de arquivos do novo bloco, se existir
        var divAnalise = novoBloco.querySelector('#divAnalise');
        if (divAnalise) {
            divAnalise.style.display = 'none';

            // Mantém apenas o primeiro campo de arquivo
            var containerArquivos = divAnalise.querySelector('.fileInputsContainer');
            if (containerArquivos) {
                var grupos = containerArquivos.querySelectorAll('.file-input-group');
                grupos.forEach(function(grupo, idx) {
                    if (idx > 0) grupo.remove();
                    else {
                        // Limpa o valor do input de arquivo e o nome exibido
                        var input = grupo.querySelector('input[type="file"]');
                        var span = grupo.querySelector('.file-name');
                        input.name = 'arquivos_' + novoBloco.querySelector('#hdnIndice').value + '[]';
                        if (span) span.textContent = '';
                        input.onchange = function() {
                            span.textContent = input.files.length ? input.files[0].name : '';
                        };
                        if (input) input.value = '';

                    }
                });
            }
        }

        container.appendChild(novoBloco);

        // Exibe o botão de remover se houver mais de um bloco
        atualizarBotaoRemover();
        atualizarBotaoRemoverArquivo(novoBloco);
    }

    function removerUltimoPedidoCertidao() {
        var container = document.getElementById('dadosAtoContainer');
        var blocos = container.getElementsByClassName('dados-ato-bloco');
        if (blocos.length > 1) {
            blocos[blocos.length - 1].remove();
        }
        atualizarBotaoRemover();
    }

    function atualizarBotaoRemover() {
        var container = document.getElementById('dadosAtoContainer');
        var blocos = container.getElementsByClassName('dados-ato-bloco');
        var btnRemover = document.getElementById('btnRemoverAto');
        if (blocos.length > 1) {
            btnRemover.style.display = 'inline-block';
        } else {
            btnRemover.style.display = 'none';
        }
    }
    var forms = $("form");

    function retornaBotoes() {
        $('form').each(function() {
            $(this).find('button').show();
            $(this).find('button').prop('disabled', false);
        });
        atualizarBotaoRemover();
        document.querySelectorAll('.dados-ato-bloco').forEach(function(bloco) {
            atualizarBotaoRemoverArquivo(bloco);
        });
    }

    $(document).ready(function() {
        for (var i = 0; i < forms.length; i++) {
            forms.eq(i).off("submit").on("submit", function(event) {
                var loading = '<div class="spinner"></div>';
                $(this).find('button').hide();
            });
        }

        $("#d").on('click', function() {
            // Remove mensagens de erro anteriores
            $('#divErros').remove();
        });

        // Envio AJAX do formulário
        $('#d').off("submit").on('submit', function(e) {
            e.preventDefault();

            var form = this;
            var formData = new FormData(form);

            // Desabilita o botão para evitar múltiplos envios
            $(form).find('button[type="submit"], .btnDefault.red').prop('disabled', true);

            $.ajax({
                url: $(form).attr('action'),
                method: $(form).attr('method'),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    // Exemplo: exibe mensagem de sucesso e esconde o formulário
                    console.log(response);
                    $("#valorCertidao").html(new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                    }).format(response.formData.request.Valor));
                    if (parseFloat(response.formData.request.Opção_digital) > 0) {
                        $('#labelTipoEntrega').html('Opção digital:');
                        $("#valorEntrega").html(new Intl.NumberFormat('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                        }).format(response.formData.request.Opção_digital));
                        $("#valorTotal").html(new Intl.NumberFormat('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                        }).format(parseFloat(response.formData.request.Valor) +
                            parseFloat(response.formData.request.Opção_digital)));
                    } else {
                        $('#labelTipoEntrega').html('Frete:');
                        $("#valorEntrega").html(new Intl.NumberFormat('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                        }).format(response.formData.request.Taxa_de_entrega));
                        $("#valorTotal").html(new Intl.NumberFormat('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                        }).format(parseFloat(response.formData.request.Valor) +
                            parseFloat(response.formData.request.Taxa_de_entrega)));
                    }

                    $('#d').hide();
                    $('#summary').show(); // Remove resumo anterior, se houver
                    // Se o backend retornar HTML do resumo, insira aqui:
                    // if (response.summary_html) {
                    //     $(form).after(response.summary_html);
                    //     window.scrollTo(0, 0);
                    // } else {
                    //     $('#d').hide();
                    //     $('#payment-method').show();
                    // }
                    retornaBotoes();
                },
                error: function(xhr) {
                    // Exemplo: exibe erros de validação
                    console.log(xhr);
                    var msg = '';
                    if (xhr.responseJSON && xhr.responseJSON.errors) {
                        msg += '<ul>';
                        $.each(xhr.responseJSON.errors, function(field, errors) {
                            $.each(errors, function(i, error) {
                                msg += '<li>' + error + '</li>';
                            });
                        });
                        msg += '</ul>';
                    } else {
                        msg += '<ul>';
                        msg = '<li>' + (xhr.statusText ||
                            'Ocorreu um erro ao enviar o formulário.') + '</li>';
                        msg += '</ul>';
                    }
                    $('<div id="divErros" class="message error">' + msg + '</div>')
                        .insertBefore(form);
                    $(form).find('button[type="submit"], .btnDefault.red').prop('disabled',
                        false);
                    window.scrollTo(0, 0);
                    retornaBotoes();
                    grecaptcha.reset();
                }
            });
        });
    });

    $('.phone').mask('(00) 0000-00009', {
        clearIfNotMatch: true
    });
    $('.phone').blur(function(event) {
        if ($(this).val().length == 15) {
            $('.phone').mask('(00) 00000-0009');
        } else if ($(this).val().length == 14) {
            $('.phone').mask('(00) 0000-00009');
        } else {
            this.value = '';
        }
    });

    // Chame ao carregar a página para garantir o estado correto do botão
    document.addEventListener('DOMContentLoaded', atualizarBotaoRemover);
</script>
