<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Auth;
use App\Mail\SendTokenToClient;
use App\Models\Client;
use Carbon\Carbon;
use App\Http\Controllers\NewsletterController;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;

class AuthController extends Controller
{
    public function login(Request $r)
    {
        $client = Auth::user();
        if ($client) {
            if (count($client->collaborators) > 0) {
                return redirect()->route('my.services');
            } else {
                return redirect()->route('my.acc');
            }
        }
        return view('auth.login');
    }

    public function sendToken(Request $r)
    {
        $r->validate([
            'g-recaptcha-response' => 'required',
            'email' => 'required',
            'password' => 'required',
        ], [
            'g-recaptcha-response' => 'Assinale a caixa "não sou um robô"!',
        ]);

        if (Auth::check()) {
            $client = Auth::user();
            return $this->redirectBasedOnRole($client);
        }

        $credentials = $r->only('email', 'password');
        $user = Client::where('email', $r->email)->first();

        if ($user && !$user->is_active) {
            $token = Str::random(12);
            $user->remember_token = $token;
            //Mail::to($user->email)->send(new SendTokenToClient($user->email, $token, 'activation'));
            $user->save();

            // return back()->withErrors(['inactive_account' => 'Sua conta ainda não está ativa. Por favor, verifique seu e-mail para ativar a conta.']);
            return back()->withErrors(['inactive_account' => 'Sua conta necessita de validação, por favor <a href="/recuperar-senha" style="color:#a94442">clique aqui</a> para solicitar recuperação de senha e siga as instruções no e-mail.']);
        }

        if (Auth::attempt($credentials)) {
            return $this->redirectBasedOnRole(Auth::user());
        }

        return back()->withErrors(['email' => 'As credenciais fornecidas não correspondem aos nossos registros.']);
    }


    private function sendLoginToken($client)
    {
        $token = Str::random(6);
        $client->remember_token = $token;
        Mail::to($client->email)->send(new SendTokenToClient($client->email, $token, 'login'));
        $client->save();

    }

    public function verifyToken(Request $r)
    {
        $client = Client::where('email', $r->email)->where('remember_token', $r->token)->first();
        if ($client) {
            Auth::loginUsingId($client->id);
            $client->remember_token = null;
            $client->save();
            return $this->redirectBasedOnRole($client);
        }

        return redirect()->route('login')->withErrors([
            'token_error' => 'Token inválido ou expirado, por favor tente fazer login novamente.'
        ]);
    }

    public function auth(Request $r)
    {
        if (!$r->hasValidSignature()) {
            return redirect()->route('login')->withErrors([
                'expired_token_error' => 'O acesso está expirado, por favor realize o acesso novamente.'
            ]);
        }

        $client = Client::where('email', $r->email)->first();

        Auth::loginUsingId($client->id);
        $client->is_active = 1;
        $client->save();

        if (count($client->collaborators) > 0) {
            return redirect()->route('my.services');
        } else {
            return redirect()->route('my.acc');
        }
    }


    public function reset()
    {
        return view('auth.reset_password');
    }

    public function resetPassword(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
        ], [
            'email.required' => 'O campo email é obrigatório.',
            'email.email' => 'Insira um endereço de e-mail válido.',
        ]);

        $client = Client::where('email', $request->email)->first();
        if (!$client) {
            return back()->withErrors(['email' => 'Não conseguimos encontrar um usuário com esse endereço de e-mail.']);
        }

        $token = Str::random(12);
        $client->remember_token = $token;
        Mail::to($client->email)->send(new SendTokenToClient($client->email, $token, 'reset'));
        $client->save();

        return view('auth.verify_token', ['email' => $client->email]);
    }


    private function redirectBasedOnRole($client)
    {
        $lastUrl = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : 'Nenhuma URL anterior';
        $parsedUrl = parse_url($lastUrl);
        $pathSegments = explode('/', trim($parsedUrl['path'], '/'));
        $lastSegment = end($pathSegments);

        if (count($client->collaborators) > 0) {
            if ($lastSegment == "recuperar-senha") {
                return redirect()->route('my.acc', ['actionType' => 'reset']);

            }
            return redirect()->route('my.services');
        } else {
            return redirect()->route('my.acc');
        }
    }

    public function logout()
    {
        Auth::logout();
        return redirect()->route('index');
    }

}
