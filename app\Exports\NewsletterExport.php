<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Illuminate\Support\Collection;

class NewsletterExport implements FromCollection, ShouldAutoSize{
    protected $newsletters;

    public function __construct(\Illuminate\Database\Eloquent\Collection $newsletters)
    {
        $this->newsletters = $newsletters;
    }

    public function collection(){
        $collection = new Collection();
        $collection->push(['Nome', 'E-mail', 'Data']);

        foreach($this->newsletters as $news){
            $collection->push([$news->name ?? '', $news->email, $news->created_at->format('d/m/Y H:i')]);
        }

        return $collection;
    }
}
