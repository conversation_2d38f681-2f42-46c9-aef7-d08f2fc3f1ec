<?php

namespace App\Traslado;

use App\Models\FormFilling;
use App\Models\Sysmeta;
use Carbon\Carbon;

class Traslado{

    private $conteudoTraslado = '';
    private $conteudoNewsletter = '';

    function __construct()
    {
        $configuracoes = Sysmeta::where('meta_key', 'mail_traslado_settings');

        if($configuracoes->count() > 0){
            $configuracao = $configuracoes->first();
            $this->conteudoTraslado = json_decode($configuracao->meta_value)->conteudo_traslado;
            $this->conteudoNewsletter = json_decode($configuracao->meta_value)->conteudo_newsletter;
        }
    }

    function retorna_status_protocolos($protocolos){
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($curl, CURLOPT_USERPWD, "26TN@pi:puGyZ2Xtm8jF08D");

        if(isset($_SERVER['HTTPS']))
        curl_setopt($curl, CURLOPT_URL, 'https://api.e-tabeliao.com.br/26TN/historicoProtocolo');
        else
        curl_setopt($curl, CURLOPT_URL, 'http://api.e-tabeliao.com.br/26TN/historicoProtocolo');

        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array(
        'chave: 42ACA468E34BB7142454BAF73A698',
        'Content-Type: application/json',
        ));

        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode(['protocolo' => $protocolos]));
        $resultAndamentos = curl_exec($curl);
        $andamentos = json_decode($resultAndamentos);
        return $andamentos;
    }

    function retorna_ultimo_status_protocolo($andamentos, $protocolo_busca){
        foreach($andamentos as $andamento){
            if($andamento->protocolo != $protocolo_busca)
              continue;

            if(!isset($andamento->envioRI)){
                return "";
            }
            foreach($andamento->envioRI as $ri){
              $ultimo_status = $ri->historico[sizeof($ri->historico) -1]->status . '|' . $ri->historico[sizeof($ri->historico) -1]->data . '|' . $ri->codigo_interno;
            }
            break;
        }
        return $ultimo_status;
    }

    function envia_email($dados, $codigo_interno){
          try{
              $data = Carbon::now()->timezone('America/Sao_Paulo');
              // $subject = 'Push 26 Notas - Atualização do protocolo ' . $dados->protocolo . " [" . $codigo_interno . "] " . ' - ' . $data->format('d/m/Y');
              $subject = 'Sua certidão foi realizada com sucesso (' . $data->format('d/m/Y') . ') - 26º Tabelionato de Notas de São Paulo';
              $email = "<EMAIL>";

              $conteudoEmail = $this->conteudoTraslado;
              $conteudoNewsletter = $this->conteudoNewsletter;
              $conteudoNewsletter = str_replace("[NOME]", $dados->nome, $conteudoNewsletter);

              $conteudoNewsletter .= "<br /><br /><a href='" . env('APP_URL') . "/cadastro_newsletter/" . $dados->email . "' class='btnMailTraslado' title='Clique para se cadastrar'>Newsletter</a>";

              $conteudoEmail = str_replace("[NOME]", $dados->nome, $conteudoEmail);
              $conteudoEmail = str_replace("[TRASLADO]", \DateTime::createFromFormat('Y-m-d H:i:s', $dados->ultimoStatusData)->format('d/m/Y H:i:s'), $conteudoEmail);
              $conteudoEmail = str_replace("[ATUALIZACAO]", $dados->ultimoStatus, $conteudoEmail);
              $conteudoEmail = str_replace("[ESCREVENTE]", $dados->escrevente_nome, $conteudoEmail);
              $conteudoEmail = str_replace("[NEWSLETTER]", $conteudoNewsletter, $conteudoEmail);

              $data = [
                'conteudo' => $conteudoEmail
              ];

            //   \Mail::send('emails.traslado-crm-interno', $data, function($message) use ($email, $subject) {
            //     $message->to($email)->subject($subject);
            //   });
            return "OK";
        }catch(\Exception $e){
            return $e->getMessage();
        }
    }

    function atualiza_status_protocolo($protocolo, $ultimo_status, $ultima_data, $codigo_interno){
        $protocolos = FormFilling::whereRaw('JSON_EXTRACT(fields, "$.protocolo") is not null AND JSON_EXTRACT(fields, "$.protocolo") = ?', [$protocolo])->distinct()->get();
        foreach($protocolos as $protocolo){
            $fields = json_decode($protocolo->fields);
            $fields->ultimoStatus = $ultimo_status;
            $fields->ultimoStatusData = $ultima_data;

            if($ultimo_status == "REGISTRADO/AVERBADO"){
                $fields->envioEmailTraslado = $this->envia_email($fields, $codigo_interno);
                $ultimo_status = "Traslado emitido";
                if($fields->envioEmailTraslado == "OK"){
                    $fields->concluido = true;
                }
            }
            $protocolo->fields = json_encode($fields);
            $protocolo->save();
        }
    }

    function envia_emails_traslado_individual($crm_id){
        if($this->conteudoTraslado == '')
            exit;

        $protocolo = json_decode(FormFilling::find($crm_id)->fields);
        $protocolos = array();


        array_push($protocolos, intval($protocolo->protocolo));

        if(count($protocolos) > 0){
            $andamentos = $this->retorna_status_protocolos($protocolos);

            $statusProtocolo = $this->retorna_ultimo_status_protocolo($andamentos, intval($protocolo->protocolo));
            if($statusProtocolo != ""){
                $this->atualiza_status_protocolo($protocolo->protocolo, explode('|',$statusProtocolo)[0], explode('|',$statusProtocolo)[1], explode('|',$statusProtocolo)[2]);
            }
        }

        return redirect()->to("admin/crm/$crm_id");
    }

    function envia_emails_traslado(){
        if($this->conteudoTraslado == '')
            exit;

        $buscaProtocolos = FormFilling::whereRaw('JSON_EXTRACT(fields, "$.protocolo") is not null AND (JSON_EXTRACT(fields, "$.concluido") is null OR JSON_EXTRACT(fields, "$.email_enviado") <> \'OK\')')->selectRaw('REPLACE(JSON_EXTRACT(fields, "$.protocolo"), \'"\', \'\') as protocolo')->distinct()->get();
        $protocolos = array();

        foreach($buscaProtocolos as $protocolo){
            array_push($protocolos, intval($protocolo->protocolo));
        }
        if(count($protocolos) > 0){
            $andamentos = $this->retorna_status_protocolos($protocolos);

            foreach($buscaProtocolos as $protocolo){
                $statusProtocolo = $this->retorna_ultimo_status_protocolo($andamentos, intval($protocolo->protocolo));
                if($statusProtocolo != ""){
                    $this->atualiza_status_protocolo($protocolo->protocolo, explode('|',$statusProtocolo)[0], explode('|',$statusProtocolo)[1], explode('|',$statusProtocolo)[2]);
                }
            }
        }
    }
}
