<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Collaborator;
use Illuminate\Http\Request;
use App\Models\Document;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Carbon;

class DocumentController extends Controller
{
  public function index(Request $r)
  {
    $where = [];

    if (!empty($r->ticket)) 
      $where[] = ['documents.id', '=', $r->ticket];

    $person = '%';
    if (!empty($r->pessoa)) {
      $person .= $r->pessoa . '%';
    }

    if (!empty($r->escrevente)) 
      $where[] = ['clerk_id', '=', $r->escrevente];

    if (!empty($r->status)) 
    $where[] = ['status', '=', $r->status];

    $docs = Document::join('clients', 'clients.id', '=', 'documents.client_id')
            ->select('documents.*')
            ->where($where)
            ->where(function ($query) use ($person) {
              $query->where('part_name', 'like', $person)
                    ->orWhere('part_cpf', 'like', $person)
                    ->orWhere('clients.name', 'like', $person)
                    ->orWhere('clients.cpf', 'like', $person);
            });

    $user = Auth::guard('admin')->user();
    if ($user->type != 'admin') {
      $settings = json_decode($user->settings, true);
      $ids = (isset($settings['manage_docs_of'])) ? $settings['manage_docs_of'] : [];
      $ids[] = $user->id;
      $docs->whereIn('clerk_id', $ids);
    }

    $docs = $docs->orderBy('documents.id', 'desc')->get();



    foreach ($docs as $doc) {
      $log = json_decode($doc->log) ?? [];
      $maxDate = $doc->updated_at;

      foreach ($log as $item) {
        if ($item->date > $maxDate && $item->by == 'client')
          $maxDate = $item->date;
      }

      $doc->last_user_update = $maxDate;
    }

    $clerks = Collaborator::where('type', 'clerk');
    if ($user->type != 'admin') {
      $settings = json_decode($user->settings, true);
      $ids = (isset($settings['manage_docs_of'])) ? $settings['manage_docs_of'] : [];
      $ids[] = $user->id;
      $clerks->whereIn('id', $ids);
    }
    $clerks = $clerks->get();

    return view('admin.documents.index', ['docs' => $docs, 'clerks' => $clerks]);
  }

  public function show($id)
  {
    $doc = Document::where('id', $id);

    $user = Auth::guard('admin')->user();
    if ($user->type != 'admin') {
      $settings = json_decode($user->settings, true);
      $ids = (isset($settings['manage_docs_of'])) ? $settings['manage_docs_of'] : [];
      $ids[] = $user->id;
      $doc->whereIn('clerk_id', $ids);
    }
    $doc = $doc->first();

    if (empty($doc)) {
      return redirect()->route('admin.documents.index');
    }

    $log = json_decode($doc->log) ?? [];

    return view('admin.documents.show', ['doc' =>  $doc, 'log' => $log]);
  }

  public function reopen($id)
  {
    $doc = Document::find($id);
    $doc->log = $this->addToLog($doc, 'reabriu o ticket.');
    $doc->status = 'opened';
    $doc->save();
    return redirect()->route('admin.documents.index');
  }

  public function drop($id)
  {
    $doc = Document::find($id);
    $doc->log = $this->addToLog($doc, 'baixou o ticket.');
    $doc->status = 'dropped';
    $doc->save();
    return redirect()->route('admin.documents.index');
  }

  public function downloadAll($id)
  {
    $doc = Document::find($id);
    $documents = json_decode($doc->documents);
    $zip_file = 'documentos-' .  $doc->id . '.zip';

    $zip = new \ZipArchive(); // TODO: Enable ext-zip extension
    $zip->open($zip_file, \ZipArchive::CREATE | \ZipArchive::OVERWRITE);

    foreach ($documents as $file) {
      $path = storage_path('app/' . $file->path);
      $name = $file->file_name;
      $zip->addFile($path, $name);
    }
    $zip->close();

    return response()->download($zip_file);
  }

  private function addToLog($doc, $msg)
  {
    $user = Auth::guard('admin')->user();
    $msg = $user->name . " " . $msg;

    $log = json_decode($doc->log);
    $log[] = [
      'date' => Carbon::now()->format('Y-m-d H:i:s'),
      'message' => $msg,
      'by' => 'collaborator'
    ];
    return json_encode($log);
  }

  public function documentDownload(Request $r)
  {
    $path = storage_path('app/' . $r->path);
    $name = $r->file_name;
    return response()->download($path, $name);
  }
}