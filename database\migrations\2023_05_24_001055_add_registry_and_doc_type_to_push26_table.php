<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('push26', function (Blueprint $table) {
          $table->string('doc_type');
          $table->text('registry');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('push26', function (Blueprint $table) {
          $table->dropColumn(['doc_type', 'registry']);
        });
    }
};
