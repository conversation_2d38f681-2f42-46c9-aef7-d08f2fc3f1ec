@extends('layouts.app')

@section('content')

<section id="conteudo" class="txtcontent"></section>
<section class="bannerDestaque" @isset($settings->banner_img->hash_name) style="background-image: url('{{ asset('storage/' . $settings->banner_img->hash_name) }}');" @endisset>

  <div class="container-fluid lingueta">

    <div class="row">

      <div class="col-lg-6 col-md-12 col-sm-12 col-xs-12">

        <p class="subTitle">Autoatendimento</p>
        <p>Encontre as informações que você precisa com agilidade e segurança pelo Autoatendimento.</p>
        <form class="uneedForm">
          <input type="text" placeholder="O que você precisa hoje?" onkeyup="searchServices()" onblur="closeResult()" class="uneedInput">
          <div class="result"><ul></ul></div>
          <input type="submit" value="&nbsp;">
        </form>

      </div>
      <div class="col-lg-2 col-lg-offset-1 col-md-6 col-sm-12 col-xs-12">
        <p class="subTitle darkBlue">Serviços</p>
        <ul class="menuBanner">
          @php
            $i = 0;
          @endphp
          @foreach ($settings->block1 as $thePage)
          @php
            $css = "";
            $target = "_self";
            if(isset($settings->{"color1" . $i})){
              $css = "color-" . $settings->{"color1" . $i};
            }
            if(isset($settings->{"bold1" . $i}) && $settings->{"bold1" . $i} == 'on'){
              $css .= " strong";
            }
            if(isset($settings->{"blank1" . $i}) && $settings->{"blank1" . $i} == 'on'){
              $target = "_blank";
            }
          @endphp
          @if ($thePage->type == 'dynamic' || $thePage->template == 'default')
            <li><a href='{{ url("/{$thePage->slug}") }}' class="@if($thePage->restricted_access) private-page @endif {{$css}}" target="{{$target}}">{{ $thePage->title }}</a></li>
          @elseif ($thePage->type == 'coded')
            <li><a href='{{ url("/{$thePage->coded_endpoint}") }}' class="@if($thePage->restricted_access) private-page @endif {{$css}}" target="{{$target}}">{{ $thePage->title }}</a></li>
          @else
            <li><a href='{{ url("/" . __('messages.slug.' . $thePage->template) . "/" . $thePage->slug) }}'class="@if($thePage->restricted_access) private-page @endif {{$css}}" target="{{$target}}">{{ $thePage->title }}</a></li>
          @endif
          @php
            $i++;
          @endphp
          @endforeach
        </ul>
        <a href="{{ url('/menu/servicos') }}" class="btnLink">Todos os serviços</a>
      </div>

      <div class="col-lg-2 col-lg-offset-1 col-md-6 col-sm-12 col-xs-12">
        <p class="subTitle darkBlue">Consulta</p>
        <ul class="menuBanner">
            @php
            $i = 0;
            @endphp
          @foreach ($settings->block2 as $thePage)
            @php
                $css = "";
                $target = "_self";
                if(isset($settings->{"color2" . $i})){
                    $css = "color-" . $settings->{"color2" . $i};
                }
                if(isset($settings->{"bold2" . $i}) && $settings->{"bold2" . $i} == 'on'){
                    $css .= " strong";
                }
                if(isset($settings->{"blank2" . $i}) && $settings->{"blank2" . $i} == 'on'){
                    $target = "_blank";
                }
            @endphp
            @if ($thePage->type == 'dynamic' || $thePage->template == 'default')
            <li><a href='{{ url("/{$thePage->slug}") }}' class="@if($thePage->restricted_access) private-page @endif {{$css}}" target="{{$target}}">{{ $thePage->title }}</a></li>
            @elseif ($thePage->type == 'coded')
            <li><a href='{{ url("/{$thePage->coded_endpoint}") }}' class="@if($thePage->restricted_access) private-page @endif {{$css}}" target="{{$target}}">{{ $thePage->title }}</a></li>
            @else
              <li><a href='{{ url("/" . __('messages.slug.' . $thePage->template) . "/" . $thePage->slug) }}' class="@if($thePage->restricted_access) private-page @endif {{$css}}" target="{{$target}}">{{ $thePage->title }}</a></li>
            @endif
            @php
                $i++;
            @endphp
          @endforeach
        </ul>
      </div>

    </div>

  </div>

</section>
{{-- https://boxicons.com/ --}}

{{--
<section class="bannerDisclaimer">
  <div class="container-fluid">
    <div class="row">
        <span class="bannerDisclaimer_desktop"><img src="img/banner-front/banner-rede-vivo-desktop.jpg" width="1090" height="120" alt="26 Notas Informa"></span>
        <span class="bannerDisclaimer_mobile"><img src="img/banner-front/banner-rede-vivo-mobile.jpg" width="365" height="200" alt="26 Notas Informa"></span>
    </div>
  </div>
</section>
--}}

<section class="atalhosNav">
  <div class="container-fluid">
    <div class="row">
      <div class="col-lg-6 col-md-12 col-sm-12 col-xs-12">
        <div class="row">
          @foreach ($settings->shortcuts as $shortcut)
            <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
              <div class="blocoAtalhoDefault">
                <div class="iconeAtalho">
                  {!! $shortcut->icon !!}
                </div>
                <div class="infoAtalho">
                  <p class="darkBlue">{{ $shortcut->title }}</p>
                  <a href="{{ $shortcut->link }}" class="btnLink">{{ $shortcut->text_link }}</a>
                </div>
              </div>
            </div>
          @endforeach
        </div>
      </div>

      <div class="col-lg-6 col-md-12 col-sm-12 col-xs-12">
        <div class="row">
          @foreach ($settings->image_shortcuts as $shortcut)
            <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
              <div class="blocoAtalhoBg" style="background-image: url('{{ asset('storage/' . $shortcut->image->hash_name) }}');">
                <p>{{ $shortcut->title }}</p>
                <a href="{{ url("{$shortcut->link}") }}" class="btnLink">{{ $shortcut->text_link }}</a>
              </div>
            </div>
            @endforeach
        </div>
      </div>
    </div>
  </div>
</section>


<section class="escrituraOnline" @isset($settings->cta->image->hash_name) style="background-image: url('{{ asset('storage/' . $settings->cta->image->hash_name) }}');" @endisset>
  <div class="container-fluid lingueta">
    <p>{!! $settings->cta->text ?? '' !!}</p>

    @empty(!$settings->cta->link)
      <a href="{{ $settings->cta->link }}" target="_blank" class="btnLink">{{ $settings->cta->text_link ?? '' }}</a>
    @endempty
  </div>
</section>

<section class="newsletter">
  <div class="container-fluid">
    <div class="row">
      <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
        <p class="titleNewsletter">Fique atualizado com as novidades do direito. <br>Assine a nossa newsletter.</p>
      </div>
      <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
        <form onsubmit="addToNewsletter(event, this)">
          <div class="line">
            <input type="email" required placeholder="Digite seu e-mail" class="newsletter">
            <input type="submit" value="ENVIAR >">
          </div>
          <div class="lineLGPD">
            <input type="checkbox" id="checklgpd" required>
            <label for="checklgpd">Li, estou de acordo com a política de privacidade e desejo receber <br>informativos do 26º Tabelionato de Notas</label>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>

<section class="previewBlog">
  <div class="container-fluid">
    <h2 class="darkBlue">Novidades</h2>
    <div class="row">
      @foreach ($news as $theNews)
        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
          <div class="blocoBlog">
            @if(!empty($theNews->photo))
              <div class="imageBlog">
                <img src="{{ asset('storage/' . $theNews->photo) }}" alt="PreviewBlog">
              </div>
            @endif
            <div class="infoBlog">
              <p>{{ $theNews->title }}</p>
              <a href="{{ route('news.show', [$theNews->slug]) }}" class="btnLink">Leia mais +</a>
            </div>
          </div>
        </div>
      @endforeach
    </div>
  </div>
</section>
@endsection
