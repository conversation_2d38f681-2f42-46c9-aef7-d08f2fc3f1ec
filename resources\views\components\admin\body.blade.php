<body>

    <!-- Layout wrapper -->
    <div class="layout-wrapper layout-content-navbar">
        <div class="layout-container">
            <!-- Menu -->

            <aside id="layout-menu" class="layout-menu menu-vertical menu bg-menu-theme">
                <div class="app-brand demo">
                    <a href="{{ url('admin') }}" class="app-brand-link">
                        <img src="{{ asset('/img/logos/logo.png') }}" alt="Peraltas">
                    </a>
                    <a href="#;" class="layout-menu-toggle menu-link text-large ms-auto d-block d-xl-none">
                        <i class="bx bx-chevron-left bx-sm align-middle"></i>
                    </a>
                </div>

                <div class="menu-inner-shadow"></div>

                <ul class="menu-inner py-1">
                    <!-- Dashboard -->
                    <li class="menu-item @if (request()->routeIs('admin')) active @endif">
                        <a href="{{ route('admin') }}" class="menu-link">
                            <i class="menu-icon tf-icons bx bx-home-circle"></i>
                            <div data-i18n="Analytics">Home</div>
                        </a>
                    </li>

                    @if (Auth::guard('admin')->user()->type == 'admin')
                        <li class="menu-item @if (request()->routeIs('news*')) active open @endif">
                            <a href="#" class="menu-link menu-toggle">
                                <i class='menu-icon bx bx-news'></i>
                                <div data-i18n="User interface">Novidades</div>
                            </a>
                            <ul class="menu-sub">
                                <li class="menu-item @if (request()->routeIs('news.categories*')) active @endif">
                                    <a href="{{ route('news.categories') }}" class="menu-link">
                                        <div data-i18n="Accordion">Categorias</div>
                                    </a>
                                </li>
                                <li class="menu-item @if (request()->routeIs('news.authors*')) active @endif">
                                    <a href="{{ route('news.authors') }}" class="menu-link">
                                        <div data-i18n="Accordion">Autores</div>
                                    </a>
                                </li>
                                <li class="menu-item @if (request()->routeIs('news.news*')) active @endif">
                                    <a href="{{ route('news.news') }}" class="menu-link">
                                        <div data-i18n="Accordion">Novidades</div>
                                    </a>
                                </li>
                                <li class="menu-item @if (request()->routeIs('news.banners*')) active @endif">
                                    <a href="{{ route('news.banners') }}" class="menu-link">
                                        <div data-i18n="Accordion">Banners</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="menu-item @if (request()->routeIs('pages*')) active open @endif">
                            <a href="#" class="menu-link menu-toggle">
                                <i class='menu-icon bx bx-file-blank'></i>
                                <div data-i18n="User interface">Páginas</div>
                            </a>
                            <ul class="menu-sub">
                                <li class="menu-item @if (request()->routeIs('pages.templates*')) active @endif">
                                    <a href="{{ route('pages.templates') }}" class="menu-link">
                                        <div data-i18n="Accordion">Modelos</div>
                                    </a>
                                </li>
                                <li class="menu-item @if (request()->routeIs('pages.dynamics*')) active @endif">
                                    <a href="{{ route('pages.dynamics') }}" class="menu-link">
                                        <div data-i18n="Accordion">Dinamicas</div>
                                    </a>
                                </li>
                                <li class="menu-item @if (request()->routeIs('pages.coded*')) active @endif">
                                    <a href="{{ route('pages.coded') }}" class="menu-link">
                                        {{-- Fixas == encoded/codificadas --}}
                                        <div data-i18n="Accordion">Fixas</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="menu-item @if (request()->routeIs('menus*')) active open @endif">
                            <a href="#" class="menu-link menu-toggle">
                                <i class='menu-icon bx bx-menu'></i>
                                <div data-i18n="User interface">Menus</div>
                            </a>
                            <ul class="menu-sub">
                                <li class="menu-item @if (request()->routeIs('menus.header*')) active @endif">
                                    <a href="{{ route('menus.header') }}" class="menu-link">
                                        <div data-i18n="Accordion">Cabeçalho</div>
                                    </a>
                                </li>
                                <li class="menu-item @if (request()->routeIs('menus.links*')) active @endif">
                                    <a href="{{ route('menus.links') }}" class="menu-link">
                                        <div data-i18n="Accordion">Links do Cabeçalho</div>
                                    </a>
                                </li>
                                <li class="menu-item @if (request()->routeIs('clientes.servicos*')) active @endif">
                                    <a href="{{ route('clientes.servicos') }}" class="menu-link">
                                        <div data-i18n="Accordion">Clientes/Restrito</div>
                                    </a>
                                </li>
                                <li class="menu-item @if (request()->routeIs('menus.footer*')) active @endif">
                                    <a href="{{ route('menus.footer') }}" class="menu-link">
                                        <div data-i18n="Accordion">Rodapé</div>
                                    </a>
                                </li>

                            </ul>
                        </li>

                        <li class="menu-item @if (request()->routeIs('questions*')) active open @endif">
                            <a href="#" class="menu-link menu-toggle">
                                <i class='menu-icon bx bx-question-mark'></i>
                                <div data-i18n="User interface">Dúvidas</div>
                            </a>
                            <ul class="menu-sub">
                                <li class="menu-item @if (request()->routeIs('questions.categories*')) active @endif">
                                    <a href="{{ route('questions.categories') }}" class="menu-link">
                                        <div data-i18n="Accordion">Categorias</div>
                                    </a>
                                </li>
                                <li class="menu-item @if (request()->routeIs('questions.q.*')) active @endif">
                                    <a href="{{ route('questions.q.index') }}" class="menu-link">
                                        <div data-i18n="Accordion">Dúvidas</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                    @endif

                    @if (Auth::guard('admin')->user()->canany(['manage_collaborators']))
                        <li class="menu-item @if (request()->routeIs('collaborators*')) active open @endif">
                            <a href="#" class="menu-link menu-toggle">
                                <i class='menu-icon bx bx-group'></i>
                                <div data-i18n="User interface">Colaboradores</div>
                            </a>
                            <ul class="menu-sub">
                                <li class="menu-item @if (request()->routeIs('collaborators.occupations*')) active @endif">
                                    <a href="{{ route('collaborators.occupations') }}" class="menu-link">
                                        <div data-i18n="Accordion">Áreas de Atuação</div>
                                    </a>
                                </li>
                                <li class="menu-item @if (request()->routeIs('collaborators.collaborators*')) active @endif">
                                    <a href="{{ route('collaborators.collaborators') }}" class="menu-link">
                                        <div data-i18n="Accordion">Colaboradores</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                    @endif

                    @if (Auth::guard('admin')->user()->can('pedidos_certidao'))
                        <li class="menu-item  @if (request()->routeIs('admin.pedidos_certidao.*')) active @endif">
                            <a href="{{ route('admin.pedidos_certidao.index') }}" class="menu-link">
                                <i class='menu-icon bx bxs-file-find'></i>
                                <div data-i18n="Analytics">Pedidos Certidão</div>
                            </a>
                        </li>
                    @endif

                    @if (Auth::guard('admin')->user()->canany(['manage_boleto', 'use_boleto']))
                        <li class="menu-item  @if (request()->routeIs('boletos.*')) active @endif">
                            <a href="{{ route('boletos.index') }}" class="menu-link">
                                <i class='menu-icon bx bxs-bank'></i>
                                <div data-i18n="Analytics">Boletos</div>
                            </a>
                        </li>
                    @endif

                    @if (Auth::guard('admin')->user()->can('use_documents'))
                        <li class="menu-item  @if (request()->routeIs('admin.documents.*')) active @endif">
                            <a href="{{ route('admin.documents.index') }}" class="menu-link">
                                <i class='menu-icon bx bxs-file-doc'></i>
                                <div data-i18n="Analytics">Documentos</div>
                            </a>
                        </li>
                    @endif

                    @if (Auth::guard('admin')->user()->can('manager_newsletter'))
                        <li class="menu-item  @if (request()->routeIs('admin.newsletter.*')) active @endif">
                            <a href="{{ route('admin.newsletter.index') }}" class="menu-link">
                                <i class='menu-icon bx bxs-news'></i>
                                <div data-i18n="Analytics">Newsletter</div>
                            </a>
                        </li>
                    @endif

                    @if (Auth::guard('admin')->user()->can('manager_formularios'))
                        <li class="menu-item  @if (request()->routeIs('admin.formularios.*')) active @endif">
                            <a href="{{ route('admin.formularios.index') }}" class="menu-link">
                                <i class='menu-icon bx bx-food-menu'></i>
                                <div data-i18n="Analytics">Formulários</div>
                            </a>
                        </li>
                    @endif

                    @if (Auth::guard('admin')->user()->can('manager_crm'))
                        <li class="menu-item  @if (request()->routeIs('admin.crm.*')) active @endif">
                            <a href="{{ route('admin.crm.index') }}" class="menu-link">
                                <i class='menu-icon bx bxs-envelope-open'></i>
                                <div data-i18n="Analytics">CRM</div>
                            </a>
                        </li>
                    @endif

                    @if (Auth::guard('admin')->user()->type == 'admin')
                        <li class="menu-item @if (request()->routeIs('settings*')) active open @endif">
                            <a href="#" class="menu-link menu-toggle">
                                <i class='menu-icon bx bx-cog'></i>
                                <div data-i18n="User interface">Configurações</div>
                            </a>
                            <ul class="menu-sub">
                                <li class="menu-item @if (request()->routeIs('settings.certificate*')) active @endif">
                                    <a href="{{ route('settings.certificate') }}" class="menu-link">
                                        <div data-i18n="Accordion">Certidão</div>
                                    </a>
                                </li>
                                <li class="menu-item @if (request()->routeIs('settings.email_traslado*')) active @endif">
                                    <a href="{{ route('settings.email_traslado') }}" class="menu-link">
                                        <div data-i18n="Accordion">E-mail Traslado</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                    @endif

                    <li class="menu-item  @if (request()->routeIs('collaborator.profile*')) active @endif">
                        <a href="{{ route('collaborator.profile') }}" class="menu-link">
                            <i class='menu-icon bx bxs-user-account'></i>
                            <div data-i18n="Analytics">Minha Conta</div>
                        </a>
                    </li>

                    <li class="menu-header small text-uppercase"><span class="menu-header-text">Deslogar</span></li>
                    <li class="menu-item">
                        <a href="{{ route('admin.logout') }}" class="menu-link">
                            <i class="menu-icon tf-icons bx bx-power-off mb-1"></i>
                            Sair
                        </a>
                    </li>

                </ul>
            </aside>
            <!-- / Menu -->



            <!-- Layout container -->
            <div class="layout-page">

                <!-- Content wrapper -->
                <div class="content-wrapper">
                    <!-- Content -->
                    <div class="container-xxl flex-grow-1 container-p-y">
                        @yield('breadcrumb')

                        @if (session('success'))
                            <div class="alert alert-primary" role="alert">
                                {{ session('success') }}
                            </div>
                        @elseif (session('error'))
                            <div class="alert alert-danger" role="alert">
                                {{ session('error') }}
                            </div>
                        @endif

                        @if ($errors->any())
                            <div class="alert alert-danger" role="alert">
                                <h5 class="text-white">Erros</h5>
                                <ul>
                                    @php
                                        $allErrors = $errors->all();
                                        $allErrors = array_unique($allErrors);
                                    @endphp
                                    @foreach ($allErrors as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
