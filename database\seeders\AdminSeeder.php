<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
      DB::table('collaborators')->insert([
        'name' => 'admin',
        'phone' => '00000000000',
        'email' => '<EMAIL>',
        'password' => Hash::make('123'),
        'type' => 'admin',
        'permissions' => '[]'
    ]);
    }
}
