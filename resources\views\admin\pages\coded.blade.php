@extends('layouts.admin')
@section('title', 'Fixas')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="fw-bold py-3 m-0">
    <span class="text-muted fw-light">Páginas / </span> Fixas
  </h4>
  <a href="{{ route('pages.coded.create') }}" type="button" class="btn btn-primary">Nova Página</a>
</div>

 <div class="card">
  <div class="card-header">
    <h5>Fixas</h5>
    <p>Obs: Páginas fixas tem seus links criados de forma manual na programação</p>
  </div>
  <div class="table-responsive text-nowrap">
    <table class="table table-hover">
      <thead>
        <tr>
          <th>Título</th>
          <th>Ações</th>
        </tr>
      </thead>
      <tbody class="table-border-bottom-0">
        @foreach ($pages as $page)
          <tr>
            <td><strong>{{ $page->title }}</strong></td>
            <td>
              <div class="dropdown">
                <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown" aria-expanded="false"><i class="bx bx-dots-vertical-rounded"></i></button>
                <div class="dropdown-menu">
                  <a class="dropdown-item" href="{{ route('pages.coded.edit', [$page->id]) }}"><i class="bx bx-edit-alt me-1"></i> Edit</a>
                  <form method="POST" action="{{ route('pages.coded.destroy', [$page->id]) }}">
                    @csrf
                    @method('delete')
                    <button class="dropdown-item" href="javascript:void(0);" onclick="return confirm('Tem certeza disso? Essa ação deletará o menu e seus submenus e links')"><i class="bx bx-trash me-1"></i> Delete</button>
                  </form>
                </div>
              </div>
            </td>
          </tr>
        @endforeach
      </tbody>
    </table>
  </div>
</div>

  <script nonce="{{ csp_nonce() }}">
    $( "#sortable" ).sortable();

    $( ".accordion-button" ).on('click', function(){
      $(".accordion-collapse").collapse('hide');
    });
  </script>
@endsection