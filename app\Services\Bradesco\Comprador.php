<?php

namespace App\Services\Bradesco;

use Illuminate\Http\Request;

class Comprador {

    private $request;
    public $nome;
    public $documento;
    public $endereco;
    public $ip;
    public $user_agent;

    public function __construct(Request $request, Endereco $endereco)
    {
        $this->request = $request;
        $this->endereco = $endereco;
        $this->ip = $this->request->getClientIp();
        $this->user_agent = $this->request->headers->get('User-Agent');
        unset($this->request);
    }

    /**
     * @param mixed $nome
     */
    public function setNome($nome)
    {
        $this->nome = $nome;
    }

    /**
     * @param mixed $documento
     */
    public function setDocumento($documento)
    {
	 $this->documento = str_replace(['.', '-', '/'], ['', '', ''], $documento);
    }
}
