(function(e, a) { for(var i in a) e[i] = a[i]; }(window, /******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = "./libs/perfect-scrollbar/perfect-scrollbar.js");
/******/ })
/************************************************************************/
/******/ ({

/***/ "./libs/perfect-scrollbar/perfect-scrollbar.js":
/*!*****************************************************!*\
  !*** ./libs/perfect-scrollbar/perfect-scrollbar.js ***!
  \*****************************************************/
/*! exports provided: PerfectScrollbar */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var perfect_scrollbar_dist_perfect_scrollbar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! perfect-scrollbar/dist/perfect-scrollbar */ \"./node_modules/perfect-scrollbar/dist/perfect-scrollbar.js\");\n/* harmony import */ var perfect_scrollbar_dist_perfect_scrollbar__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(perfect_scrollbar_dist_perfect_scrollbar__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (default from non-harmony) */ __webpack_require__.d(__webpack_exports__, \"PerfectScrollbar\", function() { return perfect_scrollbar_dist_perfect_scrollbar__WEBPACK_IMPORTED_MODULE_0___default.a; });\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9saWJzL3BlcmZlY3Qtc2Nyb2xsYmFyL3BlcmZlY3Qtc2Nyb2xsYmFyLmpzP2IyYzMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQSIsImZpbGUiOiIuL2xpYnMvcGVyZmVjdC1zY3JvbGxiYXIvcGVyZmVjdC1zY3JvbGxiYXIuanMuanMiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUGVyZmVjdFNjcm9sbGJhciBmcm9tICdwZXJmZWN0LXNjcm9sbGJhci9kaXN0L3BlcmZlY3Qtc2Nyb2xsYmFyJztcblxuZXhwb3J0IHsgUGVyZmVjdFNjcm9sbGJhciB9O1xuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./libs/perfect-scrollbar/perfect-scrollbar.js\n");

/***/ }),

/***/ "./node_modules/perfect-scrollbar/dist/perfect-scrollbar.js":
/*!******************************************************************!*\
  !*** ./node_modules/perfect-scrollbar/dist/perfect-scrollbar.js ***!
  \******************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("/*!\n * perfect-scrollbar v1.5.3\n * Copyright 2021 Hyunje Jun, MDBootstrap and Contributors\n * Licensed under MIT\n */\n\n(function (global, factory) {\n   true ? module.exports = factory() :\n  undefined;\n}(this, (function () { 'use strict';\n\n  function get(element) {\n    return getComputedStyle(element);\n  }\n\n  function set(element, obj) {\n    for (var key in obj) {\n      var val = obj[key];\n      if (typeof val === 'number') {\n        val = val + \"px\";\n      }\n      element.style[key] = val;\n    }\n    return element;\n  }\n\n  function div(className) {\n    var div = document.createElement('div');\n    div.className = className;\n    return div;\n  }\n\n  var elMatches =\n    typeof Element !== 'undefined' &&\n    (Element.prototype.matches ||\n      Element.prototype.webkitMatchesSelector ||\n      Element.prototype.mozMatchesSelector ||\n      Element.prototype.msMatchesSelector);\n\n  function matches(element, query) {\n    if (!elMatches) {\n      throw new Error('No element matching method supported');\n    }\n\n    return elMatches.call(element, query);\n  }\n\n  function remove(element) {\n    if (element.remove) {\n      element.remove();\n    } else {\n      if (element.parentNode) {\n        element.parentNode.removeChild(element);\n      }\n    }\n  }\n\n  function queryChildren(element, selector) {\n    return Array.prototype.filter.call(element.children, function (child) { return matches(child, selector); }\n    );\n  }\n\n  var cls = {\n    main: 'ps',\n    rtl: 'ps__rtl',\n    element: {\n      thumb: function (x) { return (\"ps__thumb-\" + x); },\n      rail: function (x) { return (\"ps__rail-\" + x); },\n      consuming: 'ps__child--consume',\n    },\n    state: {\n      focus: 'ps--focus',\n      clicking: 'ps--clicking',\n      active: function (x) { return (\"ps--active-\" + x); },\n      scrolling: function (x) { return (\"ps--scrolling-\" + x); },\n    },\n  };\n\n  /*\n   * Helper methods\n   */\n  var scrollingClassTimeout = { x: null, y: null };\n\n  function addScrollingClass(i, x) {\n    var classList = i.element.classList;\n    var className = cls.state.scrolling(x);\n\n    if (classList.contains(className)) {\n      clearTimeout(scrollingClassTimeout[x]);\n    } else {\n      classList.add(className);\n    }\n  }\n\n  function removeScrollingClass(i, x) {\n    scrollingClassTimeout[x] = setTimeout(\n      function () { return i.isAlive && i.element.classList.remove(cls.state.scrolling(x)); },\n      i.settings.scrollingThreshold\n    );\n  }\n\n  function setScrollingClassInstantly(i, x) {\n    addScrollingClass(i, x);\n    removeScrollingClass(i, x);\n  }\n\n  var EventElement = function EventElement(element) {\n    this.element = element;\n    this.handlers = {};\n  };\n\n  var prototypeAccessors = { isEmpty: { configurable: true } };\n\n  EventElement.prototype.bind = function bind (eventName, handler) {\n    if (typeof this.handlers[eventName] === 'undefined') {\n      this.handlers[eventName] = [];\n    }\n    this.handlers[eventName].push(handler);\n    this.element.addEventListener(eventName, handler, false);\n  };\n\n  EventElement.prototype.unbind = function unbind (eventName, target) {\n      var this$1 = this;\n\n    this.handlers[eventName] = this.handlers[eventName].filter(function (handler) {\n      if (target && handler !== target) {\n        return true;\n      }\n      this$1.element.removeEventListener(eventName, handler, false);\n      return false;\n    });\n  };\n\n  EventElement.prototype.unbindAll = function unbindAll () {\n    for (var name in this.handlers) {\n      this.unbind(name);\n    }\n  };\n\n  prototypeAccessors.isEmpty.get = function () {\n      var this$1 = this;\n\n    return Object.keys(this.handlers).every(\n      function (key) { return this$1.handlers[key].length === 0; }\n    );\n  };\n\n  Object.defineProperties( EventElement.prototype, prototypeAccessors );\n\n  var EventManager = function EventManager() {\n    this.eventElements = [];\n  };\n\n  EventManager.prototype.eventElement = function eventElement (element) {\n    var ee = this.eventElements.filter(function (ee) { return ee.element === element; })[0];\n    if (!ee) {\n      ee = new EventElement(element);\n      this.eventElements.push(ee);\n    }\n    return ee;\n  };\n\n  EventManager.prototype.bind = function bind (element, eventName, handler) {\n    this.eventElement(element).bind(eventName, handler);\n  };\n\n  EventManager.prototype.unbind = function unbind (element, eventName, handler) {\n    var ee = this.eventElement(element);\n    ee.unbind(eventName, handler);\n\n    if (ee.isEmpty) {\n      // remove\n      this.eventElements.splice(this.eventElements.indexOf(ee), 1);\n    }\n  };\n\n  EventManager.prototype.unbindAll = function unbindAll () {\n    this.eventElements.forEach(function (e) { return e.unbindAll(); });\n    this.eventElements = [];\n  };\n\n  EventManager.prototype.once = function once (element, eventName, handler) {\n    var ee = this.eventElement(element);\n    var onceHandler = function (evt) {\n      ee.unbind(eventName, onceHandler);\n      handler(evt);\n    };\n    ee.bind(eventName, onceHandler);\n  };\n\n  function createEvent(name) {\n    if (typeof window.CustomEvent === 'function') {\n      return new CustomEvent(name);\n    } else {\n      var evt = document.createEvent('CustomEvent');\n      evt.initCustomEvent(name, false, false, undefined);\n      return evt;\n    }\n  }\n\n  function processScrollDiff(\n    i,\n    axis,\n    diff,\n    useScrollingClass,\n    forceFireReachEvent\n  ) {\n    if ( useScrollingClass === void 0 ) useScrollingClass = true;\n    if ( forceFireReachEvent === void 0 ) forceFireReachEvent = false;\n\n    var fields;\n    if (axis === 'top') {\n      fields = [\n        'contentHeight',\n        'containerHeight',\n        'scrollTop',\n        'y',\n        'up',\n        'down' ];\n    } else if (axis === 'left') {\n      fields = [\n        'contentWidth',\n        'containerWidth',\n        'scrollLeft',\n        'x',\n        'left',\n        'right' ];\n    } else {\n      throw new Error('A proper axis should be provided');\n    }\n\n    processScrollDiff$1(i, diff, fields, useScrollingClass, forceFireReachEvent);\n  }\n\n  function processScrollDiff$1(\n    i,\n    diff,\n    ref,\n    useScrollingClass,\n    forceFireReachEvent\n  ) {\n    var contentHeight = ref[0];\n    var containerHeight = ref[1];\n    var scrollTop = ref[2];\n    var y = ref[3];\n    var up = ref[4];\n    var down = ref[5];\n    if ( useScrollingClass === void 0 ) useScrollingClass = true;\n    if ( forceFireReachEvent === void 0 ) forceFireReachEvent = false;\n\n    var element = i.element;\n\n    // reset reach\n    i.reach[y] = null;\n\n    // 1 for subpixel rounding\n    if (element[scrollTop] < 1) {\n      i.reach[y] = 'start';\n    }\n\n    // 1 for subpixel rounding\n    if (element[scrollTop] > i[contentHeight] - i[containerHeight] - 1) {\n      i.reach[y] = 'end';\n    }\n\n    if (diff) {\n      element.dispatchEvent(createEvent((\"ps-scroll-\" + y)));\n\n      if (diff < 0) {\n        element.dispatchEvent(createEvent((\"ps-scroll-\" + up)));\n      } else if (diff > 0) {\n        element.dispatchEvent(createEvent((\"ps-scroll-\" + down)));\n      }\n\n      if (useScrollingClass) {\n        setScrollingClassInstantly(i, y);\n      }\n    }\n\n    if (i.reach[y] && (diff || forceFireReachEvent)) {\n      element.dispatchEvent(createEvent((\"ps-\" + y + \"-reach-\" + (i.reach[y]))));\n    }\n  }\n\n  function toInt(x) {\n    return parseInt(x, 10) || 0;\n  }\n\n  function isEditable(el) {\n    return (\n      matches(el, 'input,[contenteditable]') ||\n      matches(el, 'select,[contenteditable]') ||\n      matches(el, 'textarea,[contenteditable]') ||\n      matches(el, 'button,[contenteditable]')\n    );\n  }\n\n  function outerWidth(element) {\n    var styles = get(element);\n    return (\n      toInt(styles.width) +\n      toInt(styles.paddingLeft) +\n      toInt(styles.paddingRight) +\n      toInt(styles.borderLeftWidth) +\n      toInt(styles.borderRightWidth)\n    );\n  }\n\n  var env = {\n    isWebKit:\n      typeof document !== 'undefined' &&\n      'WebkitAppearance' in document.documentElement.style,\n    supportsTouch:\n      typeof window !== 'undefined' &&\n      ('ontouchstart' in window ||\n        ('maxTouchPoints' in window.navigator &&\n          window.navigator.maxTouchPoints > 0) ||\n        (window.DocumentTouch && document instanceof window.DocumentTouch)),\n    supportsIePointer:\n      typeof navigator !== 'undefined' && navigator.msMaxTouchPoints,\n    isChrome:\n      typeof navigator !== 'undefined' &&\n      /Chrome/i.test(navigator && navigator.userAgent),\n  };\n\n  function updateGeometry(i) {\n    var element = i.element;\n    var roundedScrollTop = Math.floor(element.scrollTop);\n    var rect = element.getBoundingClientRect();\n\n    i.containerWidth = Math.round(rect.width);\n    i.containerHeight = Math.round(rect.height);\n\n    i.contentWidth = element.scrollWidth;\n    i.contentHeight = element.scrollHeight;\n\n    if (!element.contains(i.scrollbarXRail)) {\n      // clean up and append\n      queryChildren(element, cls.element.rail('x')).forEach(function (el) { return remove(el); }\n      );\n      element.appendChild(i.scrollbarXRail);\n    }\n    if (!element.contains(i.scrollbarYRail)) {\n      // clean up and append\n      queryChildren(element, cls.element.rail('y')).forEach(function (el) { return remove(el); }\n      );\n      element.appendChild(i.scrollbarYRail);\n    }\n\n    if (\n      !i.settings.suppressScrollX &&\n      i.containerWidth + i.settings.scrollXMarginOffset < i.contentWidth\n    ) {\n      i.scrollbarXActive = true;\n      i.railXWidth = i.containerWidth - i.railXMarginWidth;\n      i.railXRatio = i.containerWidth / i.railXWidth;\n      i.scrollbarXWidth = getThumbSize(\n        i,\n        toInt((i.railXWidth * i.containerWidth) / i.contentWidth)\n      );\n      i.scrollbarXLeft = toInt(\n        ((i.negativeScrollAdjustment + element.scrollLeft) *\n          (i.railXWidth - i.scrollbarXWidth)) /\n          (i.contentWidth - i.containerWidth)\n      );\n    } else {\n      i.scrollbarXActive = false;\n    }\n\n    if (\n      !i.settings.suppressScrollY &&\n      i.containerHeight + i.settings.scrollYMarginOffset < i.contentHeight\n    ) {\n      i.scrollbarYActive = true;\n      i.railYHeight = i.containerHeight - i.railYMarginHeight;\n      i.railYRatio = i.containerHeight / i.railYHeight;\n      i.scrollbarYHeight = getThumbSize(\n        i,\n        toInt((i.railYHeight * i.containerHeight) / i.contentHeight)\n      );\n      i.scrollbarYTop = toInt(\n        (roundedScrollTop * (i.railYHeight - i.scrollbarYHeight)) /\n          (i.contentHeight - i.containerHeight)\n      );\n    } else {\n      i.scrollbarYActive = false;\n    }\n\n    if (i.scrollbarXLeft >= i.railXWidth - i.scrollbarXWidth) {\n      i.scrollbarXLeft = i.railXWidth - i.scrollbarXWidth;\n    }\n    if (i.scrollbarYTop >= i.railYHeight - i.scrollbarYHeight) {\n      i.scrollbarYTop = i.railYHeight - i.scrollbarYHeight;\n    }\n\n    updateCss(element, i);\n\n    if (i.scrollbarXActive) {\n      element.classList.add(cls.state.active('x'));\n    } else {\n      element.classList.remove(cls.state.active('x'));\n      i.scrollbarXWidth = 0;\n      i.scrollbarXLeft = 0;\n      element.scrollLeft = i.isRtl === true ? i.contentWidth : 0;\n    }\n    if (i.scrollbarYActive) {\n      element.classList.add(cls.state.active('y'));\n    } else {\n      element.classList.remove(cls.state.active('y'));\n      i.scrollbarYHeight = 0;\n      i.scrollbarYTop = 0;\n      element.scrollTop = 0;\n    }\n  }\n\n  function getThumbSize(i, thumbSize) {\n    if (i.settings.minScrollbarLength) {\n      thumbSize = Math.max(thumbSize, i.settings.minScrollbarLength);\n    }\n    if (i.settings.maxScrollbarLength) {\n      thumbSize = Math.min(thumbSize, i.settings.maxScrollbarLength);\n    }\n    return thumbSize;\n  }\n\n  function updateCss(element, i) {\n    var xRailOffset = { width: i.railXWidth };\n    var roundedScrollTop = Math.floor(element.scrollTop);\n\n    if (i.isRtl) {\n      xRailOffset.left =\n        i.negativeScrollAdjustment +\n        element.scrollLeft +\n        i.containerWidth -\n        i.contentWidth;\n    } else {\n      xRailOffset.left = element.scrollLeft;\n    }\n    if (i.isScrollbarXUsingBottom) {\n      xRailOffset.bottom = i.scrollbarXBottom - roundedScrollTop;\n    } else {\n      xRailOffset.top = i.scrollbarXTop + roundedScrollTop;\n    }\n    set(i.scrollbarXRail, xRailOffset);\n\n    var yRailOffset = { top: roundedScrollTop, height: i.railYHeight };\n    if (i.isScrollbarYUsingRight) {\n      if (i.isRtl) {\n        yRailOffset.right =\n          i.contentWidth -\n          (i.negativeScrollAdjustment + element.scrollLeft) -\n          i.scrollbarYRight -\n          i.scrollbarYOuterWidth -\n          9;\n      } else {\n        yRailOffset.right = i.scrollbarYRight - element.scrollLeft;\n      }\n    } else {\n      if (i.isRtl) {\n        yRailOffset.left =\n          i.negativeScrollAdjustment +\n          element.scrollLeft +\n          i.containerWidth * 2 -\n          i.contentWidth -\n          i.scrollbarYLeft -\n          i.scrollbarYOuterWidth;\n      } else {\n        yRailOffset.left = i.scrollbarYLeft + element.scrollLeft;\n      }\n    }\n    set(i.scrollbarYRail, yRailOffset);\n\n    set(i.scrollbarX, {\n      left: i.scrollbarXLeft,\n      width: i.scrollbarXWidth - i.railBorderXWidth,\n    });\n    set(i.scrollbarY, {\n      top: i.scrollbarYTop,\n      height: i.scrollbarYHeight - i.railBorderYWidth,\n    });\n  }\n\n  function clickRail(i) {\n    var element = i.element;\n\n    i.event.bind(i.scrollbarY, 'mousedown', function (e) { return e.stopPropagation(); });\n    i.event.bind(i.scrollbarYRail, 'mousedown', function (e) {\n      var positionTop =\n        e.pageY -\n        window.pageYOffset -\n        i.scrollbarYRail.getBoundingClientRect().top;\n      var direction = positionTop > i.scrollbarYTop ? 1 : -1;\n\n      i.element.scrollTop += direction * i.containerHeight;\n      updateGeometry(i);\n\n      e.stopPropagation();\n    });\n\n    i.event.bind(i.scrollbarX, 'mousedown', function (e) { return e.stopPropagation(); });\n    i.event.bind(i.scrollbarXRail, 'mousedown', function (e) {\n      var positionLeft =\n        e.pageX -\n        window.pageXOffset -\n        i.scrollbarXRail.getBoundingClientRect().left;\n      var direction = positionLeft > i.scrollbarXLeft ? 1 : -1;\n\n      i.element.scrollLeft += direction * i.containerWidth;\n      updateGeometry(i);\n\n      e.stopPropagation();\n    });\n  }\n\n  function dragThumb(i) {\n    bindMouseScrollHandler(i, [\n      'containerWidth',\n      'contentWidth',\n      'pageX',\n      'railXWidth',\n      'scrollbarX',\n      'scrollbarXWidth',\n      'scrollLeft',\n      'x',\n      'scrollbarXRail' ]);\n    bindMouseScrollHandler(i, [\n      'containerHeight',\n      'contentHeight',\n      'pageY',\n      'railYHeight',\n      'scrollbarY',\n      'scrollbarYHeight',\n      'scrollTop',\n      'y',\n      'scrollbarYRail' ]);\n  }\n\n  function bindMouseScrollHandler(\n    i,\n    ref\n  ) {\n    var containerHeight = ref[0];\n    var contentHeight = ref[1];\n    var pageY = ref[2];\n    var railYHeight = ref[3];\n    var scrollbarY = ref[4];\n    var scrollbarYHeight = ref[5];\n    var scrollTop = ref[6];\n    var y = ref[7];\n    var scrollbarYRail = ref[8];\n\n    var element = i.element;\n\n    var startingScrollTop = null;\n    var startingMousePageY = null;\n    var scrollBy = null;\n\n    function mouseMoveHandler(e) {\n      if (e.touches && e.touches[0]) {\n        e[pageY] = e.touches[0].pageY;\n      }\n      element[scrollTop] =\n        startingScrollTop + scrollBy * (e[pageY] - startingMousePageY);\n      addScrollingClass(i, y);\n      updateGeometry(i);\n\n      e.stopPropagation();\n      if (e.type.startsWith('touch') && e.changedTouches.length > 1) {\n        e.preventDefault();\n      }\n    }\n\n    function mouseUpHandler() {\n      removeScrollingClass(i, y);\n      i[scrollbarYRail].classList.remove(cls.state.clicking);\n      i.event.unbind(i.ownerDocument, 'mousemove', mouseMoveHandler);\n    }\n\n    function bindMoves(e, touchMode) {\n      startingScrollTop = element[scrollTop];\n      if (touchMode && e.touches) {\n        e[pageY] = e.touches[0].pageY;\n      }\n      startingMousePageY = e[pageY];\n      scrollBy =\n        (i[contentHeight] - i[containerHeight]) /\n        (i[railYHeight] - i[scrollbarYHeight]);\n      if (!touchMode) {\n        i.event.bind(i.ownerDocument, 'mousemove', mouseMoveHandler);\n        i.event.once(i.ownerDocument, 'mouseup', mouseUpHandler);\n        e.preventDefault();\n      } else {\n        i.event.bind(i.ownerDocument, 'touchmove', mouseMoveHandler);\n      }\n\n      i[scrollbarYRail].classList.add(cls.state.clicking);\n\n      e.stopPropagation();\n    }\n\n    i.event.bind(i[scrollbarY], 'mousedown', function (e) {\n      bindMoves(e);\n    });\n    i.event.bind(i[scrollbarY], 'touchstart', function (e) {\n      bindMoves(e, true);\n    });\n  }\n\n  function keyboard(i) {\n    var element = i.element;\n\n    var elementHovered = function () { return matches(element, ':hover'); };\n    var scrollbarFocused = function () { return matches(i.scrollbarX, ':focus') || matches(i.scrollbarY, ':focus'); };\n\n    function shouldPreventDefault(deltaX, deltaY) {\n      var scrollTop = Math.floor(element.scrollTop);\n      if (deltaX === 0) {\n        if (!i.scrollbarYActive) {\n          return false;\n        }\n        if (\n          (scrollTop === 0 && deltaY > 0) ||\n          (scrollTop >= i.contentHeight - i.containerHeight && deltaY < 0)\n        ) {\n          return !i.settings.wheelPropagation;\n        }\n      }\n\n      var scrollLeft = element.scrollLeft;\n      if (deltaY === 0) {\n        if (!i.scrollbarXActive) {\n          return false;\n        }\n        if (\n          (scrollLeft === 0 && deltaX < 0) ||\n          (scrollLeft >= i.contentWidth - i.containerWidth && deltaX > 0)\n        ) {\n          return !i.settings.wheelPropagation;\n        }\n      }\n      return true;\n    }\n\n    i.event.bind(i.ownerDocument, 'keydown', function (e) {\n      if (\n        (e.isDefaultPrevented && e.isDefaultPrevented()) ||\n        e.defaultPrevented\n      ) {\n        return;\n      }\n\n      if (!elementHovered() && !scrollbarFocused()) {\n        return;\n      }\n\n      var activeElement = document.activeElement\n        ? document.activeElement\n        : i.ownerDocument.activeElement;\n      if (activeElement) {\n        if (activeElement.tagName === 'IFRAME') {\n          activeElement = activeElement.contentDocument.activeElement;\n        } else {\n          // go deeper if element is a webcomponent\n          while (activeElement.shadowRoot) {\n            activeElement = activeElement.shadowRoot.activeElement;\n          }\n        }\n        if (isEditable(activeElement)) {\n          return;\n        }\n      }\n\n      var deltaX = 0;\n      var deltaY = 0;\n\n      switch (e.which) {\n        case 37: // left\n          if (e.metaKey) {\n            deltaX = -i.contentWidth;\n          } else if (e.altKey) {\n            deltaX = -i.containerWidth;\n          } else {\n            deltaX = -30;\n          }\n          break;\n        case 38: // up\n          if (e.metaKey) {\n            deltaY = i.contentHeight;\n          } else if (e.altKey) {\n            deltaY = i.containerHeight;\n          } else {\n            deltaY = 30;\n          }\n          break;\n        case 39: // right\n          if (e.metaKey) {\n            deltaX = i.contentWidth;\n          } else if (e.altKey) {\n            deltaX = i.containerWidth;\n          } else {\n            deltaX = 30;\n          }\n          break;\n        case 40: // down\n          if (e.metaKey) {\n            deltaY = -i.contentHeight;\n          } else if (e.altKey) {\n            deltaY = -i.containerHeight;\n          } else {\n            deltaY = -30;\n          }\n          break;\n        case 32: // space bar\n          if (e.shiftKey) {\n            deltaY = i.containerHeight;\n          } else {\n            deltaY = -i.containerHeight;\n          }\n          break;\n        case 33: // page up\n          deltaY = i.containerHeight;\n          break;\n        case 34: // page down\n          deltaY = -i.containerHeight;\n          break;\n        case 36: // home\n          deltaY = i.contentHeight;\n          break;\n        case 35: // end\n          deltaY = -i.contentHeight;\n          break;\n        default:\n          return;\n      }\n\n      if (i.settings.suppressScrollX && deltaX !== 0) {\n        return;\n      }\n      if (i.settings.suppressScrollY && deltaY !== 0) {\n        return;\n      }\n\n      element.scrollTop -= deltaY;\n      element.scrollLeft += deltaX;\n      updateGeometry(i);\n\n      if (shouldPreventDefault(deltaX, deltaY)) {\n        e.preventDefault();\n      }\n    });\n  }\n\n  function wheel(i) {\n    var element = i.element;\n\n    function shouldPreventDefault(deltaX, deltaY) {\n      var roundedScrollTop = Math.floor(element.scrollTop);\n      var isTop = element.scrollTop === 0;\n      var isBottom =\n        roundedScrollTop + element.offsetHeight === element.scrollHeight;\n      var isLeft = element.scrollLeft === 0;\n      var isRight =\n        element.scrollLeft + element.offsetWidth === element.scrollWidth;\n\n      var hitsBound;\n\n      // pick axis with primary direction\n      if (Math.abs(deltaY) > Math.abs(deltaX)) {\n        hitsBound = isTop || isBottom;\n      } else {\n        hitsBound = isLeft || isRight;\n      }\n\n      return hitsBound ? !i.settings.wheelPropagation : true;\n    }\n\n    function getDeltaFromEvent(e) {\n      var deltaX = e.deltaX;\n      var deltaY = -1 * e.deltaY;\n\n      if (typeof deltaX === 'undefined' || typeof deltaY === 'undefined') {\n        // OS X Safari\n        deltaX = (-1 * e.wheelDeltaX) / 6;\n        deltaY = e.wheelDeltaY / 6;\n      }\n\n      if (e.deltaMode && e.deltaMode === 1) {\n        // Firefox in deltaMode 1: Line scrolling\n        deltaX *= 10;\n        deltaY *= 10;\n      }\n\n      if (deltaX !== deltaX && deltaY !== deltaY /* NaN checks */) {\n        // IE in some mouse drivers\n        deltaX = 0;\n        deltaY = e.wheelDelta;\n      }\n\n      if (e.shiftKey) {\n        // reverse axis with shift key\n        return [-deltaY, -deltaX];\n      }\n      return [deltaX, deltaY];\n    }\n\n    function shouldBeConsumedByChild(target, deltaX, deltaY) {\n      // FIXME: this is a workaround for <select> issue in FF and IE #571\n      if (!env.isWebKit && element.querySelector('select:focus')) {\n        return true;\n      }\n\n      if (!element.contains(target)) {\n        return false;\n      }\n\n      var cursor = target;\n\n      while (cursor && cursor !== element) {\n        if (cursor.classList.contains(cls.element.consuming)) {\n          return true;\n        }\n\n        var style = get(cursor);\n\n        // if deltaY && vertical scrollable\n        if (deltaY && style.overflowY.match(/(scroll|auto)/)) {\n          var maxScrollTop = cursor.scrollHeight - cursor.clientHeight;\n          if (maxScrollTop > 0) {\n            if (\n              (cursor.scrollTop > 0 && deltaY < 0) ||\n              (cursor.scrollTop < maxScrollTop && deltaY > 0)\n            ) {\n              return true;\n            }\n          }\n        }\n        // if deltaX && horizontal scrollable\n        if (deltaX && style.overflowX.match(/(scroll|auto)/)) {\n          var maxScrollLeft = cursor.scrollWidth - cursor.clientWidth;\n          if (maxScrollLeft > 0) {\n            if (\n              (cursor.scrollLeft > 0 && deltaX < 0) ||\n              (cursor.scrollLeft < maxScrollLeft && deltaX > 0)\n            ) {\n              return true;\n            }\n          }\n        }\n\n        cursor = cursor.parentNode;\n      }\n\n      return false;\n    }\n\n    function mousewheelHandler(e) {\n      var ref = getDeltaFromEvent(e);\n      var deltaX = ref[0];\n      var deltaY = ref[1];\n\n      if (shouldBeConsumedByChild(e.target, deltaX, deltaY)) {\n        return;\n      }\n\n      var shouldPrevent = false;\n      if (!i.settings.useBothWheelAxes) {\n        // deltaX will only be used for horizontal scrolling and deltaY will\n        // only be used for vertical scrolling - this is the default\n        element.scrollTop -= deltaY * i.settings.wheelSpeed;\n        element.scrollLeft += deltaX * i.settings.wheelSpeed;\n      } else if (i.scrollbarYActive && !i.scrollbarXActive) {\n        // only vertical scrollbar is active and useBothWheelAxes option is\n        // active, so let's scroll vertical bar using both mouse wheel axes\n        if (deltaY) {\n          element.scrollTop -= deltaY * i.settings.wheelSpeed;\n        } else {\n          element.scrollTop += deltaX * i.settings.wheelSpeed;\n        }\n        shouldPrevent = true;\n      } else if (i.scrollbarXActive && !i.scrollbarYActive) {\n        // useBothWheelAxes and only horizontal bar is active, so use both\n        // wheel axes for horizontal bar\n        if (deltaX) {\n          element.scrollLeft += deltaX * i.settings.wheelSpeed;\n        } else {\n          element.scrollLeft -= deltaY * i.settings.wheelSpeed;\n        }\n        shouldPrevent = true;\n      }\n\n      updateGeometry(i);\n\n      shouldPrevent = shouldPrevent || shouldPreventDefault(deltaX, deltaY);\n      if (shouldPrevent && !e.ctrlKey) {\n        e.stopPropagation();\n        e.preventDefault();\n      }\n    }\n\n    if (typeof window.onwheel !== 'undefined') {\n      i.event.bind(element, 'wheel', mousewheelHandler);\n    } else if (typeof window.onmousewheel !== 'undefined') {\n      i.event.bind(element, 'mousewheel', mousewheelHandler);\n    }\n  }\n\n  function touch(i) {\n    if (!env.supportsTouch && !env.supportsIePointer) {\n      return;\n    }\n\n    var element = i.element;\n\n    function shouldPrevent(deltaX, deltaY) {\n      var scrollTop = Math.floor(element.scrollTop);\n      var scrollLeft = element.scrollLeft;\n      var magnitudeX = Math.abs(deltaX);\n      var magnitudeY = Math.abs(deltaY);\n\n      if (magnitudeY > magnitudeX) {\n        // user is perhaps trying to swipe up/down the page\n\n        if (\n          (deltaY < 0 && scrollTop === i.contentHeight - i.containerHeight) ||\n          (deltaY > 0 && scrollTop === 0)\n        ) {\n          // set prevent for mobile Chrome refresh\n          return window.scrollY === 0 && deltaY > 0 && env.isChrome;\n        }\n      } else if (magnitudeX > magnitudeY) {\n        // user is perhaps trying to swipe left/right across the page\n\n        if (\n          (deltaX < 0 && scrollLeft === i.contentWidth - i.containerWidth) ||\n          (deltaX > 0 && scrollLeft === 0)\n        ) {\n          return true;\n        }\n      }\n\n      return true;\n    }\n\n    function applyTouchMove(differenceX, differenceY) {\n      element.scrollTop -= differenceY;\n      element.scrollLeft -= differenceX;\n\n      updateGeometry(i);\n    }\n\n    var startOffset = {};\n    var startTime = 0;\n    var speed = {};\n    var easingLoop = null;\n\n    function getTouch(e) {\n      if (e.targetTouches) {\n        return e.targetTouches[0];\n      } else {\n        // Maybe IE pointer\n        return e;\n      }\n    }\n\n    function shouldHandle(e) {\n      if (e.pointerType && e.pointerType === 'pen' && e.buttons === 0) {\n        return false;\n      }\n      if (e.targetTouches && e.targetTouches.length === 1) {\n        return true;\n      }\n      if (\n        e.pointerType &&\n        e.pointerType !== 'mouse' &&\n        e.pointerType !== e.MSPOINTER_TYPE_MOUSE\n      ) {\n        return true;\n      }\n      return false;\n    }\n\n    function touchStart(e) {\n      if (!shouldHandle(e)) {\n        return;\n      }\n\n      var touch = getTouch(e);\n\n      startOffset.pageX = touch.pageX;\n      startOffset.pageY = touch.pageY;\n\n      startTime = new Date().getTime();\n\n      if (easingLoop !== null) {\n        clearInterval(easingLoop);\n      }\n    }\n\n    function shouldBeConsumedByChild(target, deltaX, deltaY) {\n      if (!element.contains(target)) {\n        return false;\n      }\n\n      var cursor = target;\n\n      while (cursor && cursor !== element) {\n        if (cursor.classList.contains(cls.element.consuming)) {\n          return true;\n        }\n\n        var style = get(cursor);\n\n        // if deltaY && vertical scrollable\n        if (deltaY && style.overflowY.match(/(scroll|auto)/)) {\n          var maxScrollTop = cursor.scrollHeight - cursor.clientHeight;\n          if (maxScrollTop > 0) {\n            if (\n              (cursor.scrollTop > 0 && deltaY < 0) ||\n              (cursor.scrollTop < maxScrollTop && deltaY > 0)\n            ) {\n              return true;\n            }\n          }\n        }\n        // if deltaX && horizontal scrollable\n        if (deltaX && style.overflowX.match(/(scroll|auto)/)) {\n          var maxScrollLeft = cursor.scrollWidth - cursor.clientWidth;\n          if (maxScrollLeft > 0) {\n            if (\n              (cursor.scrollLeft > 0 && deltaX < 0) ||\n              (cursor.scrollLeft < maxScrollLeft && deltaX > 0)\n            ) {\n              return true;\n            }\n          }\n        }\n\n        cursor = cursor.parentNode;\n      }\n\n      return false;\n    }\n\n    function touchMove(e) {\n      if (shouldHandle(e)) {\n        var touch = getTouch(e);\n\n        var currentOffset = { pageX: touch.pageX, pageY: touch.pageY };\n\n        var differenceX = currentOffset.pageX - startOffset.pageX;\n        var differenceY = currentOffset.pageY - startOffset.pageY;\n\n        if (shouldBeConsumedByChild(e.target, differenceX, differenceY)) {\n          return;\n        }\n\n        applyTouchMove(differenceX, differenceY);\n        startOffset = currentOffset;\n\n        var currentTime = new Date().getTime();\n\n        var timeGap = currentTime - startTime;\n        if (timeGap > 0) {\n          speed.x = differenceX / timeGap;\n          speed.y = differenceY / timeGap;\n          startTime = currentTime;\n        }\n\n        if (shouldPrevent(differenceX, differenceY)) {\n          e.preventDefault();\n        }\n      }\n    }\n    function touchEnd() {\n      if (i.settings.swipeEasing) {\n        clearInterval(easingLoop);\n        easingLoop = setInterval(function() {\n          if (i.isInitialized) {\n            clearInterval(easingLoop);\n            return;\n          }\n\n          if (!speed.x && !speed.y) {\n            clearInterval(easingLoop);\n            return;\n          }\n\n          if (Math.abs(speed.x) < 0.01 && Math.abs(speed.y) < 0.01) {\n            clearInterval(easingLoop);\n            return;\n          }\n\n          if (!i.element) {\n            clearInterval(easingLoop);\n            return;\n          }\n\n          applyTouchMove(speed.x * 30, speed.y * 30);\n\n          speed.x *= 0.8;\n          speed.y *= 0.8;\n        }, 10);\n      }\n    }\n\n    if (env.supportsTouch) {\n      i.event.bind(element, 'touchstart', touchStart);\n      i.event.bind(element, 'touchmove', touchMove);\n      i.event.bind(element, 'touchend', touchEnd);\n    } else if (env.supportsIePointer) {\n      if (window.PointerEvent) {\n        i.event.bind(element, 'pointerdown', touchStart);\n        i.event.bind(element, 'pointermove', touchMove);\n        i.event.bind(element, 'pointerup', touchEnd);\n      } else if (window.MSPointerEvent) {\n        i.event.bind(element, 'MSPointerDown', touchStart);\n        i.event.bind(element, 'MSPointerMove', touchMove);\n        i.event.bind(element, 'MSPointerUp', touchEnd);\n      }\n    }\n  }\n\n  var defaultSettings = function () { return ({\n    handlers: ['click-rail', 'drag-thumb', 'keyboard', 'wheel', 'touch'],\n    maxScrollbarLength: null,\n    minScrollbarLength: null,\n    scrollingThreshold: 1000,\n    scrollXMarginOffset: 0,\n    scrollYMarginOffset: 0,\n    suppressScrollX: false,\n    suppressScrollY: false,\n    swipeEasing: true,\n    useBothWheelAxes: false,\n    wheelPropagation: true,\n    wheelSpeed: 1,\n  }); };\n\n  var handlers = {\n    'click-rail': clickRail,\n    'drag-thumb': dragThumb,\n    keyboard: keyboard,\n    wheel: wheel,\n    touch: touch,\n  };\n\n  var PerfectScrollbar = function PerfectScrollbar(element, userSettings) {\n    var this$1 = this;\n    if ( userSettings === void 0 ) userSettings = {};\n\n    if (typeof element === 'string') {\n      element = document.querySelector(element);\n    }\n\n    if (!element || !element.nodeName) {\n      throw new Error('no element is specified to initialize PerfectScrollbar');\n    }\n\n    this.element = element;\n\n    element.classList.add(cls.main);\n\n    this.settings = defaultSettings();\n    for (var key in userSettings) {\n      this.settings[key] = userSettings[key];\n    }\n\n    this.containerWidth = null;\n    this.containerHeight = null;\n    this.contentWidth = null;\n    this.contentHeight = null;\n\n    var focus = function () { return element.classList.add(cls.state.focus); };\n    var blur = function () { return element.classList.remove(cls.state.focus); };\n\n    this.isRtl = get(element).direction === 'rtl';\n    if (this.isRtl === true) {\n      element.classList.add(cls.rtl);\n    }\n    this.isNegativeScroll = (function () {\n      var originalScrollLeft = element.scrollLeft;\n      var result = null;\n      element.scrollLeft = -1;\n      result = element.scrollLeft < 0;\n      element.scrollLeft = originalScrollLeft;\n      return result;\n    })();\n    this.negativeScrollAdjustment = this.isNegativeScroll\n      ? element.scrollWidth - element.clientWidth\n      : 0;\n    this.event = new EventManager();\n    this.ownerDocument = element.ownerDocument || document;\n\n    this.scrollbarXRail = div(cls.element.rail('x'));\n    element.appendChild(this.scrollbarXRail);\n    this.scrollbarX = div(cls.element.thumb('x'));\n    this.scrollbarXRail.appendChild(this.scrollbarX);\n    this.scrollbarX.setAttribute('tabindex', 0);\n    this.event.bind(this.scrollbarX, 'focus', focus);\n    this.event.bind(this.scrollbarX, 'blur', blur);\n    this.scrollbarXActive = null;\n    this.scrollbarXWidth = null;\n    this.scrollbarXLeft = null;\n    var railXStyle = get(this.scrollbarXRail);\n    this.scrollbarXBottom = parseInt(railXStyle.bottom, 10);\n    if (isNaN(this.scrollbarXBottom)) {\n      this.isScrollbarXUsingBottom = false;\n      this.scrollbarXTop = toInt(railXStyle.top);\n    } else {\n      this.isScrollbarXUsingBottom = true;\n    }\n    this.railBorderXWidth =\n      toInt(railXStyle.borderLeftWidth) + toInt(railXStyle.borderRightWidth);\n    // Set rail to display:block to calculate margins\n    set(this.scrollbarXRail, { display: 'block' });\n    this.railXMarginWidth =\n      toInt(railXStyle.marginLeft) + toInt(railXStyle.marginRight);\n    set(this.scrollbarXRail, { display: '' });\n    this.railXWidth = null;\n    this.railXRatio = null;\n\n    this.scrollbarYRail = div(cls.element.rail('y'));\n    element.appendChild(this.scrollbarYRail);\n    this.scrollbarY = div(cls.element.thumb('y'));\n    this.scrollbarYRail.appendChild(this.scrollbarY);\n    this.scrollbarY.setAttribute('tabindex', 0);\n    this.event.bind(this.scrollbarY, 'focus', focus);\n    this.event.bind(this.scrollbarY, 'blur', blur);\n    this.scrollbarYActive = null;\n    this.scrollbarYHeight = null;\n    this.scrollbarYTop = null;\n    var railYStyle = get(this.scrollbarYRail);\n    this.scrollbarYRight = parseInt(railYStyle.right, 10);\n    if (isNaN(this.scrollbarYRight)) {\n      this.isScrollbarYUsingRight = false;\n      this.scrollbarYLeft = toInt(railYStyle.left);\n    } else {\n      this.isScrollbarYUsingRight = true;\n    }\n    this.scrollbarYOuterWidth = this.isRtl ? outerWidth(this.scrollbarY) : null;\n    this.railBorderYWidth =\n      toInt(railYStyle.borderTopWidth) + toInt(railYStyle.borderBottomWidth);\n    set(this.scrollbarYRail, { display: 'block' });\n    this.railYMarginHeight =\n      toInt(railYStyle.marginTop) + toInt(railYStyle.marginBottom);\n    set(this.scrollbarYRail, { display: '' });\n    this.railYHeight = null;\n    this.railYRatio = null;\n\n    this.reach = {\n      x:\n        element.scrollLeft <= 0\n          ? 'start'\n          : element.scrollLeft >= this.contentWidth - this.containerWidth\n          ? 'end'\n          : null,\n      y:\n        element.scrollTop <= 0\n          ? 'start'\n          : element.scrollTop >= this.contentHeight - this.containerHeight\n          ? 'end'\n          : null,\n    };\n\n    this.isAlive = true;\n\n    this.settings.handlers.forEach(function (handlerName) { return handlers[handlerName](this$1); });\n\n    this.lastScrollTop = Math.floor(element.scrollTop); // for onScroll only\n    this.lastScrollLeft = element.scrollLeft; // for onScroll only\n    this.event.bind(this.element, 'scroll', function (e) { return this$1.onScroll(e); });\n    updateGeometry(this);\n  };\n\n  PerfectScrollbar.prototype.update = function update () {\n    if (!this.isAlive) {\n      return;\n    }\n\n    // Recalcuate negative scrollLeft adjustment\n    this.negativeScrollAdjustment = this.isNegativeScroll\n      ? this.element.scrollWidth - this.element.clientWidth\n      : 0;\n\n    // Recalculate rail margins\n    set(this.scrollbarXRail, { display: 'block' });\n    set(this.scrollbarYRail, { display: 'block' });\n    this.railXMarginWidth =\n      toInt(get(this.scrollbarXRail).marginLeft) +\n      toInt(get(this.scrollbarXRail).marginRight);\n    this.railYMarginHeight =\n      toInt(get(this.scrollbarYRail).marginTop) +\n      toInt(get(this.scrollbarYRail).marginBottom);\n\n    // Hide scrollbars not to affect scrollWidth and scrollHeight\n    set(this.scrollbarXRail, { display: 'none' });\n    set(this.scrollbarYRail, { display: 'none' });\n\n    updateGeometry(this);\n\n    processScrollDiff(this, 'top', 0, false, true);\n    processScrollDiff(this, 'left', 0, false, true);\n\n    set(this.scrollbarXRail, { display: '' });\n    set(this.scrollbarYRail, { display: '' });\n  };\n\n  PerfectScrollbar.prototype.onScroll = function onScroll (e) {\n    if (!this.isAlive) {\n      return;\n    }\n\n    updateGeometry(this);\n    processScrollDiff(this, 'top', this.element.scrollTop - this.lastScrollTop);\n    processScrollDiff(\n      this,\n      'left',\n      this.element.scrollLeft - this.lastScrollLeft\n    );\n\n    this.lastScrollTop = Math.floor(this.element.scrollTop);\n    this.lastScrollLeft = this.element.scrollLeft;\n  };\n\n  PerfectScrollbar.prototype.destroy = function destroy () {\n    if (!this.isAlive) {\n      return;\n    }\n\n    this.event.unbindAll();\n    remove(this.scrollbarX);\n    remove(this.scrollbarY);\n    remove(this.scrollbarXRail);\n    remove(this.scrollbarYRail);\n    this.removePsClasses();\n\n    // unset elements\n    this.element = null;\n    this.scrollbarX = null;\n    this.scrollbarY = null;\n    this.scrollbarXRail = null;\n    this.scrollbarYRail = null;\n\n    this.isAlive = false;\n  };\n\n  PerfectScrollbar.prototype.removePsClasses = function removePsClasses () {\n    this.element.className = this.element.className\n      .split(' ')\n      .filter(function (name) { return !name.match(/^ps([-_].+|)$/); })\n      .join(' ');\n  };\n\n  return PerfectScrollbar;\n\n})));\n//# sourceMappingURL=perfect-scrollbar.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/perfect-scrollbar/dist/perfect-scrollbar.js\n");

/***/ })

/******/ })));