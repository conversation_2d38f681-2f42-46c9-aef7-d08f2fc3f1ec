@extends('layouts.app')
@section('title', 'Pergunte a<PERSON>')

@section('content')
<section class="contentServico txtcontent">
	
	<div class="container-fluid">
		
		<div class="row">
			
			<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12">

				<h1>Pergunte ao Tabelião</h1>
        <p>Consulte aqui a sua dúvida. Se precisar, contate-nos.</p>

        <div class="d-flex align-items-start">
          <form  id="searchFaq" class="searchForm">
            <input type="text" placeholder="Digite para pesquisar" name="busca"  autocomplete="off" value="{{ request()->busca }}">
            {{--<div class="result"><ul></ul></div>--}}
            
            <input type="submit" style="cursor: pointer">
          </form>
          <a class="btnDefault btnFaqLimparFiltro" href="{{ route('questions') }}">Limpar Filtro</a>
        </div>

        
        @if ($categories->isEmpty())
            <p>Refine a pesquisa. Caso não encontre, envie sua dúvida para <a href="mailto:<EMAIL>?subject=Pergunte ao Tabelião"><EMAIL></a>, com o assunto Pergunte ao Tabelião.</p>
        @else

          <p>Resultados da busca:</p>

          @foreach ($categories as $category)
            <details class="itemDuvida">
              <summary>{{ $category->category }}</summary>
              <div class="tabs">
                @foreach ($category->questions(request()->busca)->get() as $question)
                <div class="tab">
                  <input type="checkbox" id="chck1">
                  <label class="tab-label" for="chck1">{{ $question->question }}</label>
                  {{--  <div class="tab-content">{!! $question->answer !!}</div> --}}
                  <div class="tab-content">{!! strip_tags($question->answer, '<br><strong><em><a><b><i>') !!}</div>
                </div>
                @endforeach
              </div>
            </details>
          @endforeach
        @endif
			</div>

			<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12">
				<div class="boxWidgetSidebar">
					<p class="red"><strong>Filtrar dúvida</strong></p>
          @foreach ($categories as $category)
            <a href="#">{{ $category->category }}</a><br>  
          @endforeach
				</div>
			</div>

		</div>

	</div>

</section>

<script nonce="{{ csp_nonce() }}">
  function filter() {
    var txt = $('#busca').val();

    var url = new URL(window.location.href);
    url.searchParams.set('busca', txt);
    window.location = url.href;
  }
</script>
@endsection