@extends('layouts.admin')
@section('title', 'Links Cabeçalho')

@section('content')
<h4 class="fw-bold py-3 mb-4">
  <span class="text-muted fw-light">Menus / </span>Links
</h4>

<form action="{{ route('menus.links.store') }}" method="POST" id="ddd">
  @csrf
  <div class="card" x-data="menuSelectorComponent(@js($selectedMenu), @js($selectedSubmenu))">
    <h5 class="card-header">Menu</h5>
    <div class="card-body">
        <div class="row">
          <div class="mb-3 col-sm-6">
            <label class="form-label">Menu</label>
            <select type="text" class="form-select" x-model="selectedMenu" name="menu">
              <option>Selecione um menu</option>
              @foreach ($menus as $menu)
                <option value="{{ $menu->id }}">{{ $menu->title }}</option>
              @endforeach
            </select>
          </div>
          <div class="mb-3 col-sm-6">
            @foreach ($menus as $menu)
            <template x-if="selectedMenu == {{ $menu->id }}">
              <div>
                <label class="form-label">Submenu</label>

                  <select type="text" class="form-select" placeholder="agende-seu-servico" name="submenu" x-on:change="submenuSelected" x-model="selectedSubmenu">
                    <option>Selecione um submenu</option>
                      @foreach ($menu->submenus as $submenu)
                        <option value="{{ $submenu->id }}">{{ $submenu->title }}</option>
                      @endforeach
                  </select>

              </div>
          </template>
          @endforeach
          </div>
        </div>
    </div>
  </div>

  <div id="sortable" x-data="linksComponent(@js($links))">
    <template x-for="(link, index) in links">
      <div class="card my-4">
        <div class="card-body">
            <div class="d-flex justify-content-between mb-1">
              <div class="d-flex">
                <i class='bx bxs-sort-alt cursor-move me-2'></i>
                <template x-if="link.type == 'page'"><h5>Página</h5></template>
                <template x-if="link.type == 'link'"><h5>Link Externo</h5></template>
              </div>
              <i class='bx bx-trash text-danger cursor-pointer' x-on:click="remove(index)"></i>
            </div>
            <div class="row">
              <div class="col-sm-2">
                <label class="form-label">Tipo</label>
                <select class="form-select" x-model="link.type" :name="getInputName(index, 'type')">
                  <option value="page">Página</option>
                  <option value="link">Link Externo</option>
                </select>
              </div>
              <template x-if="link.type == 'link'">
                <div class="col-sm-5">
                  <label class="form-label">Titulo</label>
                  <input type="text" class="form-control" placeholder="Digite o título" :name="getInputName(index, 'title')" x-model="link.title">
                </div>
              </template>
              <div class="col-sm-5">
                <template x-if="link.type == 'page'">
                  <div>
                    <label class="form-label">Página</label>
                    <select class="form-select" :name="getInputName(index, 'page_id')" x-model="link.page_id">
                      <option value="">Selecionar...</option>
                      @foreach ($pages as $page)
                        <option value="{{ $page->id }}">{{ $page->title }}</option>
                      @endforeach
                    </select>
                  </div>
                </template>
                <template x-if="link.type == 'link'">
                    <div>
                        <label class="form-label">Link</label>
                        <input type="text" class="form-control" placeholder="https://26notas.com.br/ata-notarial" x-model="link.link" :name="getInputName(index, 'link')">
                    </div>
                </template>
              </div>
                <div class="row">
                    <div class="col-md-6 offset-md-2">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check mt-2">
                                    <input class="form-check-input" type="radio" x-model="link.color" value="padrao" :name="getInputName(index, 'color')">
                                    <label class="form-check-label">
                                    Cor padrão
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check mt-2">
                                    <input class="form-check-input" type="radio" x-model="link.color" value="azul" id="corazul" :name="getInputName(index, 'color')">
                                    <label class="form-check-label">
                                    Cor azul
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check mt-2">
                                    <input class="form-check-input" type="radio" x-model="link.color" value="vermelho" id="corvermelho" :name="getInputName(index, 'color')">
                                    <label class="form-check-label">
                                    Cor vermelho
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check mt-2">
                                    <input class="form-check-input" type="checkbox" x-model="link.bold" :value="!!link.bold" :name="getInputName(index, 'bold')">
                                    <label class="form-check-label">
                                    Negrito
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check mt-2">
                                    <input class="form-check-input" type="checkbox" x-model="link.blank" :value="!!link.blank" id="defaultCheck2" :name="getInputName(index, 'blank')">
                                    <label class="form-check-label">
                                        Abrir em nova guia
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

              </div>
            </div>

        </div>
      </div>
    </template>

    <div class="d-flex justify-content-center mt-4">
      <a x-on:click="addLink" class="cursor-pointer">Adicionar Link</a>
    </div>
  </div>


  <button type="submit" class="btn btn-primary" form="ddd">Salvar</button>
</form>



<script nonce="{{ csp_nonce() }}">
  $( "#sortable" ).sortable();

  function menuSelectorComponent(initialMenu, initialSubmenu)
  {
    return {
      selectedMenu: initialMenu,
      selectedSubmenu: initialSubmenu,
      submenuSelected() {
        window.location.href = "{{ route('menus.links') }}?menu=" + this.selectedMenu + "&submenu=" + this.selectedSubmenu
      }
    }
  }

  function linksComponent(initialLinks = [])
  {
    return {
      links: initialLinks,
      addLink() {
        this.links.push({ title: '', blank: false, bold: false, color: 'padrao', type: 'page', link: '', page_id: null })
      },
      remove(index) {
        this.links.splice(index, 1);
      },
      getInputName(index, input) {
        return 'links[' + index + '][' + input + ']';
      },
      init() {
        console.log(this.links);
      }
    }
  }


</script>
@endsection
