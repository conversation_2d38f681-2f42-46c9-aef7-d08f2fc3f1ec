<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CollaboratorOccupation extends Model
{
    use HasFactory;

    public function collaborators($types = 'clerk')
    {
      switch ($types) {
        case 'clerk':
          $types = ['clerk'];
          break;
        case 'main':
          $types = ['clerk', 'notary', 'substitute_notary'];
          break;
        default:
          $types = ['clerk'];
          break;
      }

      return $this->belongsToMany(Collaborator::class, 'collaborator_has_occupation', 'occupation_id')
                            ->whereIn('collaborators.type', $types)
                            ->orderBy('name');
    }
}
