@extends('layouts.admin')
@section('title', 'Menus Rodapé')

@section('content')
<h4 class="fw-bold py-3 mb-4">
  <span class="text-muted fw-light">Menus / </span><span class="text-muted fw-light">Rodapé /</span> Novo
</h4>

<form method="POST" action="{{ route('menus.footer.update', [$menu->id]) }}">
  @csrf

  <div class="card">
    <h5 class="card-header">Menu</h5>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-2">
          <label class="form-label">Tipo</label>
          <select class="form-select" id="type" name="type" required onchange="changeType()">
            <option value="page" @selected($menu->type == 'page')>Página</option>
            <option value="link" @selected($menu->type == 'link')>Link</option>
          </select>
        </div>
        <div class="mb-3 col-sm-5">
          <label class="form-label">Titulo</label>
          <input type="text" class="form-control @error('title') is-invalid @enderror" placeholder="Digite o título" name="title" value="{{ $menu->title }}" required>
        </div>
          <div id="page-container" class="mb-3 col-sm-5">
            <label class="form-label">Página</label>
            <select class="form-select" name="page">
              <option value="">Selecionar...</option>
              @foreach ($pages as $page)
                <option value="{{ $page->id }}" @selected($menu->page_id == $page->id)>{{ $page->title }}</option>
              @endforeach
            </select>
          </div>

          <div id="link-container" class="mb-3 col-sm-5" style="display: none;">
            <label class="form-label">Link</label>
            <input type="text" class="form-control @error('link') is-invalid @enderror" placeholder="https://26notas.com.br/agendamento" name="link" value="{{ $menu->link }}">
          </div>
      </div>
    </div>
  </div>

  <button class="btn btn-primary mt-3">Salvar</button>
</form>



<script nonce="{{ csp_nonce() }}">
  setTimeout(() => {
    changeType();
  }, 200);

  function changeType() {
    var type = $('#type').val();
    if (type == 'link') {
      $('#link-container').show();
      $('#page-container').hide();
    } else {
      $('#link-container').hide();
      $('#page-container').show();
    }
  }
</script>
@endsection