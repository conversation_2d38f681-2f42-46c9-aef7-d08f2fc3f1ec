@extends('layouts.my-account')
@section('content')
    <style>
        .input-group {
            position: relative;
        }
        .password {
            padding-right: 30px;
            border: none;
        }
        .password.highlight {
            border: 1px solid red !important;
        }
        .toggle-password {
            position: absolute;
            right: 10px;
            top: 45%;
            transform: translateY(-50%);
            cursor: pointer;
        }
    </style>
    <section class="account txtcontent">

        <div class="container-fluid">

            <div class="row">

                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                    <h1>Minha Conta</h1>
                    <p>Informe seus dados para ter uma experiência personalizada. </p>

                    <div id="anchor"></div>
                    @if(session('success'))
                        <div class="message success">
                            <p>Sucesso</p>
                            <p>{{ session('success') }}</p>
                        </div>
                    @endif

                    @if($errors->any())
                        <div class="message error">
                            <p>Obrigatório</p>
                            <ul class="mt-2" style="font-size: 15px;">
                                @foreach ($errors->all() as $error)
                                    <li style="color:#a94442;">{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('my.acc.update') }}" method="POST">
                        @csrf
                        <div class="formControl">
                            <label>Nome Completo</label>
                            <input type="text" name="name" required value="{{ $client->name }}">
                        </div>

                        <div class="formControl">
                            <label>CPF</label>
                            <input type="text" name="cpf" required value="{{ $client->cpf }}" class="cpf">
                        </div>

                        <div class="formControl">
                            <label>Data de Nascimento</label>
                            <input type="type" name="birthdate" required value="{{ $client->birthdate ? \Carbon\Carbon::parse($client->birthdate)->format('d/m/Y') : '' }}" class="datacompleta">
                        </div>

                        <div class="formControl">
                            <label>Celular com DDD</label>
                            <input type="text" name="phone" required class="cel-mask" value="{{ $client->phone }}">
                        </div>

                        <div class="formControl">
                            <label for="password">Caso necessário, altere a senha</label>
                            <div class="input-group">
                                <input type="password" id="password" name="password" class="password">
                                <span class="toggle-password"><i class="fa fa-eye"></i></span>
                            </div>
                        </div>


                        <div class="formControl">
                            <label>Gênero</label>
                            <div class="typeRadio">
                                <input type="radio" name="gender" id="yes"
                                       value="male" @checked($client->gender == 'male')>
                                <label for="yes">Masculino</label><br>
                            </div>
                            <div class="typeRadio">
                                <input type="radio" id="no" name="gender"
                                       value="female" @checked($client->gender == 'female')>
                                <label for="no">Feminino</label><br>
                            </div>
                            <div class="typeRadio">
                                <input type="radio" id="null" name="gender" value="" @checked($client->gender == null)>
                                <label for="null">Prefiro não dizer</label><br>
                            </div>
                        </div>

                        <div class="d-flex">
                            <button class="btnDefault">Salvar</button>
                            {{-- <p class="resendToken"><a href="{{ route('my.services') }}">Configurar depois</a></p> --}}
                        </div>
                    </form>


                </div>

                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">

                    @can('use_services')
                        <div class="contentServicesAccount">

                            <div class="item">

                                <div class="d-flex justify-content-between align-items-center">
                                    <h2 class="red">Meu 26º</h2>
                                </div>

                            </div>


                            <div class="item item_personalize">
                                <p>Personalize suas informações selecionando o escrevente que te atende. </p>
                                <form action="{{ route('my.clerks') }}" method="POST">

                                    @csrf
                                    <select name="collaborator_id" required>
                                        <option value="">Selecionar escrevente</option>
                                        @foreach ($occupations as $occupation)
                                            <optgroup label="{{ $occupation->name }}">
                                                @foreach ($occupation->collaborators('main')->get() as $collaborator)
                                                    <option
                                                        value="{{ $collaborator->id }}">{{ $collaborator->name }}</option>
                                                @endforeach
                                            </optgroup>
                                        @endforeach
                                    </select>
                                    <div class="d-flex">
                                        <button class="btnDefault">Cadastrar</button>
                                        {{-- <p class="resendToken"><a href="{{ route('my.services') }}">Configurar depois</a></p> --}}
                                    </div>
                                </form>

                            </div>


                            @foreach ($occupations as $occupation)
                                @if (count($occupation->my_clerks) > 0)
                                    <div class="item">
                                        <h2>{{ $occupation->name }}</h2>
                                        @foreach ($occupation->my_clerks as $clerk)
                                            <div class="d-flex justify-content-between align-items-center">
                                                <p>{{ $clerk->name }}<br>{{ $clerk->email }}<br>{{ $clerk->phone }}</p>
                                                <form action="{{ route('my.clerks.remove', [$clerk->id]) }}"
                                                      method="POST" class="d-flex align-items-end"
                                                      style="align-items: flex-end;">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button href="" type="submit"
                                                            onclick="return confirm('Tem certeza que deseja excluir esse escrevente?')"
                                                            class="btnAsLink">Excluir
                                                    </button>
                                                </form>
                                            </div>
                                        @endforeach
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    @endcan

                </div>

            </div>

        </div>

    </section>
    <script>
        $(document).ready(function () {

            if (window.location.href.includes('actionType=reset')) {
                $('#password').addClass('highlight');
                $('html, body').animate({
                    scrollTop: $('#password').offset().top
                }, 'slow');
            }

            $('.toggle-password').click(function () {
                var input = $(this).prev('.password');
                if (input.attr('type') === 'password') {
                    input.attr('type', 'text');
                } else {
                    input.attr('type', 'password');
                }
            });

        });
    </script>
@endsection
