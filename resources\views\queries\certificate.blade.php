@extends('layouts.app')
@section('title', 'Andamento da Certidão')

@section('content')
<section class="account txtcontent">
  <div class="container-fluid">
	@if (!isset($resposta))
  <div class="px-1">
    <div class="breadcrumb">
      <a href="{{ route('my.services') }}">Meus Serviços</a>  >
      <span>Andamento da Certidão</span>
    </div>
    <div class="d-flex justify-content-between align-items-start">
      <h1>Andamento da Certidão</h1>
      <a href="{{ route('my.services') }}" class="btnDefault" style="background: #708082;">Voltar</a>
    </div>
  </div>
		<div class="row">
			<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">

				<p><strong class="blue">PESQUISA POR CPF/CNPJ E LIVRO E FOLHA</strong><br>Digite o número do CPF ou do CNPJ do responsável pelo pedido e o número do livro e folha do ato solicitado. Confira o andamento da certidão.</p>

				<form action="{{ route('queries.show.certificate') }}" method="POST">
                    @csrf
					<div class="formControl">
						<label>Selecione um tipo de pesquisa</label>
                        <select name="type" id="doc_type">
							<option value="cpf" @selected(request()->type == 'cpf')>CPF</option>
							<option value="cnpj" @selected(request()->type == 'cnpj')>CNPJ</option>
						</select>
					</div>
					<div class="formControl">
						<label>Informe o CPF/CNPJ:</label>
						<input type="text" required name="doc" id="cpfcnpj2" value="{{ request()->doc }}">
					</div>
                    <div class="formControl">
                        <label>Livro:</label>
                        <input type="text" name="livro" required>
                        <label>Folha:</label>
                        <input type="text" name="folha" required>
                    </div>
                    <div class="d-flex">
                        <button class="btnDefault">Enviar</button>
                    </div>
				</form>

			</div>

		</div>

        <script nonce="{{ csp_nonce() }}">
            $(document).ready(function() {
                // Obter os parâmetros da URL
                const urlParams = new URLSearchParams(window.location.search);

                // Obter o valor do parâmetro 'type'
                const typeParam = urlParams.get('type');

                const maskCPF = '000.000.000-000';
                const maskCNPJ = '00.000.000/0000-00';
                // Definir a máscara com base no parâmetro 'type'
                const mask = (typeParam === 'cnpj') ? maskCNPJ : maskCPF;

                // Aplicar a máscara ao campo
                $('#cpfcnpj2').mask(mask);

                $('#doc_type').change(function(){
                    $('#cpfcnpj2').unmask();
                    $('#cpfcnpj2').val('');
                    if($(this).val() == 'cnpj')
                        $('#cpfcnpj2').mask(maskCNPJ);
                    else
                        $('#cpfcnpj2').mask(maskCPF);
                });
            });
        </script>

    @else
      <div class="px-1">
        <div class="breadcrumb">
          <a href="{{ route('my.services') }}">Meus Serviços</a>  >
          <a href="{{ route('queries.certificate') }}">Andamento da Certidão</a>  >
          <span>Resultado</span>
        </div>
        <div class="d-flex justify-content-between align-items-start">
          <h1>Andamento da Certidão</h1>
          <a href="{{ route('queries.certificate') }}" class="btnDefault" style="background: #708082;">Voltar</a>
        </div>
      </div>
      <div class="row">

        <div class="resultadoBusca">

          <div class="headBusca">
            <h2>RESULTADOS DA PESQUISA DE ANDAMENTO DE CERTIDÃO</h2>
          </div>

          @if(!isset($resposta->message))
                @foreach($resposta as $item)
                    <div class="registrosResultados active">
                        <h3>Informação da Certidão <?php echo $item->numeroPedido ?></h3>
                        <table>
                            <tbody><tr>
                            <td><strong>Número do Pedido:</strong></td>
                            <td><?php echo $item->numeroPedido ?></td>
                            </tr>
                            <tr>
                            <td><strong>Tipo:</strong></td>
                            <td>Certidão</td>
                            </tr>
                            <tr>
                            <td><strong>Ato:</strong></td>
                            <td><?php echo $item->tipoAtoDescricao ?></td>
                            </tr>
                            <tr>
                            <td><strong>Livro:</strong></td>
                            <td><?php echo $item->livro ?></td>
                            </tr>
                            <tr>
                            <td><strong>Folha:</strong></td>
                            <td><?php echo $item->folha ?></td>
                            </tr>
                            <tr>
                            <td><strong>Quantidade:</strong></td>
                            <td><?php echo $item->quantidade ?></td>
                            </tr>
                            <tr>
                            <td><strong>Solicitante:</strong></td>
                            <td><?php echo $item->solicitante ?></td>
                            </tr>
                            <tr>
                            <td><strong>Pago:</strong></td>
                            <td><?php echo $item->baixa ?></td>
                            </tr>
                            <tr>
                            <td><strong>Situação:</strong></td>
                            <td><?php echo $item->status ?></td>
                            </tr>
                            <tr>
                        </tr></tbody></table>

                        </div>
            @endforeach
          @else
            <p>Nenhum resultado</p>
          @endif
        </div>

      </div>
    @endif

	</div>

</section>
@endsection
