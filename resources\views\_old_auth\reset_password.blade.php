@extends('layouts.app')
@section('title', 'Login')

@section('content')
    <section class="account txtcontent">

        <div class="container-fluid">

            <div class="row">

                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">

                    <h1>Cadastro Cliente 26º</h1>
                    <p>Informe seu e-mail cadastrado para gerar a recuperação do seu acesso.</p>
                    
                    <form action="{{ route('auth.reset_password') }}" method="POST">
                        @csrf
                        <div class="formControl">
                            <label for="email">E-mail</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="reCaptcha">
                        </div>
                        <button class="btnDefault">Enviar</button>
                        <div id="anchor" class="anchorLogin"></div>
                        @if(session('success'))
                            <div class="message success">
                                <p>Sucesso</p>
                                <p>{{ session('success') }}</p>
                            </div>
                        @endif
                        @if($errors->any())
                            <div class="message error">
                                <p>Erro</p>
                                <ul class="mt-2" style="font-size: 15px;">
                                    @foreach ($errors->all() as $error)
                                        <li style="color:#a94442;">{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                    </form>

                </div>

            </div>
        </div>
    </section>
@endsection

