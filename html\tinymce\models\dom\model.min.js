/**
 * TinyMCE version 7.4.1 (TBD)
 */
!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.ModelManager");const t=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&(o=n=e,(r=String).prototype.isPrototypeOf(o)||(null===(s=n.constructor)||void 0===s?void 0:s.name)===r.name)?"string":t;var o,n,r,s})(t)===e,o=e=>t=>typeof t===e,n=e=>t=>e===t,r=t("string"),s=t("object"),l=t("array"),a=n(null),c=o("boolean"),i=n(void 0),m=e=>!(e=>null==e)(e),d=o("function"),u=o("number"),f=()=>{},g=e=>()=>e,h=e=>e,p=(e,t)=>e===t;function b(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const w=e=>t=>!e(t),v=e=>e(),y=g(!1),x=g(!0);class C{constructor(e,t){this.tag=e,this.value=t}static some(e){return new C(!0,e)}static none(){return C.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?C.some(e(this.value)):C.none()}bind(e){return this.tag?e(this.value):C.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:C.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return m(e)?C.some(e):C.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}C.singletonNone=new C(!1);const T=Array.prototype.slice,S=Array.prototype.indexOf,R=Array.prototype.push,D=(e,t)=>{return o=e,n=t,S.call(o,n)>-1;var o,n},O=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},k=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},E=(e,t)=>{const o=e.length,n=new Array(o);for(let r=0;r<o;r++){const o=e[r];n[r]=t(o,r)}return n},N=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},B=(e,t)=>{const o=[],n=[];for(let r=0,s=e.length;r<s;r++){const s=e[r];(t(s,r)?o:n).push(s)}return{pass:o,fail:n}},_=(e,t)=>{const o=[];for(let n=0,r=e.length;n<r;n++){const r=e[n];t(r,n)&&o.push(r)}return o},z=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),A=(e,t,o)=>(N(e,((e,n)=>{o=t(o,e,n)})),o),L=(e,t)=>((e,t,o)=>{for(let n=0,r=e.length;n<r;n++){const r=e[n];if(t(r,n))return C.some(r);if(o(r,n))break}return C.none()})(e,t,y),W=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return C.some(o);return C.none()},M=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!l(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);R.apply(t,e[o])}return t},j=(e,t)=>M(E(e,t)),P=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},I=(e,t)=>{const o={};for(let n=0,r=e.length;n<r;n++){const r=e[n];o[String(r)]=t(r,n)}return o},F=(e,t)=>t>=0&&t<e.length?C.some(e[t]):C.none(),H=e=>F(e,0),$=e=>F(e,e.length-1),V=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return C.none()},q=Object.keys,U=Object.hasOwnProperty,G=(e,t)=>{const o=q(e);for(let n=0,r=o.length;n<r;n++){const r=o[n];t(e[r],r)}},K=(e,t)=>Y(e,((e,o)=>({k:o,v:t(e,o)}))),Y=(e,t)=>{const o={};return G(e,((e,n)=>{const r=t(e,n);o[r.k]=r.v})),o},J=(e,t)=>{const o=[];return G(e,((e,n)=>{o.push(t(e,n))})),o},Q=e=>J(e,h),X=(e,t)=>U.call(e,t),Z="undefined"!=typeof window?window:Function("return this;")(),ee=(e,t)=>((e,t)=>{let o=null!=t?t:Z;for(let t=0;t<e.length&&null!=o;++t)o=o[e[t]];return o})(e.split("."),t),te=Object.getPrototypeOf,oe=e=>{const t=ee("ownerDocument.defaultView",e);return s(e)&&((e=>((e,t)=>{const o=((e,t)=>ee(e,t))(e,t);if(null==o)throw new Error(e+" not available on this browser");return o})("HTMLElement",e))(t).prototype.isPrototypeOf(e)||/^HTML\w*Element$/.test(te(e).constructor.name))},ne=e=>e.dom.nodeName.toLowerCase(),re=e=>e.dom.nodeType,se=e=>t=>re(t)===e,le=e=>8===re(e)||"#comment"===ne(e),ae=e=>ce(e)&&oe(e.dom),ce=se(1),ie=se(3),me=se(9),de=se(11),ue=e=>t=>ce(t)&&ne(t)===e,fe=(e,t,o)=>{if(!(r(o)||c(o)||u(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},ge=(e,t,o)=>{fe(e.dom,t,o)},he=(e,t)=>{const o=e.dom;G(t,((e,t)=>{fe(o,t,e)}))},pe=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},be=(e,t)=>C.from(pe(e,t)),we=(e,t)=>{e.dom.removeAttribute(t)},ve=e=>A(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}),ye=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},xe={fromHtml:(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return ye(o.childNodes[0])},fromTag:(e,t)=>{const o=(t||document).createElement(e);return ye(o)},fromText:(e,t)=>{const o=(t||document).createTextNode(e);return ye(o)},fromDom:ye,fromPoint:(e,t,o)=>C.from(e.dom.elementFromPoint(t,o)).map(ye)},Ce=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},Te=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,Se=(e,t)=>{const o=void 0===t?document:t.dom;return Te(o)?C.none():C.from(o.querySelector(e)).map(xe.fromDom)},Re=(e,t)=>e.dom===t.dom,De=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},Oe=Ce,ke=e=>xe.fromDom(e.dom.ownerDocument),Ee=e=>me(e)?e:ke(e),Ne=e=>C.from(e.dom.parentNode).map(xe.fromDom),Be=e=>C.from(e.dom.parentElement).map(xe.fromDom),_e=(e,t)=>{const o=d(t)?t:y;let n=e.dom;const r=[];for(;null!==n.parentNode&&void 0!==n.parentNode;){const e=n.parentNode,t=xe.fromDom(e);if(r.push(t),!0===o(t))break;n=e}return r},ze=e=>C.from(e.dom.previousSibling).map(xe.fromDom),Ae=e=>C.from(e.dom.nextSibling).map(xe.fromDom),Le=e=>E(e.dom.childNodes,xe.fromDom),We=(e,t)=>{const o=e.dom.childNodes;return C.from(o[t]).map(xe.fromDom)},Me=(e,t)=>{Ne(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},je=(e,t)=>{Ae(e).fold((()=>{Ne(e).each((e=>{Ie(e,t)}))}),(e=>{Me(e,t)}))},Pe=(e,t)=>{const o=(e=>We(e,0))(e);o.fold((()=>{Ie(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},Ie=(e,t)=>{e.dom.appendChild(t.dom)},Fe=(e,t)=>{Me(e,t),Ie(t,e)},He=(e,t)=>{N(t,((o,n)=>{const r=0===n?e:t[n-1];je(r,o)}))},$e=(e,t)=>{N(t,(t=>{Ie(e,t)}))},Ve=e=>{e.dom.textContent="",N(Le(e),(e=>{qe(e)}))},qe=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},Ue=e=>{const t=Le(e);t.length>0&&He(e,t),qe(e)},Ge=(e,t)=>xe.fromDom(e.dom.cloneNode(t)),Ke=e=>Ge(e,!1),Ye=e=>Ge(e,!0),Je=(e,t)=>{const o=xe.fromTag(t),n=ve(e);return he(o,n),o},Qe=["tfoot","thead","tbody","colgroup"],Xe=(e,t,o)=>({element:e,rowspan:t,colspan:o}),Ze=(e,t,o)=>({element:e,cells:t,section:o}),et=(e,t,o)=>({element:e,isNew:t,isLocked:o}),tt=(e,t,o,n)=>({element:e,cells:t,section:o,isNew:n}),ot=e=>de(e)&&m(e.dom.host),nt=e=>xe.fromDom(e.dom.getRootNode()),rt=e=>xe.fromDom(e.dom.host),st=e=>{const t=ie(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return(e=>{const t=nt(e);return ot(t)?C.some(t):C.none()})(xe.fromDom(t)).fold((()=>o.body.contains(t)),(n=st,r=rt,e=>n(r(e))));var n,r},lt=e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return xe.fromDom(t)},at=(e,t)=>{let o=[];return N(Le(e),(e=>{t(e)&&(o=o.concat([e])),o=o.concat(at(e,t))})),o},ct=(e,t,o)=>((e,o,n)=>_(_e(e,n),(e=>Ce(e,t))))(e,0,o),it=(e,t)=>((e,o)=>_(Le(e),(e=>Ce(e,t))))(e),mt=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Te(o)?[]:E(o.querySelectorAll(e),xe.fromDom)})(t,e);var dt=(e,t,o,n,r)=>e(o,n)?C.some(o):d(r)&&r(o)?C.none():t(o,n,r);const ut=(e,t,o)=>{let n=e.dom;const r=d(o)?o:y;for(;n.parentNode;){n=n.parentNode;const e=xe.fromDom(n);if(t(e))return C.some(e);if(r(e))break}return C.none()},ft=(e,t,o)=>dt(((e,t)=>t(e)),ut,e,t,o),gt=(e,t,o)=>ut(e,(e=>Ce(e,t)),o),ht=(e,t)=>((e,o)=>L(e.dom.childNodes,(e=>{return o=xe.fromDom(e),Ce(o,t);var o})).map(xe.fromDom))(e),pt=(e,t)=>Se(t,e),bt=(e,t,o)=>dt(((e,t)=>Ce(e,t)),gt,e,t,o),wt=(e,t,o=p)=>e.exists((e=>o(e,t))),vt=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},yt=(e,t)=>e?C.some(t):C.none(),xt=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,Ct=(e,t,o=0,n)=>{const r=e.indexOf(t,o);return-1!==r&&(!!i(n)||r+t.length<=n)},Tt=(e,t)=>xt(e,t,0),St=(e,t)=>xt(e,t,e.length-t.length),Rt=(e=>t=>t.replace(e,""))(/^\s+|\s+$/g),Dt=e=>e.length>0,Ot=e=>void 0!==e.style&&d(e.style.getPropertyValue),kt=(e,t,o)=>{if(!r(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);Ot(e)&&e.style.setProperty(t,o)},Et=(e,t,o)=>{const n=e.dom;kt(n,t,o)},Nt=(e,t)=>{const o=e.dom;G(t,((e,t)=>{kt(o,t,e)}))},Bt=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||st(e)?n:_t(o,t)},_t=(e,t)=>Ot(e)?e.style.getPropertyValue(t):"",zt=(e,t)=>{const o=e.dom,n=_t(o,t);return C.from(n).filter((e=>e.length>0))},At=(e,t)=>{((e,t)=>{Ot(e)&&e.style.removeProperty(t)})(e.dom,t),wt(be(e,"style").map(Rt),"")&&we(e,"style")},Lt=(e,t,o=0)=>be(e,t).map((e=>parseInt(e,10))).getOr(o),Wt=(e,t)=>Lt(e,t,1),Mt=e=>ue("col")(e)?Lt(e,"span",1)>1:Wt(e,"colspan")>1,jt=(e,t)=>parseInt(Bt(e,t),10),Pt=g(10),It=g(10),Ft=(e,t)=>Ht(e,t,x),Ht=(e,t,o)=>j(Le(e),(e=>Ce(e,t)?o(e)?[e]:[]:Ht(e,t,o))),$t=(e,t)=>((e,t,o=y)=>o(t)?C.none():D(e,ne(t))?C.some(t):gt(t,e.join(","),(e=>Ce(e,"table")||o(e))))(["td","th"],e,t),Vt=e=>Ft(e,"th,td"),qt=e=>Ce(e,"colgroup")?it(e,"col"):j(Kt(e),(e=>it(e,"col"))),Ut=(e,t)=>bt(e,"table",t),Gt=e=>Ft(e,"tr"),Kt=e=>Ut(e).fold(g([]),(e=>it(e,"colgroup"))),Yt=(e,t)=>E(e,(e=>{if("colgroup"===ne(e)){const t=E(qt(e),(e=>{const t=Lt(e,"span",1);return Xe(e,1,t)}));return Ze(e,t,"colgroup")}{const o=E(Vt(e),(e=>{const t=Lt(e,"rowspan",1),o=Lt(e,"colspan",1);return Xe(e,t,o)}));return Ze(e,o,t(e))}})),Jt=e=>Ne(e).map((e=>{const t=ne(e);return(e=>D(Qe,e))(t)?t:"tbody"})).getOr("tbody"),Qt=e=>{const t=Gt(e),o=[...Kt(e),...t];return Yt(o,Jt)},Xt=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},Zt=()=>eo(0,0),eo=(e,t)=>({major:e,minor:t}),to={nu:eo,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?Zt():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return eo(n(1),n(2))})(e,o)},unknown:Zt},oo=(e,t)=>{const o=String(t).toLowerCase();return L(e,(e=>e.search(o)))},no=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,ro=e=>t=>Ct(t,e),so=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>Ct(e,"edge/")&&Ct(e,"chrome")&&Ct(e,"safari")&&Ct(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,no],search:e=>Ct(e,"chrome")&&!Ct(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>Ct(e,"msie")||Ct(e,"trident")},{name:"Opera",versionRegexes:[no,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:ro("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:ro("firefox")},{name:"Safari",versionRegexes:[no,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(Ct(e,"safari")||Ct(e,"mobile/"))&&Ct(e,"applewebkit")}],lo=[{name:"Windows",search:ro("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>Ct(e,"iphone")||Ct(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:ro("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:ro("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:ro("linux"),versionRegexes:[]},{name:"Solaris",search:ro("sunos"),versionRegexes:[]},{name:"FreeBSD",search:ro("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:ro("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],ao={browsers:g(so),oses:g(lo)},co="Edge",io="Chromium",mo="Opera",uo="Firefox",fo="Safari",go=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(co),isChromium:n(io),isIE:n("IE"),isOpera:n(mo),isFirefox:n(uo),isSafari:n(fo)}},ho=()=>go({current:void 0,version:to.unknown()}),po=go,bo=(g(co),g(io),g("IE"),g(mo),g(uo),g(fo),"Windows"),wo="Android",vo="Linux",yo="macOS",xo="Solaris",Co="FreeBSD",To="ChromeOS",So=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(bo),isiOS:n("iOS"),isAndroid:n(wo),isMacOS:n(yo),isLinux:n(vo),isSolaris:n(xo),isFreeBSD:n(Co),isChromeOS:n(To)}},Ro=()=>So({current:void 0,version:to.unknown()}),Do=So,Oo=(g(bo),g("iOS"),g(wo),g(vo),g(yo),g(xo),g(Co),g(To),e=>window.matchMedia(e).matches);let ko=Xt((()=>((e,t,o)=>{const n=ao.browsers(),r=ao.oses(),s=t.bind((e=>((e,t)=>V(t.brands,(t=>{const o=t.brand.toLowerCase();return L(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:to.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>oo(e,t).map((e=>{const o=to.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(ho,po),l=((e,t)=>oo(e,t).map((e=>{const o=to.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(r,e).fold(Ro,Do),a=((e,t,o,n)=>{const r=e.isiOS()&&!0===/ipad/i.test(o),s=e.isiOS()&&!r,l=e.isiOS()||e.isAndroid(),a=l||n("(pointer:coarse)"),c=r||!s&&l&&n("(min-device-width:768px)"),i=s||l&&!c,m=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),d=!i&&!c&&!m;return{isiPad:g(r),isiPhone:g(s),isTablet:g(c),isPhone:g(i),isTouch:g(a),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:g(m),isDesktop:g(d)}})(l,s,e,o);return{browser:s,os:l,deviceType:a}})(window.navigator.userAgent,C.from(window.navigator.userAgentData),Oo)));const Eo=()=>ko(),No=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=Bt(o,e);return parseFloat(t)||0}return n},n=(e,t)=>A(t,((t,o)=>{const n=Bt(e,o),r=void 0===n?0:parseInt(n,10);return isNaN(r)?t:t+r}),0);return{set:(t,o)=>{if(!u(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;Ot(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const r=n(e,o);return t>r?t-r:0}}},Bo=(e,t,o)=>((e,t)=>(e=>{const t=parseFloat(e);return isNaN(t)?C.none():C.some(t)})(e).getOr(t))(Bt(e,t),o),_o=No("width",(e=>e.dom.offsetWidth)),zo=e=>_o.get(e),Ao=e=>_o.getOuter(e),Lo=e=>((e,t)=>{const o=e.dom,n=o.getBoundingClientRect().width||o.offsetWidth;return"border-box"===t?n:((e,t,o,n)=>t-Bo(e,`padding-${o}`,0)-Bo(e,`padding-${n}`,0)-Bo(e,`border-${o}-width`,0)-Bo(e,`border-${n}-width`,0))(e,n,"left","right")})(e,"content-box"),Wo=(e,t,o)=>{const n=e.cells,r=n.slice(0,t),s=n.slice(t),l=r.concat(o).concat(s);return Po(e,l)},Mo=(e,t,o)=>Wo(e,t,[o]),jo=(e,t,o)=>{e.cells[t]=o},Po=(e,t)=>tt(e.element,t,e.section,e.isNew),Io=(e,t)=>e.cells[t],Fo=(e,t)=>Io(e,t).element,Ho=e=>e.cells.length,$o=e=>{const t=B(e,(e=>"colgroup"===e.section));return{rows:t.fail,cols:t.pass}},Vo=(e,t,o)=>{const n=E(e.cells,o);return tt(t(e.element),n,e.section,!0)},qo="data-snooker-locked-cols",Uo=e=>be(e,qo).bind((e=>C.from(e.match(/\d+/g)))).map((e=>I(e,x))),Go=e=>{const t=A($o(e).rows,((e,t)=>(N(t.cells,((t,o)=>{t.isLocked&&(e[o]=!0)})),e)),{}),o=J(t,((e,t)=>parseInt(t,10)));return((e,t)=>{const o=T.call(e,0);return o.sort(void 0),o})(o)},Ko=(e,t)=>e+","+t,Yo=(e,t)=>{const o=j(e.all,(e=>e.cells));return _(o,t)},Jo=e=>{const t={},o=[],n=H(e).map((e=>e.element)).bind(Ut).bind(Uo).getOr({});let r=0,s=0,l=0;const{pass:a,fail:c}=B(e,(e=>"colgroup"===e.section));N(c,(e=>{const a=[];N(e.cells,(e=>{let o=0;for(;void 0!==t[Ko(l,o)];)o++;const r=((e,t)=>X(e,t)&&void 0!==e[t]&&null!==e[t])(n,o.toString()),c=((e,t,o,n,r,s)=>({element:e,rowspan:t,colspan:o,row:n,column:r,isLocked:s}))(e.element,e.rowspan,e.colspan,l,o,r);for(let n=0;n<e.colspan;n++)for(let r=0;r<e.rowspan;r++){const e=o+n,a=Ko(l+r,e);t[a]=c,s=Math.max(s,e+1)}a.push(c)})),r++,o.push(Ze(e.element,a,e.section)),l++}));const{columns:i,colgroups:m}=$(a).map((e=>{const t=(e=>{const t={};let o=0;return N(e.cells,(e=>{const n=e.colspan;k(n,(r=>{const s=o+r;t[s]=((e,t,o)=>({element:e,colspan:t,column:o}))(e.element,n,s)})),o+=n})),t})(e),o=((e,t)=>({element:e,columns:t}))(e.element,Q(t));return{colgroups:[o],columns:t}})).getOrThunk((()=>({colgroups:[],columns:{}}))),d=((e,t)=>({rows:e,columns:t}))(r,s);return{grid:d,access:t,all:o,columns:i,colgroups:m}},Qo=e=>{const t=Qt(e);return Jo(t)},Xo=Jo,Zo=(e,t,o)=>C.from(e.access[Ko(t,o)]),en=(e,t,o)=>{const n=Yo(e,(e=>o(t,e.element)));return n.length>0?C.some(n[0]):C.none()},tn=Yo,on=e=>j(e.all,(e=>e.cells)),nn=e=>Q(e.columns),rn=e=>q(e.columns).length>0,sn=(e,t)=>C.from(e.columns[t]),ln=(e,t=x)=>{const o=e.grid,n=k(o.columns,h),r=k(o.rows,h);return E(n,(o=>an((()=>j(r,(t=>Zo(e,t,o).filter((e=>e.column===o)).toArray()))),(e=>1===e.colspan&&t(e.element)),(()=>Zo(e,0,o)))))},an=(e,t,o)=>{const n=e();return L(n,t).orThunk((()=>C.from(n[0]).orThunk(o))).map((e=>e.element))},cn=e=>{const t=e.grid,o=k(t.rows,h),n=k(t.columns,h);return E(o,(t=>an((()=>j(n,(o=>Zo(e,t,o).filter((e=>e.row===t)).fold(g([]),(e=>[e]))))),(e=>1===e.rowspan),(()=>Zo(e,t,0)))))},mn=(e,t)=>o=>"rtl"===dn(o)?t:e,dn=e=>"rtl"===Bt(e,"direction")?"rtl":"ltr",un=No("height",(e=>{const t=e.dom;return st(e)?t.getBoundingClientRect().height:t.offsetHeight})),fn=e=>un.get(e),gn=e=>un.getOuter(e),hn=(e,t)=>({left:e,top:t,translate:(o,n)=>hn(e+o,t+n)}),pn=hn,bn=(e,t)=>void 0!==e?e:void 0!==t?t:0,wn=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,r=t.documentElement;if(o===e.dom)return pn(o.offsetLeft,o.offsetTop);const s=bn(null==n?void 0:n.pageYOffset,r.scrollTop),l=bn(null==n?void 0:n.pageXOffset,r.scrollLeft),a=bn(r.clientTop,o.clientTop),c=bn(r.clientLeft,o.clientLeft);return vn(e).translate(l-c,s-a)},vn=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?pn(o.offsetLeft,o.offsetTop):st(e)?(e=>{const t=e.getBoundingClientRect();return pn(t.left,t.top)})(t):pn(0,0)},yn=(e,t)=>({row:e,y:t}),xn=(e,t)=>({col:e,x:t}),Cn=e=>wn(e).left+Ao(e),Tn=e=>wn(e).left,Sn=(e,t)=>xn(e,Tn(t)),Rn=(e,t)=>xn(e,Cn(t)),Dn=e=>wn(e).top,On=(e,t)=>yn(e,Dn(t)),kn=(e,t)=>yn(e,Dn(t)+gn(t)),En=(e,t,o)=>{if(0===o.length)return[];const n=E(o.slice(1),((t,o)=>t.map((t=>e(o,t))))),r=o[o.length-1].map((e=>t(o.length-1,e)));return n.concat([r])},Nn={delta:h,positions:e=>En(On,kn,e),edge:Dn},Bn=mn({delta:h,edge:Tn,positions:e=>En(Sn,Rn,e)},{delta:e=>-e,edge:Cn,positions:e=>En(Rn,Sn,e)}),_n={delta:(e,t)=>Bn(t).delta(e,t),positions:(e,t)=>Bn(t).positions(e,t),edge:e=>Bn(e).edge(e)},zn={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},An=(()=>{const e="[0-9]+",t="[eE][+-]?"+e,o=e=>`(?:${e})?`,n=["Infinity",e+"\\."+o(e)+o(t),"\\."+e+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),Ln=/(\d+(\.\d+)?)%/,Wn=/(\d+(\.\d+)?)px|em/,Mn=ue("col"),jn=ue("tr"),Pn=(e,t,o)=>{const n=Be(e).getOrThunk((()=>lt(ke(e))));return t(e)/o(n)*100},In=(e,t)=>{Et(e,"width",t+"px")},Fn=(e,t)=>{Et(e,"width",t+"%")},Hn=(e,t)=>{Et(e,"height",t+"px")},$n=e=>{const t=(e=>{return Bo(t=e,"height",t.dom.offsetHeight)+"px";var t})(e);return t?((e,t,o,n)=>{const r=parseFloat(e);return St(e,"%")&&"table"!==ne(t)?((e,t,o,n)=>{const r=Ut(e).map((e=>{const n=o(e);return Math.floor(t/100*n)})).getOr(t);return n(e,r),r})(t,r,o,n):r})(t,e,fn,Hn):fn(e)},Vn=(e,t)=>zt(e,t).orThunk((()=>be(e,t).map((e=>e+"px")))),qn=e=>Vn(e,"width"),Un=e=>Pn(e,zo,Lo),Gn=e=>{return Mn(e)?zo(e):Bo(t=e,"width",t.dom.offsetWidth);var t},Kn=e=>jn(e)?fn(e):((e,t,o)=>o(e)/Wt(e,"rowspan"))(e,0,$n),Yn=(e,t,o)=>{Et(e,"width",t+o)},Jn=e=>Pn(e,zo,Lo)+"%",Qn=g(Ln),Xn=ue("col"),Zn=e=>qn(e).getOrThunk((()=>Gn(e)+"px")),er=e=>{return(t=e,Vn(t,"height")).getOrThunk((()=>Kn(e)+"px"));var t},tr=(e,t,o,n,r,s)=>e.filter(n).fold((()=>s(((e,t)=>{if(t<0||t>=e.length-1)return C.none();const o=e[t].fold((()=>{const o=(e=>{const t=T.call(e,0);return t.reverse(),t})(e.slice(0,t));return V(o,((e,t)=>e.map((e=>({value:e,delta:t+1})))))}),(e=>C.some({value:e,delta:0}))),n=e[t+1].fold((()=>{const o=e.slice(t+1);return V(o,((e,t)=>e.map((e=>({value:e,delta:t+1})))))}),(e=>C.some({value:e,delta:1})));return o.bind((e=>n.map((t=>{const o=t.delta+e.delta;return Math.abs(t.value-e.value)/o}))))})(o,t))),(e=>r(e))),or=(e,t,o,n)=>{const r=ln(e),s=rn(e)?(e=>E(nn(e),(e=>C.from(e.element))))(e):r,l=[C.some(_n.edge(t))].concat(E(_n.positions(r,t),(e=>e.map((e=>e.x))))),a=w(Mt);return E(s,((e,t)=>tr(e,t,l,a,(e=>{if((e=>{const t=Eo().browser,o=t.isChromium()||t.isFirefox();return!Xn(e)||o})(e))return o(e);{const e=null!=(s=r[t])?h(s):C.none();return tr(e,t,l,a,(e=>n(C.some(zo(e)))),n)}var s}),n)))},nr=e=>e.map((e=>e+"px")).getOr(""),rr=(e,t,o)=>or(e,t,Gn,(e=>e.getOrThunk(o.minCellWidth))),sr=(e,t,o,n)=>{const r=cn(e),s=E(e.all,(e=>C.some(e.element))),l=[C.some(Nn.edge(t))].concat(E(Nn.positions(r,t),(e=>e.map((e=>e.y)))));return E(s,((e,t)=>tr(e,t,l,x,o,n)))},lr=(e,t)=>()=>st(e)?t(e):parseFloat(zt(e,"width").getOr("0")),ar=e=>{const t=lr(e,(e=>parseFloat(Jn(e)))),o=lr(e,zo);return{width:t,pixelWidth:o,getWidths:(t,o)=>((e,t,o)=>or(e,t,Un,(e=>e.fold((()=>o.minCellWidth()),(e=>e/o.pixelWidth()*100)))))(t,e,o),getCellDelta:e=>e/o()*100,singleColumnWidth:(e,t)=>[100-e],minCellWidth:()=>Pt()/o()*100,setElementWidth:Fn,adjustTableWidth:o=>{const n=t();Fn(e,n+o/100*n)},isRelative:!0,label:"percent"}},cr=e=>{const t=lr(e,zo);return{width:t,pixelWidth:t,getWidths:(t,o)=>rr(t,e,o),getCellDelta:h,singleColumnWidth:(e,t)=>[Math.max(Pt(),e+t)-e],minCellWidth:Pt,setElementWidth:In,adjustTableWidth:o=>{const n=t()+o;In(e,n)},isRelative:!1,label:"pixel"}},ir=e=>qn(e).fold((()=>(e=>{const t=lr(e,zo),o=g(0);return{width:t,pixelWidth:t,getWidths:(t,o)=>rr(t,e,o),getCellDelta:o,singleColumnWidth:g([0]),minCellWidth:o,setElementWidth:f,adjustTableWidth:f,isRelative:!0,label:"none"}})(e)),(t=>((e,t)=>null!==Qn().exec(t)?ar(e):cr(e))(e,t))),mr=cr,dr=ar,ur=(e,t,o)=>{const n=e[o].element,r=xe.fromTag("td");Ie(r,xe.fromTag("br")),(t?Ie:Pe)(n,r)},fr=((e,t)=>{const o=t=>e(t)?C.from(t.dom.nodeValue):C.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return o(t).getOr("")},getOption:o,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(ie),gr=e=>fr.get(e),hr=e=>fr.getOption(e),pr=(e,t)=>fr.set(e,t),br=e=>"img"===ne(e)?1:hr(e).fold((()=>Le(e).length),(e=>e.length)),wr=["img","br"],vr=e=>hr(e).filter((e=>0!==e.trim().length||e.indexOf("\xa0")>-1)).isSome()||D(wr,ne(e))||(e=>ae(e)&&"false"===pe(e,"contenteditable"))(e),yr=e=>((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const r=xe.fromDom(e.childNodes[n]);if(t(r))return C.some(r);const s=o(e.childNodes[n]);if(s.isSome())return s}return C.none()};return o(e.dom)})(e,vr),xr=e=>Cr(e,vr),Cr=(e,t)=>{const o=e=>{const n=Le(e);for(let e=n.length-1;e>=0;e--){const r=n[e];if(t(r))return C.some(r);const s=o(r);if(s.isSome())return s}return C.none()};return o(e)},Tr={scope:["row","col"]},Sr=e=>()=>{const t=xe.fromTag("td",e.dom);return Ie(t,xe.fromTag("br",e.dom)),t},Rr=e=>()=>xe.fromTag("col",e.dom),Dr=e=>()=>xe.fromTag("colgroup",e.dom),Or=e=>()=>xe.fromTag("tr",e.dom),kr=(e,t,o)=>{const n=((e,t)=>{const o=Je(e,t),n=Le(Ye(e));return $e(o,n),o})(e,t);return G(o,((e,t)=>{null===e?we(n,t):ge(n,t,e)})),n},Er=e=>e,Nr=(e,t,o)=>{const n=(e,t)=>{((e,t)=>{const o=e.dom,n=t.dom;Ot(o)&&Ot(n)&&(n.style.cssText=o.style.cssText)})(e.element,t),At(t,"height"),1!==e.colspan&&At(t,"width")};return{col:o=>{const r=xe.fromTag(ne(o.element),t.dom);return n(o,r),e(o.element,r),r},colgroup:Dr(t),row:Or(t),cell:r=>{const s=xe.fromTag(ne(r.element),t.dom),l=o.getOr(["strong","em","b","i","span","font","h1","h2","h3","h4","h5","h6","p","div"]),a=l.length>0?((e,t,o)=>yr(e).map((n=>{const r=o.join(","),s=ct(n,r,(t=>Re(t,e)));return z(s,((e,t)=>{const o=Ke(t);return Ie(e,o),o}),t)})).getOr(t))(r.element,s,l):s;return Ie(a,xe.fromTag("br")),n(r,s),((e,t)=>{G(Tr,((o,n)=>be(e,n).filter((e=>D(o,e))).each((e=>ge(t,n,e)))))})(r.element,s),e(r.element,s),s},replace:kr,colGap:Rr(t),gap:Sr(t)}},Br=e=>({col:Rr(e),colgroup:Dr(e),row:Or(e),cell:Sr(e),replace:Er,colGap:Rr(e),gap:Sr(e)}),_r=e=>t=>t.options.get(e),zr="100%",Ar=e=>{var t;const o=e.dom,n=null!==(t=o.getParent(e.selection.getStart(),o.isBlock))&&void 0!==t?t:e.getBody();return Lo(xe.fromDom(n))+"px"},Lr=e=>C.from(e.options.get("table_clone_elements")),Wr=_r("table_header_type"),Mr=_r("table_column_resizing"),jr=e=>"preservetable"===Mr(e),Pr=e=>"resizetable"===Mr(e),Ir=_r("table_sizing_mode"),Fr=e=>"relative"===Ir(e),Hr=e=>"fixed"===Ir(e),$r=e=>"responsive"===Ir(e),Vr=_r("table_resize_bars"),qr=_r("table_style_by_css"),Ur=_r("table_merge_content_on_paste"),Gr=e=>{const t=e.options,o=t.get("table_default_attributes");return t.isSet("table_default_attributes")?o:((e,t)=>$r(e)||qr(e)?t:Hr(e)?{...t,width:Ar(e)}:{...t,width:zr})(e,o)},Kr=_r("table_use_colgroups"),Yr=e=>bt(e,"[contenteditable]"),Jr=(e,t=!1)=>st(e)?e.dom.isContentEditable:Yr(e).fold(g(t),(e=>"true"===Qr(e))),Qr=e=>e.dom.contentEditable,Xr=e=>xe.fromDom(e.getBody()),Zr=e=>t=>Re(t,Xr(e)),es=e=>{we(e,"data-mce-style");const t=e=>we(e,"data-mce-style");N(Vt(e),t),N(qt(e),t),N(Gt(e),t)},ts=e=>xe.fromDom(e.selection.getStart()),os=e=>e.getBoundingClientRect().width,ns=e=>e.getBoundingClientRect().height,rs=e=>(t,o)=>{const n=t.dom.getStyle(o,e)||t.dom.getAttrib(o,e);return C.from(n).filter(Dt)},ss=rs("width"),ls=rs("height"),as=e=>ft(e,ue("table")).exists(Jr),cs=(e,t)=>{const o=t.column,n=t.column+t.colspan-1,r=t.row,s=t.row+t.rowspan-1;return o<=e.finishCol&&n>=e.startCol&&r<=e.finishRow&&s>=e.startRow},is=(e,t)=>t.column>=e.startCol&&t.column+t.colspan-1<=e.finishCol&&t.row>=e.startRow&&t.row+t.rowspan-1<=e.finishRow,ms=(e,t,o)=>{const n=en(e,t,Re),r=en(e,o,Re);return n.bind((e=>r.map((t=>{return o=e,n=t,{startRow:Math.min(o.row,n.row),startCol:Math.min(o.column,n.column),finishRow:Math.max(o.row+o.rowspan-1,n.row+n.rowspan-1),finishCol:Math.max(o.column+o.colspan-1,n.column+n.colspan-1)};var o,n}))))},ds=(e,t,o)=>ms(e,t,o).map((t=>{const o=tn(e,b(cs,t));return E(o,(e=>e.element))})),us=(e,t)=>en(e,t,((e,t)=>De(t,e))).map((e=>e.element)),fs=(e,t,o)=>{const n=hs(e);return ds(n,t,o)},gs=(e,t,o,n,r)=>{const s=hs(e),l=Re(e,o)?C.some(t):us(s,t),a=Re(e,r)?C.some(n):us(s,n);return l.bind((e=>a.bind((t=>ds(s,e,t)))))},hs=Qo;var ps=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],bs=()=>({up:g({selector:gt,closest:bt,predicate:ut,all:_e}),down:g({selector:mt,predicate:at}),styles:g({get:Bt,getRaw:zt,set:Et,remove:At}),attrs:g({get:pe,set:ge,remove:we,copyTo:(e,t)=>{const o=ve(e);he(t,o)}}),insert:g({before:Me,after:je,afterAll:He,append:Ie,appendAll:$e,prepend:Pe,wrap:Fe}),remove:g({unwrap:Ue,remove:qe}),create:g({nu:xe.fromTag,clone:e=>xe.fromDom(e.dom.cloneNode(!1)),text:xe.fromText}),query:g({comparePosition:(e,t)=>e.dom.compareDocumentPosition(t.dom),prevSibling:ze,nextSibling:Ae}),property:g({children:Le,name:ne,parent:Ne,document:e=>Ee(e).dom,isText:ie,isComment:le,isElement:ce,isSpecial:e=>{const t=ne(e);return D(["script","noscript","iframe","noframes","noembed","title","style","textarea","xmp"],t)},getLanguage:e=>ce(e)?be(e,"lang"):C.none(),getText:gr,setText:pr,isBoundary:e=>!!ce(e)&&("body"===ne(e)||D(ps,ne(e))),isEmptyTag:e=>!!ce(e)&&D(["br","img","hr","input"],ne(e)),isNonEditable:e=>ce(e)&&"false"===pe(e,"contenteditable")}),eq:Re,is:Oe});const ws=(e,t,o,n)=>{const r=t(e,o);return z(n,((o,n)=>{const r=t(e,n);return vs(e,o,r)}),r)},vs=(e,t,o)=>t.bind((t=>o.filter(b(e.eq,t)))),ys=bs(),xs=(e,t)=>((e,t,o)=>o.length>0?((e,t,o,n)=>n(e,t,o[0],o.slice(1)))(e,t,o,ws):C.none())(ys,((t,o)=>e(o)),t),Cs=e=>gt(e,"table"),Ts=(e,t,o)=>{const n=e=>t=>void 0!==o&&o(t)||Re(t,e);return Re(e,t)?C.some({boxes:C.some([e]),start:e,finish:t}):Cs(e).bind((r=>Cs(t).bind((s=>{if(Re(r,s))return C.some({boxes:fs(r,e,t),start:e,finish:t});if(De(r,s)){const o=ct(t,"td,th",n(r)),l=o.length>0?o[o.length-1]:t;return C.some({boxes:gs(r,e,r,t,s),start:e,finish:l})}if(De(s,r)){const o=ct(e,"td,th",n(s)),l=o.length>0?o[o.length-1]:e;return C.some({boxes:gs(s,e,r,t,s),start:e,finish:l})}return((e,t,o)=>((e,t,o,n=y)=>{const r=[t].concat(e.up().all(t)),s=[o].concat(e.up().all(o)),l=e=>W(e,n).fold((()=>e),(t=>e.slice(0,t+1))),a=l(r),c=l(s),i=L(a,(t=>O(c,((e,t)=>b(e.eq,t))(e,t))));return{firstpath:a,secondpath:c,shared:i}})(ys,e,t,void 0))(e,t).shared.bind((l=>bt(l,"table",o).bind((o=>{const l=ct(t,"td,th",n(o)),a=l.length>0?l[l.length-1]:t,c=ct(e,"td,th",n(o)),i=c.length>0?c[c.length-1]:e;return C.some({boxes:gs(o,e,r,t,s),start:i,finish:a})}))))}))))},Ss=(e,t)=>{const o=mt(e,t);return o.length>0?C.some(o):C.none()},Rs=(e,t,o)=>pt(e,t).bind((t=>pt(e,o).bind((e=>xs(Cs,[t,e]).map((o=>({first:t,last:e,table:o}))))))),Ds=(e,t,o,n,r)=>((e,t)=>L(e,(e=>Ce(e,t))))(e,r).bind((e=>((e,t,o)=>Ut(e).bind((n=>((e,t,o,n)=>en(e,t,Re).bind((t=>{const r=o>0?t.row+t.rowspan-1:t.row,s=n>0?t.column+t.colspan-1:t.column;return Zo(e,r+o,s+n).map((e=>e.element))})))(hs(n),e,t,o))))(e,t,o).bind((e=>((e,t)=>gt(e,"table").bind((o=>pt(o,t).bind((t=>Ts(t,e).bind((e=>e.boxes.map((t=>({boxes:t,start:e.start,finish:e.finish}))))))))))(e,n))))),Os=(e,t)=>Ss(e,t),ks=(e,t,o)=>Rs(e,t,o).bind((t=>{const o=t=>Re(e,t),n="thead,tfoot,tbody,table",r=gt(t.first,n,o),s=gt(t.last,n,o);return r.bind((e=>s.bind((o=>Re(e,o)?((e,t,o)=>((e,t,o)=>ms(e,t,o).bind((t=>((e,t)=>{let o=!0;const n=b(is,t);for(let r=t.startRow;r<=t.finishRow;r++)for(let s=t.startCol;s<=t.finishCol;s++)o=o&&Zo(e,r,s).exists(n);return o?C.some(t):C.none()})(e,t))))(hs(e),t,o))(t.table,t.first,t.last):C.none()))))})),Es=h,Ns=e=>{const t=(e,t)=>be(e,t).exists((e=>parseInt(e,10)>1));return e.length>0&&P(e,(e=>t(e,"rowspan")||t(e,"colspan")))?C.some(e):C.none()},Bs=(e,t,o)=>t.length<=1?C.none():ks(e,o.firstSelectedSelector,o.lastSelectedSelector).map((e=>({bounds:e,cells:t}))),_s="data-mce-selected",zs="data-mce-first-selected",As="data-mce-last-selected",Ls="["+_s+"]",Ws={selected:_s,selectedSelector:"td["+_s+"],th["+_s+"]",firstSelected:zs,firstSelectedSelector:"td["+zs+"],th["+zs+"]",lastSelected:As,lastSelectedSelector:"td["+As+"],th["+As+"]"},Ms=(e,t,o)=>({element:o,mergable:Bs(t,e,Ws),unmergable:Ns(e),selection:Es(e)}),js=e=>(t,o)=>{const n=ne(t),r="col"===n||"colgroup"===n?Ut(s=t).bind((e=>Os(e,Ws.firstSelectedSelector))).fold(g(s),(e=>e[0])):t;var s;return bt(r,e,o)},Ps=js("th,td,caption"),Is=js("th,td"),Fs=e=>{return t=e.model.table.getSelectedCells(),E(t,xe.fromDom);var t},Hs=(e,t)=>{e.on("BeforeGetContent",(t=>{const o=o=>{t.preventDefault(),(e=>Ut(e[0]).map((e=>{const t=((e,t)=>{const o=e=>Ce(e.element,t),n=Ye(e),r=Qt(n),s=ir(e),l=Xo(r),a=((e,t)=>{const o=e.grid.columns;let n=e.grid.rows,r=o,s=0,l=0;const a=[],c=[];return G(e.access,(e=>{if(a.push(e),t(e)){c.push(e);const t=e.row,o=t+e.rowspan-1,a=e.column,i=a+e.colspan-1;t<n?n=t:o>s&&(s=o),a<r?r=a:i>l&&(l=i)}})),((e,t,o,n,r,s)=>({minRow:e,minCol:t,maxRow:o,maxCol:n,allCells:r,selectedCells:s}))(n,r,s,l,a,c)})(l,o),c="th:not("+t+"),td:not("+t+")",i=Ht(n,"th,td",(e=>Ce(e,c)));N(i,qe),((e,t,o,n)=>{const r=_(e,(e=>"colgroup"!==e.section)),s=t.grid.columns,l=t.grid.rows;for(let e=0;e<l;e++){let l=!1;for(let a=0;a<s;a++)e<o.minRow||e>o.maxRow||a<o.minCol||a>o.maxCol||(Zo(t,e,a).filter(n).isNone()?ur(r,l,e):l=!0)}})(r,l,a,o);const m=((e,t,o,n)=>{if(0===n.minCol&&t.grid.columns===n.maxCol+1)return 0;const r=rr(t,e,o),s=A(r,((e,t)=>e+t),0),l=A(r.slice(n.minCol,n.maxCol+1),((e,t)=>e+t),0),a=l/s*o.pixelWidth()-o.pixelWidth();return o.getCellDelta(a)})(e,Qo(e),s,a);return((e,t,o,n)=>{G(o.columns,(e=>{(e.column<t.minCol||e.column>t.maxCol)&&qe(e.element)}));const r=_(Ft(e,"tr"),(e=>0===e.dom.childElementCount));N(r,qe),t.minCol!==t.maxCol&&t.minRow!==t.maxRow||N(Ft(e,"th,td"),(e=>{we(e,"rowspan"),we(e,"colspan")})),we(e,qo),we(e,"data-snooker-col-series"),ir(e).adjustTableWidth(n)})(n,a,l,m),n})(e,Ls);return es(t),[t]})))(o).each((o=>{const n="text"===t.format?((e,t)=>{const o=e.getDoc(),n=nt(xe.fromDom(e.getBody())),r=xe.fromTag("div",o);ge(r,"data-mce-bogus","all"),Nt(r,{position:"fixed",left:"-9999999px",top:"0",overflow:"hidden",opacity:"0"});const s=(e=>ot(e)?e:xe.fromDom(Ee(e).dom.body))(n);$e(r,t),Ie(s,r);const l=r.dom.innerText;return qe(r),l})(e,o):((e,t)=>E(t,(t=>e.selection.serializer.serialize(t.dom,{}))).join(""))(e,o);t.content=n}))};if(!0===t.selection){const t=(e=>_(Fs(e),(e=>Ce(e,Ws.selectedSelector))))(e);t.length>=1&&o(t)}})),e.on("BeforeSetContent",(o=>{if(!0===o.selection&&!0===o.paste){const n=Fs(e);H(n).each((n=>{Ut(n).each((r=>{const s=_(((e,t)=>{const o=document.createElement("div");return o.innerHTML=e,Le(xe.fromDom(o))})(o.content),(e=>"meta"!==ne(e))),l=ue("table");if(Ur(e)&&1===s.length&&l(s[0])){o.preventDefault();const l=xe.fromDom(e.getDoc()),a=Br(l),c=((e,t,o)=>({element:e,clipboard:t,generators:o}))(n,s[0],a);t.pasteCells(r,c).each((()=>{e.focus()}))}}))}))}}))},$s=(e,t)=>({element:e,offset:t}),Vs=(e,t,o)=>e.property().isText(t)&&0===e.property().getText(t).trim().length||e.property().isComment(t)?o(t).bind((t=>Vs(e,t,o).orThunk((()=>C.some(t))))):C.none(),qs=(e,t)=>e.property().isText(t)?e.property().getText(t).length:e.property().children(t).length,Us=(e,t)=>{const o=Vs(e,t,e.query().prevSibling).getOr(t);if(e.property().isText(o))return $s(o,qs(e,o));const n=e.property().children(o);return n.length>0?Us(e,n[n.length-1]):$s(o,qs(e,o))},Gs=Us,Ks=bs(),Ys=(e,t)=>{if(!Mt(e)){const o=(e=>qn(e).bind((e=>{return t=e,o=["fixed","relative","empty"],C.from(An.exec(t)).bind((e=>{const t=Number(e[1]),n=e[2];return((e,t)=>O(t,(t=>O(zn[t],(t=>e===t)))))(n,o)?C.some({value:t,unit:n}):C.none()}));var t,o})))(e);o.each((o=>{const n=o.value/2;Yn(e,n,o.unit),Yn(t,n,o.unit)}))}},Js=e=>E(e,g(0)),Qs=(e,t,o,n,r)=>r(e.slice(0,t)).concat(n).concat(r(e.slice(o))),Xs=e=>(t,o,n,r)=>{if(e(n)){const e=Math.max(r,t[o]-Math.abs(n)),s=Math.abs(e-t[o]);return n>=0?s:-s}return n},Zs=Xs((e=>e<0)),el=Xs(x),tl=()=>{const e=(e,t,o,n)=>{const r=(100+o)/100,s=Math.max(n,(e[t]+o)/r);return E(e,((e,o)=>(o===t?s:e/r)-e))},t=(t,o,n,r,s,l)=>l?e(t,o,r,s):((e,t,o,n,r)=>{const s=Zs(e,t,n,r);return Qs(e,t,o+1,[s,0],Js)})(t,o,n,r,s);return{resizeTable:(e,t)=>e(t),clampTableDelta:Zs,calcLeftEdgeDeltas:t,calcMiddleDeltas:(e,o,n,r,s,l,a)=>t(e,n,r,s,l,a),calcRightEdgeDeltas:(t,o,n,r,s,l)=>{if(l)return e(t,n,r,s);{const e=Zs(t,n,r,s);return Js(t.slice(0,n)).concat([e])}},calcRedestributedWidths:(e,t,o,n)=>{if(n){const n=(t+o)/t,r=E(e,(e=>e/n));return{delta:100*n-100,newSizes:r}}return{delta:o,newSizes:e}}}},ol=()=>{const e=(e,t,o,n,r)=>{const s=el(e,n>=0?o:t,n,r);return Qs(e,t,o+1,[s,-s],Js)};return{resizeTable:(e,t,o)=>{o&&e(t)},clampTableDelta:(e,t,o,n,r)=>{if(r){if(o>=0)return o;{const t=A(e,((e,t)=>e+t-n),0);return Math.max(-t,o)}}return Zs(e,t,o,n)},calcLeftEdgeDeltas:e,calcMiddleDeltas:(t,o,n,r,s,l)=>e(t,n,r,s,l),calcRightEdgeDeltas:(e,t,o,n,r,s)=>{if(s)return Js(e);{const t=n/e.length;return E(e,g(t))}},calcRedestributedWidths:(e,t,o,n)=>({delta:0,newSizes:e})}},nl=e=>Qo(e).grid,rl=ue("th"),sl=e=>P(e,(e=>rl(e.element))),ll=(e,t)=>e&&t?"sectionCells":e?"section":"cells",al=e=>{const t="thead"===e.section,o=wt(cl(e.cells),"th");return"tfoot"===e.section?{type:"footer"}:t||o?{type:"header",subType:ll(t,o)}:{type:"body"}},cl=e=>{const t=_(e,(e=>rl(e.element)));return 0===t.length?C.some("td"):t.length===e.length?C.some("th"):C.none()},il=(e,t,o)=>et(o(e.element,t),!0,e.isLocked),ml=(e,t)=>e.section!==t?tt(e.element,e.cells,t,e.isNew):e,dl=()=>({transformRow:ml,transformCell:(e,t,o)=>{const n=o(e.element,t),r="td"!==ne(n)?((e,t)=>{const o=Je(e,"td");je(e,o);const n=Le(e);return $e(o,n),qe(e),o})(n):n;return et(r,e.isNew,e.isLocked)}}),ul=()=>({transformRow:ml,transformCell:il}),fl=()=>({transformRow:(e,t)=>ml(e,"thead"===t?"tbody":t),transformCell:il}),gl=dl,hl=ul,pl=fl,bl=()=>({transformRow:h,transformCell:il}),wl=(e,t,o,n)=>{o===n?we(e,t):ge(e,t,o)},vl=(e,t,o)=>{$(it(e,t)).fold((()=>Pe(e,o)),(e=>je(e,o)))},yl=(e,t)=>{const o=[],n=[],r=e=>E(e,(e=>{e.isNew&&o.push(e.element);const t=e.element;return Ve(t),N(e.cells,(e=>{e.isNew&&n.push(e.element),wl(e.element,"colspan",e.colspan,1),wl(e.element,"rowspan",e.rowspan,1),Ie(t,e.element)})),t})),s=e=>j(e,(e=>E(e.cells,(e=>(wl(e.element,"span",e.colspan,1),e.element))))),l=(t,o)=>{const n=((e,t)=>{const o=ht(e,t).getOrThunk((()=>{const o=xe.fromTag(t,ke(e).dom);return"thead"===t?vl(e,"caption,colgroup",o):"colgroup"===t?vl(e,"caption",o):Ie(e,o),o}));return Ve(o),o})(e,o),l=("colgroup"===o?s:r)(t);$e(n,l)},a=(t,o)=>{t.length>0?l(t,o):(t=>{ht(e,t).each(qe)})(o)},c=[],i=[],m=[],d=[];return N(t,(e=>{switch(e.section){case"thead":c.push(e);break;case"tbody":i.push(e);break;case"tfoot":m.push(e);break;case"colgroup":d.push(e)}})),a(d,"colgroup"),a(c,"thead"),a(i,"tbody"),a(m,"tfoot"),{newRows:o,newCells:n}},xl=(e,t)=>{if(0===e.length)return 0;const o=e[0];return W(e,(e=>!t(o.element,e.element))).getOr(e.length)},Cl=(e,t)=>{const o=E(e,(e=>E(e.cells,y)));return E(e,((n,r)=>{const s=j(n.cells,((n,s)=>{if(!1===o[r][s]){const m=((e,t,o,n)=>{const r=((e,t)=>e[t])(e,t),s="colgroup"===r.section,l=xl(r.cells.slice(o),n),a=s?1:xl(((e,t)=>E(e,(e=>Io(e,t))))(e.slice(t),o),n);return{colspan:l,rowspan:a}})(e,r,s,t);return((e,t,n,r)=>{for(let s=e;s<e+n;s++)for(let e=t;e<t+r;e++)o[s][e]=!0})(r,s,m.rowspan,m.colspan),[(l=n.element,a=m.rowspan,c=m.colspan,i=n.isNew,{element:l,rowspan:a,colspan:c,isNew:i})]}return[];var l,a,c,i}));return((e,t,o,n)=>({element:e,cells:t,section:o,isNew:n}))(n.element,s,n.section,n.isNew)}))},Tl=(e,t,o)=>{const n=[];N(e.colgroups,(r=>{const s=[];for(let n=0;n<e.grid.columns;n++){const r=sn(e,n).map((e=>et(e.element,o,!1))).getOrThunk((()=>et(t.colGap(),!0,!1)));s.push(r)}n.push(tt(r.element,s,"colgroup",o))}));for(let r=0;r<e.grid.rows;r++){const s=[];for(let n=0;n<e.grid.columns;n++){const l=Zo(e,r,n).map((e=>et(e.element,o,e.isLocked))).getOrThunk((()=>et(t.gap(),!0,!1)));s.push(l)}const l=e.all[r],a=tt(l.element,s,l.section,o);n.push(a)}return n},Sl=e=>Cl(e,Re),Rl=(e,t)=>V(e.all,(e=>L(e.cells,(e=>Re(t,e.element))))),Dl=(e,t,o)=>{const n=E(t.selection,(t=>$t(t).bind((t=>Rl(e,t))).filter(o))),r=vt(n);return yt(r.length>0,r)},Ol=(e,t,o,n,r)=>(s,l,a,c)=>{const i=Qo(s),m=C.from(null==c?void 0:c.section).getOrThunk(bl);return t(i,l).map((t=>{const o=((e,t)=>Tl(e,t,!1))(i,a),n=e(o,t,Re,r(a),m),s=Go(n.grid);return{info:t,grid:Sl(n.grid),cursor:n.cursor,lockedColumns:s}})).bind((e=>{const t=yl(s,e.grid),r=C.from(null==c?void 0:c.sizing).getOrThunk((()=>ir(s))),l=C.from(null==c?void 0:c.resize).getOrThunk(ol);return o(s,e.grid,e.info,{sizing:r,resize:l,section:m}),n(s),we(s,qo),e.lockedColumns.length>0&&ge(s,qo,e.lockedColumns.join(",")),C.some({cursor:e.cursor,newRows:t.newRows,newCells:t.newCells})}))},kl=(e,t)=>Dl(e,t,x).map((e=>({cells:e,generators:t.generators,clipboard:t.clipboard}))),El=(e,t)=>Dl(e,t,x),Nl=(e,t)=>Dl(e,t,(e=>!e.isLocked)),Bl=(e,t)=>P(t,(t=>((e,t)=>Rl(e,t).exists((e=>!e.isLocked)))(e,t))),_l=(e,t,o,n)=>{const r=$o(e).rows;let s=!0;for(let e=0;e<r.length;e++)for(let l=0;l<Ho(r[0]);l++){const a=r[e],c=Io(a,l),i=o(c.element,t);i&&!s?jo(a,l,et(n(),!0,c.isLocked)):i&&(s=!1)}return e},zl=e=>{const t=t=>t(e),o=g(e),n=()=>r,r={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:x,isError:y,map:t=>Ll.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>C.some(e)};return r},Al=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:y,isError:x,map:t,mapError:t=>Ll.error(t(e)),bind:t,exists:y,forall:x,getOr:h,or:h,getOrThunk:v,orThunk:v,getOrDie:(n=String(e),()=>{throw new Error(n)}),each:f,toOptional:C.none};var n;return o},Ll={value:zl,error:Al,fromOption:(e,t)=>e.fold((()=>Al(t)),zl)},Wl=(e,t)=>({rowDelta:0,colDelta:Ho(e[0])-Ho(t[0])}),Ml=(e,t)=>({rowDelta:e.length-t.length,colDelta:0}),jl=(e,t,o,n)=>{const r="colgroup"===t.section?o.col:o.cell;return k(e,(e=>et(r(),!0,n(e))))},Pl=(e,t,o,n)=>{const r=e[e.length-1];return e.concat(k(t,(()=>{const e="colgroup"===r.section?o.colgroup:o.row,t=Vo(r,e,h),s=jl(t.cells.length,t,o,(e=>X(n,e.toString())));return Po(t,s)})))},Il=(e,t,o,n)=>E(e,(e=>{const r=jl(t,e,o,y);return Wo(e,n,r)})),Fl=(e,t,o)=>{const n=t.colDelta<0?Il:h,r=t.rowDelta<0?Pl:h,s=Go(e),l=Ho(e[0]),a=O(s,(e=>e===l-1)),c=n(e,Math.abs(t.colDelta),o,a?l-1:l),i=Go(c);return r(c,Math.abs(t.rowDelta),o,I(i,x))},Hl=(e,t,o,n)=>{const r=b(n,Io(e[t],o).element),s=e[t];return e.length>1&&Ho(s)>1&&(o>0&&r(Fo(s,o-1))||o<s.cells.length-1&&r(Fo(s,o+1))||t>0&&r(Fo(e[t-1],o))||t<e.length-1&&r(Fo(e[t+1],o)))},$l=(e,t,o)=>_(o,(o=>o>=e.column&&o<=Ho(t[0])+e.column)),Vl=(e,t,o,n,r)=>{((e,t,o,n)=>{t>0&&t<e[0].cells.length&&N(e,(e=>{const r=e.cells[t-1];let s=0;const l=n();for(;e.cells.length>t+s&&o(r.element,e.cells[t+s].element);)jo(e,t+s,et(l,!0,e.cells[t+s].isLocked)),s++}))})(t,e,r,n.cell);const s=Ml(o,t),l=Fl(o,s,n),a=Ml(t,l),c=Fl(t,a,n);return E(c,((t,o)=>Wo(t,e,l[o].cells)))},ql=(e,t,o,n,r)=>{((e,t,o,n)=>{const r=$o(e).rows;if(t>0&&t<r.length){const e=((e,t)=>A(e,((e,o)=>O(e,(e=>t(e.element,o.element)))?e:e.concat([o])),[]))(r[t-1].cells,o);N(e,(e=>{let s=C.none();for(let l=t;l<r.length;l++)for(let t=0;t<Ho(r[0]);t++){const a=r[l],c=Io(a,t);o(c.element,e.element)&&(s.isNone()&&(s=C.some(n())),s.each((e=>{jo(a,t,et(e,!0,c.isLocked))})))}}))}})(t,e,r,n.cell);const s=Go(t),l=Wl(t,o),a={...l,colDelta:l.colDelta-s.length},c=Fl(t,a,n),{cols:i,rows:m}=$o(c),d=Go(c),u=Wl(o,t),f={...u,colDelta:u.colDelta+d.length},g=(p=n,b=d,E(o,(e=>A(b,((t,o)=>{const n=jl(1,e,p,x)[0];return Mo(t,o,n)}),e)))),h=Fl(g,f,n);var p,b;return[...i,...m.slice(0,e),...h,...m.slice(e,m.length)]},Ul=(e,t,o,n,r)=>{const{rows:s,cols:l}=$o(e),a=s.slice(0,t),c=s.slice(t);return[...l,...a,((e,t,o,n)=>Vo(e,(e=>n(e,o)),t))(s[o],((e,o)=>t>0&&t<s.length&&n(Fo(s[t-1],o),Fo(s[t],o))?Io(s[t],o):et(r(e.element,n),!0,e.isLocked)),n,r),...c]},Gl=(e,t,o,n,r)=>E(e,(e=>{const s=t>0&&t<Ho(e)&&n(Fo(e,t-1),Fo(e,t)),l=((e,t,o,n,r,s,l)=>{if("colgroup"!==o&&n)return Io(e,t);{const t=Io(e,r);return et(l(t.element,s),!0,!1)}})(e,t,e.section,s,o,n,r);return Mo(e,t,l)})),Kl=(e,t,o,n)=>((e,t,o,n)=>void 0!==Fo(e[t],o)&&t>0&&n(Fo(e[t-1],o),Fo(e[t],o)))(e,t,o,n)||((e,t,o)=>t>0&&o(Fo(e,t-1),Fo(e,t)))(e[t],o,n),Yl=(e,t,o,n)=>{const r=e=>(e=>"row"===e?(e=>Wt(e,"rowspan")>1)(t):Mt(t))(e)?`${e}group`:e;return e?rl(t)?r(o):null:n&&rl(t)?r("row"===o?"col":"row"):null},Jl=(e,t,o)=>et(o(e.element,t),!0,e.isLocked),Ql=(e,t,o,n,r,s,l)=>E(e,((e,a)=>((e,c)=>{const i=e.cells,m=E(i,((e,c)=>{if((e=>O(t,(t=>o(e.element,t.element))))(e)){const t=l(e,a,c)?r(e,o,n):e;return s(t,a,c).each((e=>{var o,n;o=t.element,n={scope:C.from(e)},G(n,((e,t)=>{e.fold((()=>{we(o,t)}),(e=>{fe(o.dom,t,e)}))}))})),t}return e}));return tt(e.element,m,e.section,e.isNew)})(e))),Xl=(e,t,o)=>j(e,((n,r)=>Kl(e,r,t,o)?[]:[Io(n,t)])),Zl=(e,t,o,n,r)=>{const s=$o(e).rows,l=j(t,(e=>Xl(s,e,n))),a=E(s,(e=>sl(e.cells))),c=((e,t)=>P(t,h)&&sl(e)?x:(e,o,n)=>!("th"===ne(e.element)&&t[o]))(l,a),i=((e,t)=>(o,n)=>C.some(Yl(e,o.element,"row",t[n])))(o,a);return Ql(e,l,n,r,Jl,i,c)},ea=(e,t,o,n)=>{const r=$o(e).rows,s=E(t,(e=>Io(r[e.row],e.column)));return Ql(e,s,o,n,Jl,C.none,x)},ta=e=>{if(!l(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return N(e,((n,r)=>{const s=q(n);if(1!==s.length)throw new Error("one and only one name per case");const a=s[0],c=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!l(c))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==c.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+c.length+" ("+c+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[r].apply(null,o)},match:e=>{const n=q(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!P(t,(e=>D(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o},oa={...ta([{none:[]},{only:["index"]},{left:["index","next"]},{middle:["prev","index","next"]},{right:["prev","index"]}])},na=(e,t,o)=>{const n=((e,t)=>rn(e)?((e,t)=>{const o=nn(e);return E(o,((e,o)=>({element:e.element,width:t[o],colspan:e.colspan})))})(e,t):((e,t)=>{const o=on(e);return E(o,(e=>{const o=((e,t,o)=>{let n=0;for(let r=e;r<t;r++)n+=void 0!==o[r]?o[r]:0;return n})(e.column,e.column+e.colspan,t);return{element:e.element,width:o,colspan:e.colspan}}))})(e,t))(e,t);N(n,(e=>{o.setElementWidth(e.element,e.width)}))},ra=(e,t,o,n,r)=>{const s=Qo(e),l=r.getCellDelta(t),a=r.getWidths(s,r),c=o===s.grid.columns-1,i=n.clampTableDelta(a,o,l,r.minCellWidth(),c),m=((e,t,o,n,r)=>{const s=e.slice(0),l=((e,t)=>0===e.length?oa.none():1===e.length?oa.only(0):0===t?oa.left(0,1):t===e.length-1?oa.right(t-1,t):t>0&&t<e.length-1?oa.middle(t-1,t,t+1):oa.none())(e,t),a=g(E(s,g(0)));return l.fold(a,(e=>n.singleColumnWidth(s[e],o)),((e,t)=>r.calcLeftEdgeDeltas(s,e,t,o,n.minCellWidth(),n.isRelative)),((e,t,l)=>r.calcMiddleDeltas(s,e,t,l,o,n.minCellWidth(),n.isRelative)),((e,t)=>r.calcRightEdgeDeltas(s,e,t,o,n.minCellWidth(),n.isRelative)))})(a,o,i,r,n),d=E(m,((e,t)=>e+a[t]));na(s,d,r),n.resizeTable(r.adjustTableWidth,i,c)},sa=(e,t,o)=>{const n=Qo(e),r=((e,t)=>sr(e,t,Kn,(e=>e.getOrThunk(It))))(n,e),s=E(r,((e,n)=>o===n?Math.max(t+e,It()):e)),l=((e,t)=>E(e.all,((e,o)=>({element:e.element,height:t[o]}))))(n,s);N(l,(e=>{Hn(e.element,e.height)})),N(on(n),(e=>{(e=>{At(e,"height")})(e.element)}));const a=z(s,((e,t)=>e+t),0);Hn(e,a)},la=e=>A(e,((e,t)=>O(e,(e=>e.column===t.column))?e:e.concat([t])),[]).sort(((e,t)=>e.column-t.column)),aa=ue("col"),ca=ue("colgroup"),ia=e=>"tr"===ne(e)||ca(e),ma=e=>({element:e,colspan:Lt(e,"colspan",1),rowspan:Lt(e,"rowspan",1)}),da=e=>be(e,"scope").map((e=>e.substr(0,3))),ua=(e,t=ma)=>{const o=o=>{if(ia(o))return ca((r={element:o}).element)?e.colgroup(r):e.row(r);{const r=o,s=(t=>aa(t.element)?e.col(t):e.cell(t))(t(r));return n=C.some({item:r,replacement:s}),s}var r};let n=C.none();return{getOrInit:(e,t)=>n.fold((()=>o(e)),(n=>t(e,n.item)?n.replacement:o(e)))}},fa=e=>t=>{const o=[],n=n=>{const r="td"===e?{scope:null}:{},s=t.replace(n,e,r);return o.push({item:n,sub:s}),s};return{replaceOrInit:(e,t)=>{if(ia(e)||aa(e))return e;{const r=e;return((e,t)=>L(o,(o=>t(o.item,e))))(r,t).fold((()=>n(r)),(o=>t(e,o.item)?o.sub:n(r)))}}}},ga=e=>({unmerge:t=>{const o=da(t);return o.each((e=>ge(t,"scope",e))),()=>{const n=e.cell({element:t,colspan:1,rowspan:1});return At(n,"width"),At(t,"width"),o.each((e=>ge(n,"scope",e))),n}},merge:e=>(At(e[0],"width"),(()=>{const t=vt(E(e,da));if(0===t.length)return C.none();{const e=t[0],o=["row","col"];return O(t,(t=>t!==e&&D(o,t)))?C.none():C.from(e)}})().fold((()=>we(e[0],"scope")),(t=>ge(e[0],"scope",t+"group"))),g(e[0]))}),ha=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","table","thead","tfoot","tbody","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],pa=bs(),ba=e=>((e,t)=>{const o=e.property().name(t);return D(ha,o)})(pa,e),wa=e=>((e,t)=>{const o=e.property().name(t);return D(["ol","ul"],o)})(pa,e),va=e=>{const t=ue("br"),o=e=>xr(e).bind((o=>{const n=Ae(o).map((e=>!!ba(e)||!!((e,t)=>D(["br","img","hr","input"],e.property().name(t)))(pa,e)&&"img"!==ne(e))).getOr(!1);return Ne(o).map((r=>{return!0===n||("li"===ne(s=r)||ut(s,wa).isSome())||t(o)||ba(r)&&!Re(e,r)?[]:[xe.fromTag("br")];var s}))})).getOr([]),n=(()=>{const n=j(e,(e=>{const n=Le(e);return(e=>P(e,(e=>t(e)||ie(e)&&0===gr(e).trim().length)))(n)?[]:n.concat(o(e))}));return 0===n.length?[xe.fromTag("br")]:n})();Ve(e[0]),$e(e[0],n)},ya=e=>Jr(e,!0),xa=e=>{0===Vt(e).length&&qe(e)},Ca=(e,t)=>({grid:e,cursor:t}),Ta=(e,t,o)=>{const n=((e,t,o)=>{var n,r;const s=$o(e).rows;return C.from(null===(r=null===(n=s[t])||void 0===n?void 0:n.cells[o])||void 0===r?void 0:r.element).filter(ya).orThunk((()=>(e=>V(e,(e=>V(e.cells,(e=>{const t=e.element;return yt(ya(t),t)})))))(s)))})(e,t,o);return Ca(e,n)},Sa=e=>A(e,((e,t)=>O(e,(e=>e.row===t.row))?e:e.concat([t])),[]).sort(((e,t)=>e.row-t.row)),Ra=(e,t)=>(o,n,r,s,l)=>{const a=Sa(n),c=E(a,(e=>e.row)),i=((e,t,o,n,r,s,l)=>{const{cols:a,rows:c}=$o(e),i=c[t[0]],m=j(t,(e=>((e,t,o)=>{const n=e[t];return j(n.cells,((n,r)=>Kl(e,t,r,o)?[]:[n]))})(c,e,r))),d=E(i.cells,((e,t)=>sl(Xl(c,t,r)))),u=[...c];N(t,(e=>{u[e]=l.transformRow(c[e],o)}));const f=[...a,...u],g=((e,t)=>P(t,h)&&sl(e.cells)?x:(e,o,n)=>!("th"===ne(e.element)&&t[n]))(i,d),p=((e,t)=>(o,n,r)=>C.some(Yl(e,o.element,"col",t[r])))(n,d);return Ql(f,m,r,s,l.transformCell,p,g)})(o,c,e,t,r,s.replaceOrInit,l);return Ta(i,n[0].row,n[0].column)},Da=Ra("thead",!0),Oa=Ra("tbody",!1),ka=Ra("tfoot",!1),Ea=(e,t,o)=>{const n=((e,t)=>Yt(e,(()=>t)))(e,o.section),r=Xo(n);return Tl(r,t,!0)},Na=(e,t,o,n)=>((e,t,o,n)=>{const r=Xo(t),s=n.getWidths(r,n);na(r,s,n)})(0,t,0,n.sizing),Ba=(e,t,o,n)=>((e,t,o,n,r)=>{const s=Xo(t),l=n.getWidths(s,n),a=n.pixelWidth(),{newSizes:c,delta:i}=r.calcRedestributedWidths(l,a,o.pixelDelta,n.isRelative);na(s,c,n),n.adjustTableWidth(i)})(0,t,o,n.sizing,n.resize),_a=(e,t)=>O(t,(e=>0===e.column&&e.isLocked)),za=(e,t)=>O(t,(t=>t.column+t.colspan>=e.grid.columns&&t.isLocked)),Aa=(e,t)=>{const o=ln(e),n=la(t);return A(n,((e,t)=>e+o[t.column].map(Ao).getOr(0)),0)},La=e=>(t,o)=>El(t,o).filter((o=>!(e?_a:za)(t,o))).map((e=>({details:e,pixelDelta:Aa(t,e)}))),Wa=e=>(t,o)=>kl(t,o).filter((o=>!(e?_a:za)(t,o.cells))),Ma=fa("th"),ja=fa("td"),Pa=Ol(((e,t,o,n)=>{const r=t[0].row,s=Sa(t),l=z(s,((e,t)=>({grid:Ul(e.grid,r,t.row+e.delta,o,n.getOrInit),delta:e.delta+1})),{grid:e,delta:0}).grid;return Ta(l,r,t[0].column)}),El,f,f,ua),Ia=Ol(((e,t,o,n)=>{const r=Sa(t),s=r[r.length-1],l=s.row+s.rowspan,a=z(r,((e,t)=>Ul(e,l,t.row,o,n.getOrInit)),e);return Ta(a,l,t[0].column)}),El,f,f,ua),Fa=Ol(((e,t,o,n)=>{const r=t.details,s=la(r),l=s[0].column,a=z(s,((e,t)=>({grid:Gl(e.grid,l,t.column+e.delta,o,n.getOrInit),delta:e.delta+1})),{grid:e,delta:0}).grid;return Ta(a,r[0].row,l)}),La(!0),Ba,f,ua),Ha=Ol(((e,t,o,n)=>{const r=t.details,s=r[r.length-1],l=s.column+s.colspan,a=la(r),c=z(a,((e,t)=>Gl(e,l,t.column,o,n.getOrInit)),e);return Ta(c,r[0].row,l)}),La(!1),Ba,f,ua),$a=Ol(((e,t,o,n)=>{const r=la(t.details),s=((e,t)=>j(e,(e=>{const o=e.cells,n=z(t,((e,t)=>t>=0&&t<e.length?e.slice(0,t).concat(e.slice(t+1)):e),o);return n.length>0?[tt(e.element,n,e.section,e.isNew)]:[]})))(e,E(r,(e=>e.column))),l=s.length>0?s[0].cells.length-1:0;return Ta(s,r[0].row,Math.min(r[0].column,l))}),((e,t)=>Nl(e,t).map((t=>({details:t,pixelDelta:-Aa(e,t)})))),Ba,xa,ua),Va=Ol(((e,t,o,n)=>{const r=Sa(t),s=((e,t,o)=>{const{rows:n,cols:r}=$o(e);return[...r,...n.slice(0,t),...n.slice(o+1)]})(e,r[0].row,r[r.length-1].row),l=Math.max($o(s).rows.length-1,0);return Ta(s,Math.min(t[0].row,l),t[0].column)}),El,f,xa,ua),qa=Ol(((e,t,o,n)=>{const r=la(t),s=E(r,(e=>e.column)),l=Zl(e,s,!0,o,n.replaceOrInit);return Ta(l,t[0].row,t[0].column)}),Nl,f,f,Ma),Ua=Ol(((e,t,o,n)=>{const r=la(t),s=E(r,(e=>e.column)),l=Zl(e,s,!1,o,n.replaceOrInit);return Ta(l,t[0].row,t[0].column)}),Nl,f,f,ja),Ga=Ol(Da,Nl,f,f,Ma),Ka=Ol(Oa,Nl,f,f,ja),Ya=Ol(ka,Nl,f,f,ja),Ja=Ol(((e,t,o,n)=>{const r=ea(e,t,o,n.replaceOrInit);return Ta(r,t[0].row,t[0].column)}),Nl,f,f,Ma),Qa=Ol(((e,t,o,n)=>{const r=ea(e,t,o,n.replaceOrInit);return Ta(r,t[0].row,t[0].column)}),Nl,f,f,ja),Xa=Ol(((e,t,o,n)=>{const r=t.cells;va(r);const s=((e,t,o,n)=>{const r=$o(e).rows;if(0===r.length)return e;for(let e=t.startRow;e<=t.finishRow;e++)for(let o=t.startCol;o<=t.finishCol;o++){const t=r[e],s=Io(t,o).isLocked;jo(t,o,et(n(),!1,s))}return e})(e,t.bounds,0,n.merge(r));return Ca(s,C.from(r[0]))}),((e,t)=>((e,t)=>t.mergable)(0,t).filter((t=>Bl(e,t.cells)))),Na,f,ga),Za=Ol(((e,t,o,n)=>{const r=z(t,((e,t)=>_l(e,t,o,n.unmerge(t))),e);return Ca(r,C.from(t[0]))}),((e,t)=>((e,t)=>t.unmergable)(0,t).filter((t=>Bl(e,t)))),Na,f,ga),ec=Ol(((e,t,o,n)=>{const r=((e,t)=>{const o=Qo(e);return Tl(o,t,!0)})(t.clipboard,t.generators);var s,l;return((e,t,o,n,r)=>{const s=Go(t),l=((e,t,o)=>{const n=Ho(t[0]),r=$o(t).cols.length+e.row,s=k(n-e.column,(t=>t+e.column));return{row:r,column:L(s,(e=>P(o,(t=>t!==e)))).getOr(n-1)}})(e,t,s),a=$o(o).rows,c=$l(l,a,s),i=((e,t,o)=>{if(e.row>=t.length||e.column>Ho(t[0]))return Ll.error("invalid start address out of table bounds, row: "+e.row+", column: "+e.column);const n=t.slice(e.row),r=n[0].cells.slice(e.column),s=Ho(o[0]),l=o.length;return Ll.value({rowDelta:n.length-l,colDelta:r.length-s})})(l,t,a);return i.map((e=>{const o={...e,colDelta:e.colDelta-c.length},s=Fl(t,o,n),i=Go(s),m=$l(l,a,i);return((e,t,o,n,r,s)=>{const l=e.row,a=e.column,c=l+o.length,i=a+Ho(o[0])+s.length,m=I(s,x);for(let e=l;e<c;e++){let s=0;for(let c=a;c<i;c++){if(m[c]){s++;continue}Hl(t,e,c,r)&&_l(t,Fo(t[e],c),r,n.cell);const i=c-a-s,d=Io(o[e-l],i),u=d.element,f=n.replace(u);jo(t[e],c,et(f,!0,d.isLocked))}}return t})(l,s,a,n,r,m)}))})((s=t.row,l=t.column,{row:s,column:l}),e,r,t.generators,o).fold((()=>Ca(e,C.some(t.element))),(e=>Ta(e,t.row,t.column)))}),((e,t)=>$t(t.element).bind((o=>Rl(e,o).map((e=>({...e,generators:t.generators,clipboard:t.clipboard})))))),Na,f,ua),tc=Ol(((e,t,o,n)=>{const r=$o(e).rows,s=t.cells[0].column,l=r[t.cells[0].row],a=Ea(t.clipboard,t.generators,l),c=Vl(s,e,a,t.generators,o);return Ta(c,t.cells[0].row,t.cells[0].column)}),Wa(!0),f,f,ua),oc=Ol(((e,t,o,n)=>{const r=$o(e).rows,s=t.cells[t.cells.length-1].column+t.cells[t.cells.length-1].colspan,l=r[t.cells[0].row],a=Ea(t.clipboard,t.generators,l),c=Vl(s,e,a,t.generators,o);return Ta(c,t.cells[0].row,s)}),Wa(!1),f,f,ua),nc=Ol(((e,t,o,n)=>{const r=$o(e).rows,s=t.cells[0].row,l=r[s],a=Ea(t.clipboard,t.generators,l),c=ql(s,e,a,t.generators,o);return Ta(c,t.cells[0].row,t.cells[0].column)}),kl,f,f,ua),rc=Ol(((e,t,o,n)=>{const r=$o(e).rows,s=t.cells[t.cells.length-1].row+t.cells[t.cells.length-1].rowspan,l=r[t.cells[0].row],a=Ea(t.clipboard,t.generators,l),c=ql(s,e,a,t.generators,o);return Ta(c,s,t.cells[0].column)}),kl,f,f,ua),sc=(e,t)=>{const o=Qo(e);return El(o,t).bind((e=>{const t=e[e.length-1],n=e[0].column,r=t.column+t.colspan,s=M(E(o.all,(e=>_(e.cells,(e=>e.column>=n&&e.column<r)))));return cl(s)})).getOr("")},lc=(e,t)=>{const o=Qo(e);return El(o,t).bind(cl).getOr("")},ac=(e,t)=>{const o=Qo(e);return El(o,t).bind((e=>{const t=e[e.length-1],n=e[0].row,r=t.row+t.rowspan;return(e=>{const t=E(e,(e=>al(e).type)),o=D(t,"header"),n=D(t,"footer");if(o||n){const e=D(t,"body");return!o||e||n?o||e||!n?C.none():C.some("footer"):C.some("header")}return C.some("body")})(o.all.slice(n,r))})).getOr("")},cc=(e,t)=>e.dispatch("NewRow",{node:t}),ic=(e,t)=>e.dispatch("NewCell",{node:t}),mc=(e,t,o)=>{e.dispatch("TableModified",{...o,table:t})},dc={structure:!1,style:!0},uc={structure:!0,style:!1},fc={structure:!0,style:!0},gc=(e,t)=>Fr(e)?dr(t):Hr(e)?mr(t):ir(t),hc=(e,t,o)=>{const n=e=>"table"===ne(Xr(e)),r=Lr(e),s=Pr(e)?f:Ys,l=t=>{switch(Wr(e)){case"section":return gl();case"sectionCells":return hl();case"cells":return pl();default:return((e,t)=>{var o;switch((o=Qo(e),V(o.all,(e=>{const t=al(e);return"header"===t.type?C.from(t.subType):C.none()}))).getOr(t)){case"section":return dl();case"sectionCells":return ul();case"cells":return fl()}})(t,"section")}},a=(n,s,a,c)=>(i,m,d=!1)=>{es(i);const u=xe.fromDom(e.getDoc()),f=Nr(a,u,r),g={sizing:gc(e,i),resize:Pr(e)?tl():ol(),section:l(i)};return s(i)?n(i,m,f,g).bind((n=>{t.refresh(i.dom),N(n.newRows,(t=>{cc(e,t.dom)})),N(n.newCells,(t=>{ic(e,t.dom)}));const r=((t,n)=>n.cursor.fold((()=>{const n=Vt(t);return H(n).filter(st).map((n=>{o.clearSelectedCells(t.dom);const r=e.dom.createRng();return r.selectNode(n.dom),e.selection.setRng(r),ge(n,"data-mce-selected","1"),r}))}),(n=>{const r=Gs(Ks,n),s=e.dom.createRng();return s.setStart(r.element.dom,r.offset),s.setEnd(r.element.dom,r.offset),e.selection.setRng(s),o.clearSelectedCells(t.dom),C.some(s)})))(i,n);return st(i)&&(es(i),d||mc(e,i.dom,c)),r.map((e=>({rng:e,effect:c})))})):C.none()},c=a(Va,(t=>!n(e)||nl(t).rows>1),f,uc),i=a($a,(t=>!n(e)||nl(t).columns>1),f,uc);return{deleteRow:c,deleteColumn:i,insertRowsBefore:a(Pa,x,f,uc),insertRowsAfter:a(Ia,x,f,uc),insertColumnsBefore:a(Fa,x,s,uc),insertColumnsAfter:a(Ha,x,s,uc),mergeCells:a(Xa,x,f,uc),unmergeCells:a(Za,x,f,uc),pasteColsBefore:a(tc,x,f,uc),pasteColsAfter:a(oc,x,f,uc),pasteRowsBefore:a(nc,x,f,uc),pasteRowsAfter:a(rc,x,f,uc),pasteCells:a(ec,x,f,fc),makeCellsHeader:a(Ja,x,f,uc),unmakeCellsHeader:a(Qa,x,f,uc),makeColumnsHeader:a(qa,x,f,uc),unmakeColumnsHeader:a(Ua,x,f,uc),makeRowsHeader:a(Ga,x,f,uc),makeRowsBody:a(Ka,x,f,uc),makeRowsFooter:a(Ya,x,f,uc),getTableRowType:ac,getTableCellType:lc,getTableColType:sc}},pc=(e,t,o)=>{const n=Lt(e,t,1);1===o||n<=1?we(e,t):ge(e,t,Math.min(o,n))},bc=(e,t)=>o=>{const n=o.column+o.colspan-1,r=o.column;return n>=e&&r<t},wc=ta([{invalid:["raw"]},{pixels:["value"]},{percent:["value"]}]),vc=(e,t,o)=>{const n=o.substring(0,o.length-e.length),r=parseFloat(n);return n===r.toString()?t(r):wc.invalid(o)},yc={...wc,from:e=>St(e,"%")?vc("%",wc.percent,e):St(e,"px")?vc("px",wc.pixels,e):wc.invalid(e)},xc=(e,t,o)=>{const n=yc.from(o),r=P(e,(e=>"0px"===e))?((e,t)=>{const o=e.fold((()=>g("")),(e=>g(e/t+"px")),(()=>g(100/t+"%")));return k(t,o)})(n,e.length):((e,t,o)=>e.fold((()=>t),(e=>((e,t,o)=>{const n=o/t;return E(e,(e=>yc.from(e).fold((()=>e),(e=>e*n+"px"),(e=>e/100*o+"px"))))})(t,o,e)),(e=>((e,t)=>E(e,(e=>yc.from(e).fold((()=>e),(e=>e/t*100+"%"),(e=>e+"%")))))(t,o))))(n,e,t);return Sc(r)},Cc=(e,t)=>0===e.length?t:z(e,((e,t)=>yc.from(t).fold(g(0),h,h)+e),0),Tc=(e,t)=>yc.from(e).fold(g(e),(e=>e+t+"px"),(e=>e+t+"%")),Sc=e=>{if(0===e.length)return e;const t=z(e,((e,t)=>{const o=yc.from(t).fold((()=>({value:t,remainder:0})),(e=>((e,t)=>{const o=Math.floor(e);return{value:o+"px",remainder:e-o}})(e)),(e=>({value:e+"%",remainder:0})));return{output:[o.value].concat(e.output),remainder:e.remainder+o.remainder}}),{output:[],remainder:0}),o=t.output;return o.slice(0,o.length-1).concat([Tc(o[o.length-1],Math.round(t.remainder))])},Rc=yc.from,Dc=(e,t,o)=>{const n=Qo(e),r=n.all,s=on(n),l=nn(n);t.each((t=>{const o=Rc(t).fold(g("px"),g("px"),g("%")),r=zo(e),a=((e,t)=>or(e,t,Zn,nr))(n,e),c=xc(a,r,t);rn(n)?((e,t,o)=>{N(t,((t,n)=>{const r=Cc([e[n]],Pt());Et(t.element,"width",r+o)}))})(c,l,o):((e,t,o)=>{N(t,(t=>{const n=e.slice(t.column,t.colspan+t.column),r=Cc(n,Pt());Et(t.element,"width",r+o)}))})(c,s,o),Et(e,"width",t)})),o.each((t=>{const o=fn(e),l=((e,t)=>sr(e,t,er,nr))(n,e);((e,t,o)=>{N(o,(e=>{At(e.element,"height")})),N(t,((t,o)=>{Et(t.element,"height",e[o])}))})(xc(l,o,t),r,s),Et(e,"height",t)}))},Oc=e=>qn(e).exists((e=>Ln.test(e))),kc=e=>qn(e).exists((e=>Wn.test(e))),Ec=e=>qn(e).isNone(),Nc=e=>{we(e,"width"),we(e,"height")},Bc=e=>{const t=Jn(e);Dc(e,C.some(t),C.none()),Nc(e)},_c=e=>{const t=(e=>zo(e)+"px")(e);Dc(e,C.some(t),C.none()),Nc(e)},zc=e=>{At(e,"width");const t=qt(e),o=t.length>0?t:Vt(e);N(o,(e=>{At(e,"width"),Nc(e)})),Nc(e)},Ac={styles:{"border-collapse":"collapse",width:"100%"},attributes:{border:"1"},colGroups:!1},Lc=(e,t,o,n)=>k(e,(e=>((e,t,o,n)=>{const r=xe.fromTag("tr");for(let s=0;s<e;s++){const e=xe.fromTag(n<t||s<o?"th":"td");s<o&&ge(e,"scope","row"),n<t&&ge(e,"scope","col"),Ie(e,xe.fromTag("br")),Ie(r,e)}return r})(t,o,n,e))),Wc=(e,t)=>{e.selection.select(t.dom,!0),e.selection.collapse(!0)},Mc=(e,t,o,n,s)=>{const l=(e=>{const t=e.options,o=t.get("table_default_styles");return t.isSet("table_default_styles")?o:((e,t)=>$r(e)||!qr(e)?t:Hr(e)?{...t,width:Ar(e)}:{...t,width:zr})(e,o)})(e),a={styles:l,attributes:Gr(e),colGroups:Kr(e)};return e.undoManager.ignore((()=>{const r=((e,t,o,n,r,s=Ac)=>{const l=xe.fromTag("table"),a="cells"!==r;Nt(l,s.styles),he(l,s.attributes),s.colGroups&&Ie(l,(e=>{const t=xe.fromTag("colgroup");return k(e,(()=>Ie(t,xe.fromTag("col")))),t})(t));const c=Math.min(e,o);if(a&&o>0){const e=xe.fromTag("thead");Ie(l,e);const s=Lc(o,t,"sectionCells"===r?c:0,n);$e(e,s)}const i=xe.fromTag("tbody");Ie(l,i);const m=Lc(a?e-c:e,t,a?0:o,n);return $e(i,m),l})(o,t,s,n,Wr(e),a);ge(r,"data-mce-id","__mce");const l=(e=>{const t=xe.fromTag("div"),o=xe.fromDom(e.dom.cloneNode(!0));return Ie(t,o),(e=>e.dom.innerHTML)(t)})(r);e.insertContent(l),e.addVisual()})),pt(Xr(e),'table[data-mce-id="__mce"]').map((t=>(Hr(e)?_c(t):$r(e)?zc(t):(Fr(e)||(e=>r(e)&&-1!==e.indexOf("%"))(l.width))&&Bc(t),es(t),we(t,"data-mce-id"),((e,t)=>{N(mt(t,"tr"),(t=>{cc(e,t.dom),N(mt(t,"th,td"),(t=>{ic(e,t.dom)}))}))})(e,t),((e,t)=>{pt(t,"td,th").each(b(Wc,e))})(e,t),t.dom))).getOrNull()};var jc=tinymce.util.Tools.resolve("tinymce.FakeClipboard");const Pc="x-tinymce/dom-table-",Ic=Pc+"rows",Fc=Pc+"columns",Hc=e=>{const t=jc.FakeClipboardItem(e);jc.write([t])},$c=e=>{var t;const o=null!==(t=jc.read())&&void 0!==t?t:[];return V(o,(t=>C.from(t.getType(e))))},Vc=e=>{$c(e).isSome()&&jc.clear()},qc=e=>{e.fold(Gc,(e=>Hc({[Ic]:e})))},Uc=()=>$c(Ic),Gc=()=>Vc(Ic),Kc=e=>{e.fold(Jc,(e=>Hc({[Fc]:e})))},Yc=()=>$c(Fc),Jc=()=>Vc(Fc),Qc=e=>Ps(ts(e),Zr(e)).filter(as),Xc=(e,t)=>{const o=Zr(e),n=e=>Ut(e,o),l=t=>(e=>Is(ts(e),Zr(e)).filter(as))(e).bind((e=>n(e).map((o=>t(o,e))))),a=t=>{e.focus()},c=(t,o=!1)=>l(((n,r)=>{const s=Ms(Fs(e),n,r);t(n,s,o).each(a)})),i=()=>l(((t,o)=>((e,t,o)=>{const n=Qo(e);return El(n,t).bind((e=>{const t=Tl(n,o,!1),r=$o(t).rows.slice(e[0].row,e[e.length-1].row+e[e.length-1].rowspan),s=j(r,(e=>{const t=_(e.cells,(e=>!e.isLocked));return t.length>0?[{...e,cells:t}]:[]})),l=Sl(s);return yt(l.length>0,l)})).map((e=>E(e,(e=>{const t=Ke(e.element);return N(e.cells,(e=>{const o=Ye(e.element);wl(o,"colspan",e.colspan,1),wl(o,"rowspan",e.rowspan,1),Ie(t,o)})),t}))))})(t,Ms(Fs(e),t,o),Nr(f,xe.fromDom(e.getDoc()),C.none())))),m=()=>l(((t,o)=>((e,t)=>{const o=Qo(e);return Nl(o,t).map((e=>{const t=e[e.length-1],n=e[0].column,r=t.column+t.colspan,s=((e,t,o)=>{if(rn(e)){const n=_(nn(e),bc(t,o)),r=E(n,(e=>{const n=Ye(e.element);return pc(n,"span",o-t),n})),s=xe.fromTag("colgroup");return $e(s,r),[s]}return[]})(o,n,r),l=((e,t,o)=>E(e.all,(e=>{const n=_(e.cells,bc(t,o)),r=E(n,(e=>{const n=Ye(e.element);return pc(n,"colspan",o-t),n})),s=xe.fromTag("tr");return $e(s,r),s})))(o,n,r);return[...s,...l]}))})(t,Ms(Fs(e),t,o)))),d=(t,o)=>o().each((o=>{const n=E(o,(e=>Ye(e)));l(((o,r)=>{const s=Br(xe.fromDom(e.getDoc())),l=((e,t,o,n)=>({selection:Es(e),clipboard:o,generators:n}))(Fs(e),0,n,s);t(o,l).each(a)}))})),g=e=>(t,o)=>((e,t)=>X(e,t)?C.from(e[t]):C.none())(o,"type").each((t=>{c(e(t),o.no_events)}));G({mceTableSplitCells:()=>c(t.unmergeCells),mceTableMergeCells:()=>c(t.mergeCells),mceTableInsertRowBefore:()=>c(t.insertRowsBefore),mceTableInsertRowAfter:()=>c(t.insertRowsAfter),mceTableInsertColBefore:()=>c(t.insertColumnsBefore),mceTableInsertColAfter:()=>c(t.insertColumnsAfter),mceTableDeleteCol:()=>c(t.deleteColumn),mceTableDeleteRow:()=>c(t.deleteRow),mceTableCutCol:()=>m().each((e=>{Kc(e),c(t.deleteColumn)})),mceTableCutRow:()=>i().each((e=>{qc(e),c(t.deleteRow)})),mceTableCopyCol:()=>m().each((e=>Kc(e))),mceTableCopyRow:()=>i().each((e=>qc(e))),mceTablePasteColBefore:()=>d(t.pasteColsBefore,Yc),mceTablePasteColAfter:()=>d(t.pasteColsAfter,Yc),mceTablePasteRowBefore:()=>d(t.pasteRowsBefore,Uc),mceTablePasteRowAfter:()=>d(t.pasteRowsAfter,Uc),mceTableDelete:()=>Qc(e).each((t=>{Ut(t,o).filter(w(o)).each((t=>{const o=xe.fromText("");if(je(t,o),qe(t),e.dom.isEmpty(e.getBody()))e.setContent(""),e.selection.setCursorLocation();else{const t=e.dom.createRng();t.setStart(o.dom,0),t.setEnd(o.dom,0),e.selection.setRng(t),e.nodeChanged()}}))})),mceTableCellToggleClass:(t,o)=>{l((t=>{const n=Fs(e),r=P(n,(t=>e.formatter.match("tablecellclass",{value:o},t.dom))),s=r?e.formatter.remove:e.formatter.apply;N(n,(e=>s("tablecellclass",{value:o},e.dom))),mc(e,t.dom,dc)}))},mceTableToggleClass:(t,o)=>{l((t=>{e.formatter.toggle("tableclass",{value:o},t.dom),mc(e,t.dom,dc)}))},mceTableToggleCaption:()=>{Qc(e).each((t=>{Ut(t,o).each((o=>{ht(o,"caption").fold((()=>{const t=xe.fromTag("caption");Ie(t,xe.fromText("Caption")),((e,t,o)=>{We(e,0).fold((()=>{Ie(e,t)}),(e=>{Me(e,t)}))})(o,t),e.selection.setCursorLocation(t.dom,0)}),(n=>{ue("caption")(t)&&Se("td",o).each((t=>e.selection.setCursorLocation(t.dom,0))),qe(n)})),mc(e,o.dom,uc)}))}))},mceTableSizingMode:(t,n)=>(t=>Qc(e).each((n=>{$r(e)||Hr(e)||Fr(e)||Ut(n,o).each((o=>{"relative"!==t||Oc(o)?"fixed"!==t||kc(o)?"responsive"!==t||Ec(o)||zc(o):_c(o):Bc(o),es(o),mc(e,o.dom,uc)}))})))(n),mceTableCellType:g((e=>"th"===e?t.makeCellsHeader:t.unmakeCellsHeader)),mceTableColType:g((e=>"th"===e?t.makeColumnsHeader:t.unmakeColumnsHeader)),mceTableRowType:g((e=>{switch(e){case"header":return t.makeRowsHeader;case"footer":return t.makeRowsFooter;default:return t.makeRowsBody}}))},((t,o)=>e.addCommand(o,t))),e.addCommand("mceInsertTable",((t,o)=>{((e,t,o,n={})=>{const r=e=>u(e)&&e>0;if(r(t)&&r(o)){const r=n.headerRows||0,s=n.headerColumns||0;return Mc(e,o,t,s,r)}console.error("Invalid values for mceInsertTable - rows and columns values are required to insert a table.")})(e,o.rows,o.columns,o.options)})),e.addCommand("mceTableApplyCellStyle",((t,o)=>{const l=e=>"tablecell"+e.toLowerCase().replace("-","");if(!s(o))return;const a=_(Fs(e),as);if(0===a.length)return;const c=((e,t)=>{const o={};return((e,t,o,n)=>{G(e,((e,r)=>{(t(e,r)?o:n)(e,r)}))})(e,t,(e=>(t,o)=>{e[o]=t})(o),f),o})(o,((t,o)=>e.formatter.has(l(o))&&r(t)));(e=>{for(const t in e)if(U.call(e,t))return!1;return!0})(c)||(G(c,((t,o)=>{const n=l(o);N(a,(o=>{""===t?e.formatter.remove(n,{value:null},o.dom,!0):e.formatter.apply(n,{value:t},o.dom)}))})),n(a[0]).each((t=>mc(e,t.dom,dc))))}))},Zc=ta([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),ei={before:Zc.before,on:Zc.on,after:Zc.after,cata:(e,t,o,n)=>e.fold(t,o,n),getStart:e=>e.fold(h,h,h)},ti=(e,t)=>({selection:e,kill:t}),oi=(e,t)=>{const o=e.document.createRange();return o.selectNode(t.dom),o},ni=(e,t)=>{const o=e.document.createRange();return ri(o,t),o},ri=(e,t)=>e.selectNodeContents(t.dom),si=(e,t,o)=>{const n=e.document.createRange();var r;return r=n,t.fold((e=>{r.setStartBefore(e.dom)}),((e,t)=>{r.setStart(e.dom,t)}),(e=>{r.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},li=(e,t,o,n,r)=>{const s=e.document.createRange();return s.setStart(t.dom,o),s.setEnd(n.dom,r),s},ai=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),ci=ta([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),ii=(e,t,o)=>t(xe.fromDom(o.startContainer),o.startOffset,xe.fromDom(o.endContainer),o.endOffset),mi=(e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:g(e),rtl:C.none}),relative:(t,o)=>({ltr:Xt((()=>si(e,t,o))),rtl:Xt((()=>C.some(si(e,o,t))))}),exact:(t,o,n,r)=>({ltr:Xt((()=>li(e,t,o,n,r))),rtl:Xt((()=>C.some(li(e,n,r,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>ci.rtl(xe.fromDom(e.endContainer),e.endOffset,xe.fromDom(e.startContainer),e.startOffset))).getOrThunk((()=>ii(0,ci.ltr,o))):ii(0,ci.ltr,o)})(0,o)},di=(e,t)=>mi(e,t).match({ltr:(t,o,n,r)=>{const s=e.document.createRange();return s.setStart(t.dom,o),s.setEnd(n.dom,r),s},rtl:(t,o,n,r)=>{const s=e.document.createRange();return s.setStart(n.dom,r),s.setEnd(t.dom,o),s}});ci.ltr,ci.rtl;const ui=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),fi=(e,t,o,n)=>({start:ei.on(e,t),finish:ei.on(o,n)}),gi=(e,t)=>{const o=di(e,t);return ui(xe.fromDom(o.startContainer),o.startOffset,xe.fromDom(o.endContainer),o.endOffset)},hi=fi,pi=(e,t,o,n,r)=>Re(o,n)?C.none():Ts(o,n,t).bind((t=>{const n=t.boxes.getOr([]);return n.length>1?(r(e,n,t.start,t.finish),C.some(ti(C.some(hi(o,0,o,br(o))),!0))):C.none()})),bi=(e,t)=>({item:e,mode:t}),wi=(e,t,o,n=vi)=>e.property().parent(t).map((e=>bi(e,n))),vi=(e,t,o,n=yi)=>o.sibling(e,t).map((e=>bi(e,n))),yi=(e,t,o,n=yi)=>{const r=e.property().children(t);return o.first(r).map((e=>bi(e,n)))},xi=[{current:wi,next:vi,fallback:C.none()},{current:vi,next:yi,fallback:C.some(wi)},{current:yi,next:yi,fallback:C.some(vi)}],Ci=(e,t,o,n,r=xi)=>L(r,(e=>e.current===o)).bind((o=>o.current(e,t,n,o.next).orThunk((()=>o.fallback.bind((o=>Ci(e,t,o,n))))))),Ti=(e,t,o,n,r,s)=>Ci(e,t,n,r).bind((t=>s(t.item)?C.none():o(t.item)?C.some(t.item):Ti(e,t.item,o,t.mode,r,s))),Si=e=>t=>0===e.property().children(t).length,Ri=(e,t,o,n)=>Ti(e,t,o,vi,{sibling:(e,t)=>e.query().prevSibling(t),first:e=>e.length>0?C.some(e[e.length-1]):C.none()},n),Di=(e,t,o,n)=>Ti(e,t,o,vi,{sibling:(e,t)=>e.query().nextSibling(t),first:e=>e.length>0?C.some(e[0]):C.none()},n),Oi=bs(),ki=(e,t)=>((e,t,o)=>Ri(e,t,Si(e),o))(Oi,e,t),Ei=(e,t)=>((e,t,o)=>Di(e,t,Si(e),o))(Oi,e,t),Ni=ta([{none:["message"]},{success:[]},{failedUp:["cell"]},{failedDown:["cell"]}]),Bi=e=>bt(e,"tr"),_i={...Ni,verify:(e,t,o,n,r,s,l)=>bt(n,"td,th",l).bind((o=>bt(t,"td,th",l).map((t=>Re(o,t)?Re(n,o)&&br(o)===r?s(t):Ni.none("in same cell"):xs(Bi,[o,t]).fold((()=>((e,t,o)=>{const n=e.getRect(t),r=e.getRect(o);return r.right>n.left&&r.left<n.right})(e,t,o)?Ni.success():s(t)),(e=>s(t))))))).getOr(Ni.none("default")),cata:(e,t,o,n,r)=>e.fold(t,o,n,r)},zi=ue("br"),Ai=(e,t,o)=>t(e,o).bind((e=>ie(e)&&0===gr(e).trim().length?Ai(e,t,o):C.some(e))),Li=(e,t,o,n)=>((e,t)=>We(e,t).filter(zi).orThunk((()=>We(e,t-1).filter(zi))))(t,o).bind((t=>n.traverse(t).fold((()=>Ai(t,n.gather,e).map(n.relative)),(e=>(e=>Ne(e).bind((t=>{const o=Le(t);return((e,t)=>W(e,b(Re,t)))(o,e).map((n=>((e,t,o,n)=>({parent:e,children:t,element:o,index:n}))(t,o,e,n)))})))(e).map((e=>ei.on(e.parent,e.index))))))),Wi=(e,t)=>({left:e.left,top:e.top+t,right:e.right,bottom:e.bottom+t}),Mi=(e,t)=>({left:e.left,top:e.top-t,right:e.right,bottom:e.bottom-t}),ji=(e,t,o)=>({left:e.left+t,top:e.top+o,right:e.right+t,bottom:e.bottom+o}),Pi=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom}),Ii=(e,t)=>C.some(e.getRect(t)),Fi=(e,t,o)=>ce(t)?Ii(e,t).map(Pi):ie(t)?((e,t,o)=>o>=0&&o<br(t)?e.getRangedRect(t,o,t,o+1):o>0?e.getRangedRect(t,o-1,t,o):C.none())(e,t,o).map(Pi):C.none(),Hi=(e,t)=>ce(t)?Ii(e,t).map(Pi):ie(t)?e.getRangedRect(t,0,t,br(t)).map(Pi):C.none(),$i=ta([{none:[]},{retry:["caret"]}]),Vi=(e,t,o)=>ft(t,ba).fold(y,(t=>Hi(e,t).exists((e=>((e,t)=>e.left<t.left||Math.abs(t.right-e.left)<1||e.left>t.right)(o,e))))),qi={point:e=>e.bottom,adjuster:(e,t,o,n,r)=>{const s=Wi(r,5);return Math.abs(o.bottom-n.bottom)<1||o.top>r.bottom?$i.retry(s):o.top===r.bottom?$i.retry(Wi(r,1)):Vi(e,t,r)?$i.retry(ji(s,5,0)):$i.none()},move:Wi,gather:Ei},Ui=(e,t,o,n,r)=>0===r?C.some(n):((e,t,o)=>e.elementFromPoint(t,o).filter((e=>"table"===ne(e))).isSome())(e,n.left,t.point(n))?((e,t,o,n,r)=>Ui(e,t,o,t.move(n,5),r))(e,t,o,n,r-1):e.situsFromPoint(n.left,t.point(n)).bind((s=>s.start.fold(C.none,(s=>Hi(e,s).bind((l=>t.adjuster(e,s,l,o,n).fold(C.none,(n=>Ui(e,t,o,n,r-1))))).orThunk((()=>C.some(n)))),C.none))),Gi=(e,t,o)=>{const n=e.move(o,5),r=Ui(t,e,o,n,100).getOr(n);return((e,t,o)=>e.point(t)>o.getInnerHeight()?C.some(e.point(t)-o.getInnerHeight()):e.point(t)<0?C.some(-e.point(t)):C.none())(e,r,t).fold((()=>t.situsFromPoint(r.left,e.point(r))),(o=>(t.scrollBy(0,o),t.situsFromPoint(r.left,e.point(r)-o))))},Ki={tryUp:b(Gi,{point:e=>e.top,adjuster:(e,t,o,n,r)=>{const s=Mi(r,5);return Math.abs(o.top-n.top)<1||o.bottom<r.top?$i.retry(s):o.bottom===r.top?$i.retry(Mi(r,1)):Vi(e,t,r)?$i.retry(ji(s,5,0)):$i.none()},move:Mi,gather:ki}),tryDown:b(Gi,qi),getJumpSize:g(5)},Yi=(e,t,o)=>e.getSelection().bind((n=>((e,t,o,n)=>{const r=zi(t)?((e,t,o)=>o.traverse(t).orThunk((()=>Ai(t,o.gather,e))).map(o.relative))(e,t,n):Li(e,t,o,n);return r.map((e=>({start:e,finish:e})))})(t,n.finish,n.foffset,o).fold((()=>C.some($s(n.finish,n.foffset))),(r=>{const s=e.fromSitus(r);return l=_i.verify(e,n.finish,n.foffset,s.finish,s.foffset,o.failure,t),_i.cata(l,(e=>C.none()),(()=>C.none()),(e=>C.some($s(e,0))),(e=>C.some($s(e,br(e)))));var l})))),Ji=(e,t,o,n,r,s)=>0===s?C.none():Zi(e,t,o,n,r).bind((l=>{const a=e.fromSitus(l),c=_i.verify(e,o,n,a.finish,a.foffset,r.failure,t);return _i.cata(c,(()=>C.none()),(()=>C.some(l)),(l=>Re(o,l)&&0===n?Qi(e,o,n,Mi,r):Ji(e,t,l,0,r,s-1)),(l=>Re(o,l)&&n===br(l)?Qi(e,o,n,Wi,r):Ji(e,t,l,br(l),r,s-1)))})),Qi=(e,t,o,n,r)=>Fi(e,t,o).bind((t=>Xi(e,r,n(t,Ki.getJumpSize())))),Xi=(e,t,o)=>{const n=Eo().browser;return n.isChromium()||n.isSafari()||n.isFirefox()?t.retry(e,o):C.none()},Zi=(e,t,o,n,r)=>Fi(e,o,n).bind((t=>Xi(e,r,t))),em=(e,t,o,n,r)=>bt(n,"td,th",t).bind((n=>bt(n,"table",t).bind((s=>((e,t)=>ut(e,(e=>Ne(e).exists((e=>Re(e,t)))),void 0).isSome())(r,s)?((e,t,o)=>Yi(e,t,o).bind((n=>Ji(e,t,n.element,n.offset,o,20).map(e.fromSitus))))(e,t,o).bind((e=>bt(e.finish,"td,th",t).map((t=>({start:n,finish:t,range:e}))))):C.none())))),tm=(e,t,o,n,r,s)=>s(n,t).orThunk((()=>em(e,t,o,n,r).map((e=>{const t=e.range;return ti(C.some(hi(t.start,t.soffset,t.finish,t.foffset)),!0)})))),om=(e,t)=>bt(e,"tr",t).bind((e=>bt(e,"table",t).bind((o=>{const n=mt(o,"tr");return Re(e,n[0])?((e,t,o)=>Ri(Oi,e,(e=>xr(e).isSome()),o))(o,0,t).map((e=>{const t=br(e);return ti(C.some(hi(e,t,e,t)),!0)})):C.none()})))),nm=(e,t)=>bt(e,"tr",t).bind((e=>bt(e,"table",t).bind((o=>{const n=mt(o,"tr");return Re(e,n[n.length-1])?((e,t,o)=>Di(Oi,e,(e=>yr(e).isSome()),o))(o,0,t).map((e=>ti(C.some(hi(e,0,e,0)),!0))):C.none()})))),rm=(e,t,o,n,r,s,l)=>em(e,o,n,r,s).bind((e=>pi(t,o,e.start,e.finish,l))),sm=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},lm=()=>{const e=(e=>{const t=sm(C.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(C.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(C.some(e))}}})(f);return{...e,on:t=>e.get().each(t)}},am=(e,t)=>bt(e,"td,th",t),cm=e=>Be(e).exists(Jr),im={traverse:Ae,gather:Ei,relative:ei.before,retry:Ki.tryDown,failure:_i.failedDown},mm={traverse:ze,gather:ki,relative:ei.before,retry:Ki.tryUp,failure:_i.failedUp},dm=e=>t=>t===e,um=dm(38),fm=dm(40),gm=e=>e>=37&&e<=40,hm={isBackward:dm(37),isForward:dm(39)},pm={isBackward:dm(39),isForward:dm(37)},bm=ta([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),wm={domRange:bm.domRange,relative:bm.relative,exact:bm.exact,exactFromRange:e=>bm.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>xe.fromDom(e.startContainer),relative:(e,t)=>ei.getStart(e),exact:(e,t,o,n)=>e}))(e);return xe.fromDom(Ee(t).dom.defaultView)},range:ui},vm=(e,t)=>{const o=ne(e);return"input"===o?ei.after(e):D(["br","img"],o)?0===t?ei.before(e):ei.after(e):ei.on(e,t)},ym=e=>C.from(e.getSelection()),xm=(e,t)=>{ym(e).each((e=>{e.removeAllRanges(),e.addRange(t)}))},Cm=(e,t,o,n,r)=>{const s=li(e,t,o,n,r);xm(e,s)},Tm=(e,t)=>mi(e,t).match({ltr:(t,o,n,r)=>{Cm(e,t,o,n,r)},rtl:(t,o,n,r)=>{ym(e).each((s=>{if(s.setBaseAndExtent)s.setBaseAndExtent(t.dom,o,n.dom,r);else if(s.extend)try{((e,t,o,n,r,s)=>{t.collapse(o.dom,n),t.extend(r.dom,s)})(0,s,t,o,n,r)}catch(s){Cm(e,n,r,t,o)}else Cm(e,n,r,t,o)}))}}),Sm=(e,t,o,n,r)=>{const s=((e,t,o,n)=>{const r=vm(e,t),s=vm(o,n);return wm.relative(r,s)})(t,o,n,r);Tm(e,s)},Rm=(e,t,o)=>{const n=((e,t)=>{const o=e.fold(ei.before,vm,ei.after),n=t.fold(ei.before,vm,ei.after);return wm.relative(o,n)})(t,o);Tm(e,n)},Dm=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return C.some(ui(xe.fromDom(t.startContainer),t.startOffset,xe.fromDom(o.endContainer),o.endOffset))}return C.none()},Om=e=>{if(null===e.anchorNode||null===e.focusNode)return Dm(e);{const t=xe.fromDom(e.anchorNode),o=xe.fromDom(e.focusNode);return((e,t,o,n)=>{const r=((e,t,o,n)=>{const r=ke(e).dom.createRange();return r.setStart(e.dom,t),r.setEnd(o.dom,n),r})(e,t,o,n),s=Re(e,o)&&t===n;return r.collapsed&&!s})(t,e.anchorOffset,o,e.focusOffset)?C.some(ui(t,e.anchorOffset,o,e.focusOffset)):Dm(e)}},km=(e,t,o=!0)=>{const n=(o?ni:oi)(e,t);xm(e,n)},Em=e=>(e=>ym(e).filter((e=>e.rangeCount>0)).bind(Om))(e).map((e=>wm.exact(e.start,e.soffset,e.finish,e.foffset))),Nm=(e,t,o)=>((e,t,o)=>((e,t,o)=>e.caretPositionFromPoint?((e,t,o)=>{var n;return C.from(null===(n=e.caretPositionFromPoint)||void 0===n?void 0:n.call(e,t,o)).bind((t=>{if(null===t.offsetNode)return C.none();const o=e.createRange();return o.setStart(t.offsetNode,t.offset),o.collapse(),C.some(o)}))})(e,t,o):e.caretRangeFromPoint?((e,t,o)=>{var n;return C.from(null===(n=e.caretRangeFromPoint)||void 0===n?void 0:n.call(e,t,o))})(e,t,o):C.none())(e.document,t,o).map((e=>ui(xe.fromDom(e.startContainer),e.startOffset,xe.fromDom(e.endContainer),e.endOffset))))(e,t,o),Bm=e=>({elementFromPoint:(t,o)=>xe.fromPoint(xe.fromDom(e.document),t,o),getRect:e=>e.dom.getBoundingClientRect(),getRangedRect:(t,o,n,r)=>{const s=wm.exact(t,o,n,r);return((e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?C.some(o).map(ai):C.none()})(di(e,t)))(e,s)},getSelection:()=>Em(e).map((t=>gi(e,t))),fromSitus:t=>{const o=wm.relative(t.start,t.finish);return gi(e,o)},situsFromPoint:(t,o)=>Nm(e,t,o).map((e=>fi(e.start,e.soffset,e.finish,e.foffset))),clearSelection:()=>{(e=>{ym(e).each((e=>e.removeAllRanges()))})(e)},collapseSelection:(t=!1)=>{Em(e).each((o=>o.fold((e=>e.collapse(t)),((o,n)=>{const r=t?o:n;Rm(e,r,r)}),((o,n,r,s)=>{const l=t?o:r,a=t?n:s;Sm(e,l,a,l,a)}))))},setSelection:t=>{Sm(e,t.start,t.soffset,t.finish,t.foffset)},setRelativeSelection:(t,o)=>{Rm(e,t,o)},selectNode:t=>{km(e,t,!1)},selectContents:t=>{km(e,t)},getInnerHeight:()=>e.innerHeight,getScrollY:()=>(e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return pn(o,n)})(xe.fromDom(e.document)).top,scrollBy:(t,o)=>{((e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollBy(e,t)})(t,o,xe.fromDom(e.document))}}),_m=(e,t)=>({rows:e,cols:t}),zm=e=>ft(e,ae).exists(Jr),Am=(e,t)=>zm(e)||zm(t),Lm=e=>void 0!==e.dom.classList,Wm=(e,t)=>((e,t,o)=>{const n=((e,t)=>{const o=pe(e,t);return void 0===o||""===o?[]:o.split(" ")})(e,t).concat([o]);return ge(e,t,n.join(" ")),!0})(e,"class",t),Mm=(e,t)=>{Lm(e)?e.dom.classList.add(t):Wm(e,t)},jm=(e,t)=>Lm(e)&&e.dom.classList.contains(t),Pm=()=>({tag:"none"}),Im=e=>({tag:"multiple",elements:e}),Fm=e=>({tag:"single",element:e}),Hm=e=>{const t=xe.fromDom((e=>{if(m(e.target)){const t=xe.fromDom(e.target);if(ce(t)&&m(t.dom.shadowRoot)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return H(t)}}return C.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),r=(s=n,l=o,(...e)=>s(l.apply(null,e)));var s,l;return((e,t,o,n,r,s,l)=>({target:e,x:t,y:o,stop:n,prevent:r,kill:s,raw:l}))(t,e.clientX,e.clientY,o,n,r,e)},$m=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},Vm=x,qm=(e,t,o)=>((e,t,o,n)=>((e,t,o,n,r)=>{const s=((e,t)=>o=>{e(o)&&t(Hm(o))})(o,n);return e.dom.addEventListener(t,s,r),{unbind:b($m,e,t,s,r)}})(e,t,o,n,!1))(e,t,Vm,o),Um=Hm,Gm=e=>!jm(xe.fromDom(e.target),"ephox-snooker-resizer-bar"),Km=(e,t)=>{const o=(r=Ws.selectedSelector,{get:()=>Os(xe.fromDom(e.getBody()),r).fold((()=>Is(ts(e),Zr(e)).fold(Pm,Fm)),Im)}),n=((e,t,o)=>{const n=t=>{we(t,e.selected),we(t,e.firstSelected),we(t,e.lastSelected)},r=t=>{ge(t,e.selected,"1")},s=e=>{l(e),o()},l=t=>{const o=mt(t,`${e.selectedSelector},${e.firstSelectedSelector},${e.lastSelectedSelector}`);N(o,n)};return{clearBeforeUpdate:l,clear:s,selectRange:(o,n,l,a)=>{s(o),N(n,r),ge(l,e.firstSelected,"1"),ge(a,e.lastSelected,"1"),t(n,l,a)},selectedSelector:e.selectedSelector,firstSelectedSelector:e.firstSelectedSelector,lastSelectedSelector:e.lastSelectedSelector}})(Ws,((t,o,n)=>{Ut(o).each((r=>{const s=E(t,(e=>e.dom)),l=Lr(e),a=Nr(f,xe.fromDom(e.getDoc()),l),c=((e,t,o)=>{const n=Qo(e);return El(n,t).map((e=>{const t=Tl(n,o,!1),{rows:r}=$o(t),s=((e,t)=>{const o=e.slice(0,t[t.length-1].row+1),n=Sl(o);return j(n,(e=>{const o=e.cells.slice(0,t[t.length-1].column+1);return E(o,(e=>e.element))}))})(r,e),l=((e,t)=>{const o=e.slice(t[0].row+t[0].rowspan-1,e.length),n=Sl(o);return j(n,(e=>{const o=e.cells.slice(t[0].column+t[0].colspan-1,e.cells.length);return E(o,(e=>e.element))}))})(r,e);return{upOrLeftCells:s,downOrRightCells:l}}))})(r,{selection:Fs(e)},a).map((e=>K(e,(e=>E(e,(e=>e.dom)))))).getOrUndefined();((e,t,o,n,r)=>{e.dispatch("TableSelectionChange",{cells:t,start:o,finish:n,otherCells:r})})(e,s,o.dom,n.dom,c)}))}),(()=>(e=>{e.dispatch("TableSelectionClear")})(e)));var r;return e.on("init",(o=>{const r=e.getWin(),s=Xr(e),l=Zr(e),a=((e,t,o,n)=>{const r=((e,t,o,n)=>{const r=lm(),s=r.clear,l=s=>{r.on((r=>{n.clearBeforeUpdate(t),am(s.target,o).each((l=>{Ts(r,l,o).each((o=>{const r=o.boxes.getOr([]);if(1===r.length){const e=r[0],o="false"===Qr(e),l=wt(Yr(s.target),e,Re);o&&l&&n.selectRange(t,r,e,e)}else r.length>1&&(n.selectRange(t,r,o.start,o.finish),e.selectContents(l))}))}))}))};return{clearstate:s,mousedown:e=>{n.clear(t),am(e.target,o).filter(cm).each(r.set)},mouseover:e=>{l(e)},mouseup:e=>{l(e),s()}}})(Bm(e),t,o,n);return{clearstate:r.clearstate,mousedown:r.mousedown,mouseover:r.mouseover,mouseup:r.mouseup}})(r,s,l,n),c=((e,t,o,n)=>{const r=Bm(e),s=()=>(n.clear(t),C.none());return{keydown:(e,l,a,c,i,m)=>{const d=e.raw,u=d.which,f=!0===d.shiftKey,g=Ss(t,n.selectedSelector).fold((()=>(gm(u)&&!f&&n.clearBeforeUpdate(t),gm(u)&&f&&!Am(l,c)?C.none:fm(u)&&f?b(rm,r,t,o,im,c,l,n.selectRange):um(u)&&f?b(rm,r,t,o,mm,c,l,n.selectRange):fm(u)?b(tm,r,o,im,c,l,nm):um(u)?b(tm,r,o,mm,c,l,om):C.none)),(e=>{const o=o=>()=>{const s=V(o,(o=>((e,t,o,n,r)=>Ds(n,e,t,r.firstSelectedSelector,r.lastSelectedSelector).map((e=>(r.clearBeforeUpdate(o),r.selectRange(o,e.boxes,e.start,e.finish),e.boxes))))(o.rows,o.cols,t,e,n)));return s.fold((()=>Rs(t,n.firstSelectedSelector,n.lastSelectedSelector).map((e=>{const o=fm(u)||m.isForward(u)?ei.after:ei.before;return r.setRelativeSelection(ei.on(e.first,0),o(e.table)),n.clear(t),ti(C.none(),!0)}))),(e=>C.some(ti(C.none(),!0))))};return gm(u)&&f&&!Am(l,c)?C.none:fm(u)&&f?o([_m(1,0)]):um(u)&&f?o([_m(-1,0)]):m.isBackward(u)&&f?o([_m(0,-1),_m(-1,0)]):m.isForward(u)&&f?o([_m(0,1),_m(1,0)]):gm(u)&&!f?s:C.none}));return g()},keyup:(e,r,s,l,a)=>Ss(t,n.selectedSelector).fold((()=>{const c=e.raw,i=c.which;return!0===c.shiftKey&&gm(i)&&Am(r,l)?((e,t,o,n,r,s,l)=>Re(o,r)&&n===s?C.none():bt(o,"td,th",t).bind((o=>bt(r,"td,th",t).bind((n=>pi(e,t,o,n,l))))))(t,o,r,s,l,a,n.selectRange):C.none()}),C.none)}})(r,s,l,n),i=((e,t,o,n)=>{const r=Bm(e);return(e,s)=>{n.clearBeforeUpdate(t),Ts(e,s,o).each((e=>{const o=e.boxes.getOr([]);n.selectRange(t,o,e.start,e.finish),r.selectContents(s),r.collapseSelection()}))}})(r,s,l,n);e.on("TableSelectorChange",(e=>i(e.start,e.finish)));const m=(t,o)=>{(e=>!0===e.raw.shiftKey)(t)&&(o.kill&&t.kill(),o.selection.each((t=>{const o=wm.relative(t.start,t.finish),n=di(r,o);e.selection.setRng(n)})))},d=e=>0===e.button,u=(()=>{const e=sm(xe.fromDom(s)),t=sm(0);return{touchEnd:o=>{const n=xe.fromDom(o.target);if(ue("td")(n)||ue("th")(n)){const r=e.get(),s=t.get();Re(r,n)&&o.timeStamp-s<300&&(o.preventDefault(),i(n,n))}e.set(n),t.set(o.timeStamp)}}})();e.on("dragstart",(e=>{a.clearstate()})),e.on("mousedown",(e=>{d(e)&&Gm(e)&&a.mousedown(Um(e))})),e.on("mouseover",(e=>{var t;void 0!==(t=e).buttons&&0==(1&t.buttons)||!Gm(e)||a.mouseover(Um(e))})),e.on("mouseup",(e=>{d(e)&&Gm(e)&&a.mouseup(Um(e))})),e.on("touchend",u.touchEnd),e.on("keyup",(t=>{const o=Um(t);if(o.raw.shiftKey&&gm(o.raw.which)){const t=e.selection.getRng(),n=xe.fromDom(t.startContainer),r=xe.fromDom(t.endContainer);c.keyup(o,n,t.startOffset,r,t.endOffset).each((e=>{m(o,e)}))}})),e.on("keydown",(o=>{const n=Um(o);t.hide();const r=e.selection.getRng(),s=xe.fromDom(r.startContainer),l=xe.fromDom(r.endContainer),a=mn(hm,pm)(xe.fromDom(e.selection.getStart()));c.keydown(n,s,r.startOffset,l,r.endOffset,a).each((e=>{m(n,e)})),t.show()})),e.on("NodeChange",(()=>{const t=e.selection,o=xe.fromDom(t.getStart()),r=xe.fromDom(t.getEnd());xs(Ut,[o,r]).fold((()=>n.clear(s)),f)}))})),e.on("PreInit",(()=>{e.serializer.addTempAttr(Ws.firstSelected),e.serializer.addTempAttr(Ws.lastSelected)})),{getSelectedCells:()=>((e,t,o,n)=>{switch(e.tag){case"none":return t();case"single":return(e=>[e.dom])(e.element);case"multiple":return(e=>E(e,(e=>e.dom)))(e.elements)}})(o.get(),g([])),clearSelectedCells:e=>n.clear(xe.fromDom(e))}},Ym=e=>{let t=[];return{bind:e=>{if(void 0===e)throw new Error("Event bind error: undefined handler");t.push(e)},unbind:e=>{t=_(t,(t=>t!==e))},trigger:(...o)=>{const n={};N(e,((e,t)=>{n[e]=o[t]})),N(t,(e=>{e(n)}))}}},Jm=e=>({registry:K(e,(e=>({bind:e.bind,unbind:e.unbind}))),trigger:K(e,(e=>e.trigger))}),Qm=e=>e.slice(0).sort(),Xm=(e,t)=>{const o=_(t,(t=>!D(e,t)));o.length>0&&(e=>{throw new Error("Unsupported keys for object: "+Qm(e).join(", "))})(o)},Zm=e=>((e,t)=>((e,t,o)=>{if(0===t.length)throw new Error("You must specify at least one required field.");return((e,t)=>{if(!l(t))throw new Error("The "+e+" fields must be an array. Was: "+t+".");N(t,(t=>{if(!r(t))throw new Error("The value "+t+" in the "+e+" fields was not a string.")}))})("required",t),(e=>{const t=Qm(e);L(t,((e,o)=>o<t.length-1&&e===t[o+1])).each((e=>{throw new Error("The field: "+e+" occurs more than once in the combined fields: ["+t.join(", ")+"].")}))})(t),n=>{const r=q(n);P(t,(e=>D(r,e)))||((e,t)=>{throw new Error("All required keys ("+Qm(e).join(", ")+") were not specified. Specified keys were: "+Qm(t).join(", ")+".")})(t,r),e(t,r);const s=_(t,(e=>!o.validate(n[e],e)));return s.length>0&&((e,t)=>{throw new Error("All values need to be of type: "+t+". Keys ("+Qm(e).join(", ")+") were not.")})(s,o.label),n}})(e,t,{validate:d,label:"function"}))(Xm,e),ed=Zm(["compare","extract","mutate","sink"]),td=Zm(["element","start","stop","destroy"]),od=Zm(["forceDrop","drop","move","delayDrop"]),nd=()=>{const e=(()=>{const e=Jm({move:Ym(["info"])});return{onEvent:f,reset:f,events:e.registry}})(),t=(()=>{let e=C.none();const t=Jm({move:Ym(["info"])});return{onEvent:(o,n)=>{n.extract(o).each((o=>{const r=((t,o)=>{const n=e.map((e=>t.compare(e,o)));return e=C.some(o),n})(n,o);r.each((e=>{t.trigger.move(e)}))}))},reset:()=>{e=C.none()},events:t.registry}})();let o=e;return{on:()=>{o.reset(),o=t},off:()=>{o.reset(),o=e},isOn:()=>o===t,onEvent:(e,t)=>{o.onEvent(e,t)},events:t.events}},rd=e=>{const t=e.replace(/\./g,"-");return{resolve:e=>t+"-"+e}},sd=rd("ephox-dragster").resolve;var ld=ed({compare:(e,t)=>pn(t.left-e.left,t.top-e.top),extract:e=>C.some(pn(e.x,e.y)),sink:(e,t)=>{const o=(e=>{const t={layerClass:sd("blocker"),...e},o=xe.fromTag("div");return ge(o,"role","presentation"),Nt(o,{position:"fixed",left:"0px",top:"0px",width:"100%",height:"100%"}),Mm(o,sd("blocker")),Mm(o,t.layerClass),{element:g(o),destroy:()=>{qe(o)}}})(t),n=qm(o.element(),"mousedown",e.forceDrop),r=qm(o.element(),"mouseup",e.drop),s=qm(o.element(),"mousemove",e.move),l=qm(o.element(),"mouseout",e.delayDrop);return td({element:o.element,start:e=>{Ie(e,o.element())},stop:()=>{qe(o.element())},destroy:()=>{o.destroy(),r.unbind(),s.unbind(),l.unbind(),n.unbind()}})},mutate:(e,t)=>{e.mutate(t.left,t.top)}});const ad=rd("ephox-snooker").resolve,cd=ad("resizer-bar"),id=ad("resizer-rows"),md=ad("resizer-cols"),dd=e=>{const t=mt(e.parent(),"."+cd);N(t,qe)},ud=(e,t,o)=>{const n=e.origin();N(t,(t=>{t.each((t=>{const r=o(n,t);Mm(r,cd),Ie(e.parent(),r)}))}))},fd=(e,t,o,n,r)=>{const s=wn(o),l=t.isResizable,a=n.length>0?Nn.positions(n,o):[],c=a.length>0?((e,t)=>j(e.all,((e,o)=>t(e.element)?[o]:[])))(e,l):[];((e,t,o,n)=>{ud(e,t,((e,t)=>{const r=((e,t,o,n,r)=>{const s=xe.fromTag("div");return Nt(s,{position:"absolute",left:t+"px",top:o-3.5+"px",height:"7px",width:n+"px"}),he(s,{"data-row":e,role:"presentation"}),s})(t.row,o.left-e.left,t.y-e.top,n);return Mm(r,id),r}))})(t,_(a,((e,t)=>O(c,(e=>t===e)))),s,Ao(o));const i=r.length>0?_n.positions(r,o):[],m=i.length>0?((e,t)=>{const o=[];return k(e.grid.columns,(n=>{sn(e,n).map((e=>e.element)).forall(t)&&o.push(n)})),_(o,(o=>{const n=tn(e,(e=>e.column===o));return P(n,(e=>t(e.element)))}))})(e,l):[];((e,t,o,n)=>{ud(e,t,((e,t)=>{const r=((e,t,o,n,r)=>{const s=xe.fromTag("div");return Nt(s,{position:"absolute",left:t-3.5+"px",top:o+"px",height:r+"px",width:"7px"}),he(s,{"data-column":e,role:"presentation"}),s})(t.col,t.x-e.left,o.top-e.top,0,n);return Mm(r,md),r}))})(t,_(i,((e,t)=>O(m,(e=>t===e)))),s,gn(o))},gd=(e,t)=>{if(dd(e),e.isResizable(t)){const o=Qo(t),n=cn(o),r=ln(o);fd(o,e,t,n,r)}},hd=(e,t)=>{const o=mt(e.parent(),"."+cd);N(o,t)},pd=e=>{hd(e,(e=>{Et(e,"display","none")}))},bd=e=>{hd(e,(e=>{Et(e,"display","block")}))},wd=ad("resizer-bar-dragging"),vd=e=>{const t=(()=>{const e=Jm({drag:Ym(["xDelta","yDelta","target"])});let t=C.none();const o=(()=>{const e=Jm({drag:Ym(["xDelta","yDelta"])});return{mutate:(t,o)=>{e.trigger.drag(t,o)},events:e.registry}})();return o.events.drag.bind((o=>{t.each((t=>{e.trigger.drag(o.xDelta,o.yDelta,t)}))})),{assign:e=>{t=C.some(e)},get:()=>t,mutate:o.mutate,events:e.registry}})(),o=((e,t={})=>{var o;return((e,t,o)=>{let n=!1;const r=Jm({start:Ym([]),stop:Ym([])}),s=nd(),l=()=>{m.stop(),s.isOn()&&(s.off(),r.trigger.stop())},c=((e,t)=>{let o=null;const n=()=>{a(o)||(clearTimeout(o),o=null)};return{cancel:n,throttle:(...t)=>{n(),o=setTimeout((()=>{o=null,e.apply(null,t)}),200)}}})(l);s.events.move.bind((o=>{t.mutate(e,o.info)}));const i=e=>(...t)=>{n&&e.apply(null,t)},m=t.sink(od({forceDrop:l,drop:i(l),move:i((e=>{c.cancel(),s.onEvent(e,t)})),delayDrop:i(c.throttle)}),o);return{element:m.element,go:e=>{m.start(e),s.on(),r.trigger.start()},on:()=>{n=!0},off:()=>{n=!1},isActive:()=>n,destroy:()=>{m.destroy()},events:r.registry}})(e,null!==(o=t.mode)&&void 0!==o?o:ld,t)})(t,{});let n=C.none();const r=(e,t)=>C.from(pe(e,t));t.events.drag.bind((e=>{r(e.target,"data-row").each((t=>{const o=jt(e.target,"top");Et(e.target,"top",o+e.yDelta+"px")})),r(e.target,"data-column").each((t=>{const o=jt(e.target,"left");Et(e.target,"left",o+e.xDelta+"px")}))}));const s=(e,t)=>jt(e,t)-Lt(e,"data-initial-"+t,0);o.events.stop.bind((()=>{t.get().each((t=>{n.each((o=>{r(t,"data-row").each((e=>{const n=s(t,"top");we(t,"data-initial-top"),d.trigger.adjustHeight(o,n,parseInt(e,10))})),r(t,"data-column").each((e=>{const n=s(t,"left");we(t,"data-initial-left"),d.trigger.adjustWidth(o,n,parseInt(e,10))})),gd(e,o)}))}))}));const l=(n,r)=>{d.trigger.startAdjust(),t.assign(n),ge(n,"data-initial-"+r,jt(n,r)),Mm(n,wd),Et(n,"opacity","0.2"),o.go(e.parent())},c=qm(e.parent(),"mousedown",(e=>{var t;t=e.target,jm(t,id)&&l(e.target,"top"),(e=>jm(e,md))(e.target)&&l(e.target,"left")})),i=t=>Re(t,e.view()),m=qm(e.view(),"mouseover",(t=>{var r;(r=t.target,bt(r,"table",i).filter(Jr)).fold((()=>{st(t.target)&&dd(e)}),(t=>{o.isActive()&&(n=C.some(t),gd(e,t))}))})),d=Jm({adjustHeight:Ym(["table","delta","row"]),adjustWidth:Ym(["table","delta","column"]),startAdjust:Ym([])});return{destroy:()=>{c.unbind(),m.unbind(),o.destroy(),dd(e)},refresh:t=>{gd(e,t)},on:o.on,off:o.off,hideBars:b(pd,e),showBars:b(bd,e),events:d.registry}},yd=e=>m(e)&&"TABLE"===e.nodeName,xd="bar-",Cd=e=>"false"!==pe(e,"data-mce-resize"),Td=e=>{const t=lm(),o=lm(),n=lm();let r,s,l,a;const c=t=>gc(e,t),i=()=>jr(e)?ol():tl(),m=(t,o,n,m)=>{const d=(e=>{return Tt(t=e,"corner-")?((e,t)=>e.substring(7))(t):t;var t})(o),u=St(d,"e"),f=Tt(d,"n");if(""===s&&Bc(t),""===a&&(e=>{const t=(e=>fn(e)+"px")(e);Dc(e,C.none(),C.some(t)),Nc(e)})(t),n!==r&&""!==s){Et(t,"width",s);const o=i(),l=c(t),a=jr(e)||u?(e=>nl(e).columns)(t)-1:0;ra(t,n-r,a,o,l)}else if((e=>/^(\d+(\.\d+)?)%$/.test(e))(s)){const e=parseFloat(s.replace("%",""));Et(t,"width",n*e/r+"%")}if((e=>/^(\d+(\.\d+)?)px$/.test(e))(s)&&(e=>{const t=Qo(e);rn(t)||N(Vt(e),(e=>{const t=Bt(e,"width");Et(e,"width",t),we(e,"width")}))})(t),m!==l&&""!==a){Et(t,"height",a);const e=f?0:(e=>nl(e).rows)(t)-1;sa(t,m-l,e)}};return e.on("init",(()=>{const r=((e,t)=>e.inline?((e,t,o)=>({parent:g(t),view:g(e),origin:g(pn(0,0)),isResizable:o}))(xe.fromDom(e.getBody()),(()=>{const e=xe.fromTag("div");return Nt(e,{position:"static",height:"0",width:"0",padding:"0",margin:"0",border:"0"}),Ie(lt(xe.fromDom(document)),e),e})(),t):((e,t)=>{const o=me(e)?(e=>xe.fromDom(Ee(e).dom.documentElement))(e):e;return{parent:g(o),view:g(e),origin:g(pn(0,0)),isResizable:t}})(xe.fromDom(e.getDoc()),t))(e,Cd);if(n.set(r),(e=>{const t=e.options.get("object_resizing");return D(t.split(","),"table")})(e)&&Vr(e)){const n=((e,t,o)=>{const n=Nn,r=_n,s=vd(e),l=Jm({beforeResize:Ym(["table","type"]),afterResize:Ym(["table","type"]),startDrag:Ym([])});return s.events.adjustHeight.bind((e=>{const t=e.table;l.trigger.beforeResize(t,"row");const o=n.delta(e.delta,t);sa(t,o,e.row),l.trigger.afterResize(t,"row")})),s.events.startAdjust.bind((e=>{l.trigger.startDrag()})),s.events.adjustWidth.bind((e=>{const n=e.table;l.trigger.beforeResize(n,"col");const s=r.delta(e.delta,n),a=o(n);ra(n,s,e.column,t,a),l.trigger.afterResize(n,"col")})),{on:s.on,off:s.off,refreshBars:s.refresh,hideBars:s.hideBars,showBars:s.showBars,destroy:s.destroy,events:l.registry}})(r,i(),c);e.mode.isReadOnly()||n.on(),n.events.startDrag.bind((o=>{t.set(e.selection.getRng())})),n.events.beforeResize.bind((t=>{const o=t.table.dom;((e,t,o,n,r)=>{e.dispatch("ObjectResizeStart",{target:t,width:o,height:n,origin:r})})(e,o,os(o),ns(o),xd+t.type)})),n.events.afterResize.bind((o=>{const n=o.table,r=n.dom;es(n),t.on((t=>{e.selection.setRng(t),e.focus()})),((e,t,o,n,r)=>{e.dispatch("ObjectResized",{target:t,width:o,height:n,origin:r})})(e,r,os(r),ns(r),xd+o.type),e.undoManager.add()})),o.set(n)}})),e.on("ObjectResizeStart",(t=>{const o=t.target;if(yd(o)&&!e.mode.isReadOnly()){const n=xe.fromDom(o);N(e.dom.select(".mce-clonedresizable"),(t=>{e.dom.addClass(t,"mce-"+Mr(e)+"-columns")})),!kc(n)&&Hr(e)?_c(n):!Oc(n)&&Fr(e)&&Bc(n),Ec(n)&&Tt(t.origin,xd)&&Bc(n),r=t.width,s=$r(e)?"":ss(e,o).getOr(""),l=t.height,a=ls(e,o).getOr("")}})),e.on("ObjectResized",(t=>{const o=t.target;if(yd(o)){const n=xe.fromDom(o),r=t.origin;(e=>Tt(e,"corner-"))(r)&&m(n,r,t.width,t.height),es(n),mc(e,n.dom,dc)}})),e.on("SwitchMode",(()=>{o.on((t=>{e.mode.isReadOnly()?(t.off(),t.hideBars()):(t.on(),t.showBars())}))})),e.on("dragstart dragend",(e=>{o.on((t=>{"dragstart"===e.type?(t.hideBars(),t.off()):(t.on(),t.showBars())}))})),e.on("remove",(()=>{o.on((e=>{e.destroy()})),n.on((t=>{((e,t)=>{e.inline&&qe(t.parent())})(e,t)}))})),{refresh:e=>{o.on((t=>t.refreshBars(xe.fromDom(e))))},hide:()=>{o.on((e=>e.hideBars()))},show:()=>{o.on((e=>e.showBars()))}}},Sd=e=>{(e=>{const t=e.options.register;t("table_clone_elements",{processor:"string[]"}),t("table_use_colgroups",{processor:"boolean",default:!0}),t("table_header_type",{processor:e=>{const t=D(["section","cells","sectionCells","auto"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be one of: section, cells, sectionCells or auto."}},default:"section"}),t("table_sizing_mode",{processor:"string",default:"auto"}),t("table_default_attributes",{processor:"object",default:{border:"1"}}),t("table_default_styles",{processor:"object",default:{"border-collapse":"collapse"}}),t("table_column_resizing",{processor:e=>{const t=D(["preservetable","resizetable"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be preservetable, or resizetable."}},default:"preservetable"}),t("table_resize_bars",{processor:"boolean",default:!0}),t("table_style_by_css",{processor:"boolean",default:!0}),t("table_merge_content_on_paste",{processor:"boolean",default:!0})})(e);const t=Td(e),o=Km(e,t),n=hc(e,t,o);return Xc(e,n),((e,t)=>{const o=Zr(e),n=t=>Is(ts(e)).bind((n=>Ut(n,o).map((o=>{const r=Ms(Fs(e),o,n);return t(o,r)})))).getOr("");G({mceTableRowType:()=>n(t.getTableRowType),mceTableCellType:()=>n(t.getTableCellType),mceTableColType:()=>n(t.getTableColType)},((t,o)=>e.addQueryValueHandler(o,t)))})(e,n),Hs(e,n),{getSelectedCells:o.getSelectedCells,clearSelectedCells:o.clearSelectedCells}};e.add("dom",(e=>({table:Sd(e)})))}();