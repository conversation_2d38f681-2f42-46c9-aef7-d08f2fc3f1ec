@extends('layouts.admin')
@section('title', 'Banners')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="fw-bold py-3 m-0">
    <span class="text-muted fw-light">Novidades / </span> Banners
  </h4>
</div>

<form class="card my-4" method="POST" action="{{ route('news.banners.update', [$banner1->id]) }}" enctype="multipart/form-data">
  @csrf
  <h5 class="card-header">Banner 1</h5>
  <div class="card-body">
    <div class="d-flex align-items-center">
      <div class="flex-grow-1 me-3">
        <label class="form-label">Título</label>
        <input type="text" class="form-control @error('title') is-invalid @enderror" placeholder="Insira o título" name="title" value="{{ $banner1->title }}">
      </div>
      <div class="flex-grow-1 me-3">
        <label class="form-label">Texto do botão</label>
        <input type="text" class="form-control" placeholder="Insira o texto do botão" name="button_text" value="{{ $banner1->button_text }}">
      </div>
    </div>
    <div class="d-flex mt-3">
      <div class="flex-grow-1 me-3">
        <label class="form-label">Link do Botão</label>
        <input type="text" class="form-control" placeholder="Insira o link do botão" name="button_link" value="{{ $banner1->button_link }}">
      </div>
      <div class="flex-grow-1 me-3">
        <label class="form-label">Foto</label>
        <input type="file" class="form-control" name="photo" value="{{ $banner1->photo }}">
        @isset($banner1->photo)
          <img src="{{ asset('storage/' . $banner1->photo) }}" class="mt-2" style="max-width:150px;">
        @endisset
      </div>
      <div class="form-check mt-3">
        <input class="form-check-input" type="checkbox" @checked($banner1->show) id="show" name="show">
        <label class="form-check-label" for="show">Exibir</label>
      </div>
    </div>
    <button class="btn btn-primary mt-3">Salvar</button>
  </div>
</form>

<form class="card my-4" method="POST" action="{{ route('news.banners.update', [$banner2->id]) }}" enctype="multipart/form-data">
  @csrf
  <h5 class="card-header">Banner 2</h5>
  <div class="card-body">
    <div class="d-flex align-items-center">
      <div class="flex-grow-1 me-3">
        <label class="form-label">Título</label>
        <input type="text" class="form-control @error('title') is-invalid @enderror" placeholder="Insira o título" name="title" value="{{ $banner2->title }}">
      </div>
      <div class="flex-grow-1 me-3">
        <label class="form-label">Texto do botão</label>
        <input type="text" class="form-control" placeholder="Insira o texto do botão" name="button_text" value="{{ $banner2->button_text }}">
      </div>
    </div>
    <div class="d-flex mt-3">
      <div class="flex-grow-1 me-3">
        <label class="form-label">Link do Botão</label>
        <input type="text" class="form-control" placeholder="Insira o link do botão" name="button_link" value="{{ $banner2->button_link }}">
      </div>
      <div class="flex-grow-1 me-3">
        <label class="form-label">Foto</label>
        <input type="file" class="form-control" name="photo" value="{{ $banner2->photo }}">
        @isset($banner2->photo)
          <img src="{{ asset('storage/' . $banner2->photo) }}" class="mt-2" style="max-width:150px;">
        @endisset
      </div>
      <div class="form-check mt-3">
        <input class="form-check-input" type="checkbox" @checked($banner2->show) id="show" name="show">
        <label class="form-check-label" for="show">Exibir</label>
      </div>
    </div>
    <button class="btn btn-primary mt-3">Salvar</button>
  </div>
</form>

  <script nonce="{{ csp_nonce() }}">
    $( "#sortable" ).sortable();

    $( ".accordion-button" ).on('click', function(){
      $(".accordion-collapse").collapse('hide');
    });
  </script>
@endsection