@extends('layouts.admin')
@section('title', 'Fixas')

@section('content')
<h4 class="fw-bold py-3 mb-4">
  <span class="text-muted fw-light">Páginas / </span><span class="text-muted fw-light">Fixas /</span> Nova
</h4>

<form method="POST" action="{{ route('pages.coded.store') }}" enctype="multipart/form-data">
  @csrf

  <div class="card">
    <h5 class="card-header">Configurações</h5>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-8">
          <label class="form-label">Titulo</label>
          <input type="text" class="form-control @error('title') is-invalid @enderror" placeholder="Digite o título" name="title" value="{{ old('title') }}" required>
        </div>
        <div class="mb-3 col-sm-4">
          <label class="form-label">Endpoint</label>
          <input type="text" class="form-control" placeholder="Digite o endpoint" name="endpoint" value="{{ old('endpoint') }}" required>
          <small>ex: /consultas/andamento-certidao</small>
        </div>    
      </div>

      <div class="row">
        <div class="mb-3 col-sm-5">
          <label class="form-label">Descrição breve</label>
          <textarea type="text" class="form-control @error('description') is-invalid @enderror" placeholder="Digite a descrição" name="description" rows="4">{{ old('description') }}</textarea>
        </div>
        <div class="mb-3 col-sm-2">
          <div class="form-check mt-3">
            <input class="form-check-input" type="checkbox" @checked(old('restricted_access')) id="restricted_access" name="restricted_access">
            <label class="form-check-label" for="restricted_access">
              Adicionar Cadeado
            </label>
          </div>
        </div>  
      </div>
    </div>
  </div>

  <button class="btn btn-primary mt-5">Criar</button>
</form>
@endsection