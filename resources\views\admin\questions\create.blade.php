@extends('layouts.admin')
@section('title', 'Dúvidas')

@section('content')
<h4 class="fw-bold py-3 mb-4">
  <span class="text-muted fw-light">Dúvidas / </span> Nova
</h4>

<form method="POST" action="{{ route('questions.q.store') }}">
  @csrf

  <div class="card" >
    <h5 class="card-header">Dúvida</h5>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-6">
          <label class="form-label">Pergunta</label>
          <textarea type="text" class="form-control @error('question') is-invalid @enderror" placeholder="Digite a pergunta" name="question" required rows="4">{{ old('question') }}</textarea>
        </div>
        <div class="mb-3 col-sm-6">
          <label class="form-label">Resposta</label>
          <textarea type="text" class="form-control global-editor @error('answer') is-invalid @enderror" placeholder="Digite a resposta" name="answer" rows="4">{{ old('answer') }}</textarea>
        </div>
      </div>
      <div class="row">
        <div class="col-sm-12">
          <select name="category_id" class="form-select">
            <label class="form-label">Categoria</label>
            <option value="">Selecione uma categoria...</option>
            @foreach ($categories as $category)
              <option value="{{ $category->id }}" @selected(old('category_id'))>{{ $category->category }}</option>
            @endforeach
          </select>
        </div>
      </div>
    </div>
  </div>

  <button class="btn btn-primary mt-3">Salvar</button>
</form>



<script nonce="{{ csp_nonce() }}">

</script>
@endsection