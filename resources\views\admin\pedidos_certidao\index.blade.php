@extends('layouts.admin')
@section('title', 'Pedidos Certidão')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h4 class="fw-bold py-3 m-0">
            Pedidos <PERSON>id<PERSON>
        </h4>
    </div>

    <div class="card">
        <div class="card-header d-flex justify-content-between">
            <h5 class="">Pedidos Certidão</h5>
            <form action="{{ route('admin.pedidos_certidao.index') }}">
                <div class="d-flex align-items-center">
                    <select class="form-select ms-2" name="status">
                        <option value="">Selecione o Status</option>
                        <option value="P" {{ request()->get('status') == 'P' ? 'selected' : '' }}>Pendente</option>
                        <option value="E" {{ request()->get('status') == 'E' ? 'selected' : '' }}>Elegível</option>
                        <option value="I" {{ request()->get('status') == 'I' ? 'selected' : '' }}>Inelegível</option>
                    </select>
                    <input type="text" class="form-control ms-2" name="pedido" placeholder="Digite o ID do Pedido"
                        value="{{ request()->get('pedido') }}">
                    <button class="btn btn-primary ms-3">Pesquisar</button>
                </div>
            </form>
        </div>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>ID Pedido</th>
                        <th>Emissão</th>
                        <th>Vencimento</th>
                        <th>Parte</th>
                        <th>Documento</th>
                        <th>Status</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody class="table-border-bottom-0">
                    @foreach ($pedidos as $pedido)
                        <tr>
                            <td>{{ $pedido->id }}</td>
                            <td>{{ $pedido->pedido_id }}</td>
                            <td>
                                @isset($pedido->data_emissao_boleto)
                                    {{ $pedido->data_emissao_boleto->format('d/m/Y') }}
                                @endisset
                            </td>
                            <td>
                                @isset($pedido->data_emissao_boleto)
                                    {{ $pedido->data_emissao_boleto->addDays(2)->format('d/m/Y') }}
                                @endisset
                            </td>
                            <td>{{ $pedido->name }}</td>
                            <td>{{ $pedido->document }}</td>
                            <td>{{ $pedido->descricaoStatus() }}</td>
                            <td>
                                <div class="dropdown">
                                    <button type="button" class="btn p-0 dropdown-toggle hide-arrow"
                                        data-bs-toggle="dropdown" aria-expanded="false"><i
                                            class="bx bx-dots-vertical-rounded"></i></button>
                                    <div class="dropdown-menu">
                                        <a class="dropdown-item"
                                            href="{{ route('admin.pedidos_certidao.show', ['id' => $pedido->id]) }}"><i
                                                class="bx bx-show me-1"></i> Ver</a>
                                        <form method="post"
                                            action="{{ route('admin.pedidos_certidao.update', $pedido->id) }}">
                                            @csrf
                                            <input type="hidden" value="1" name="status" id="status" />
                                            <a class="dropdown-item"
                                                onclick="if(confirm('Confirma protocolo {{ $pedido->pedido_id }} elegível?')){this.parentNode.submit();}"
                                                href="javascript: void(0)"><i
                                                    class="bx bx-check-circle me-1 text-primary"></i> Elegível</a>
                                        </form>
                                        <form method="post"
                                            action="{{ route('admin.pedidos_certidao.update', $pedido->id) }}">
                                            @csrf
                                            <input type="hidden" value="0" name="status" id="status" />
                                            <a class="dropdown-item"
                                                onclick="if(confirm('Confirma protocolo {{ $pedido->pedido_id }} NÃO elegível?')){this.parentNode.submit();}"
                                                href="javascript: void(0)"><i class="bx bx-x-circle me-1 text-danger"></i>
                                                Não
                                                elegível</a>
                                        </form>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        <div class="card-footer">
            {{ $pedidos->appends(request()->except('page'))->links() }}
        </div>
    </div>
@endsection
