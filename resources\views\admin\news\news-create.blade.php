@extends('layouts.admin')
@section('title', 'Novidades')

@section('content')
<h4 class="fw-bold py-3 mb-4">
  <span class="text-muted fw-light">Novidades / </span> Nova
</h4>

<form method="POST" action="{{ route('news.news.store') }}" enctype="multipart/form-data">
  @csrf

  <div class="card">
    <h5 class="card-header">Novidade</h5>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Titulo</label>
          <input type="text" class="form-control @error('title') is-invalid @enderror" placeholder="Insira o título" name="title" value="{{ old('title') }}" required>
        </div>
      </div>

      <div class="row">
        <div class="col-sm-3">
          <label class="form-label">Categoria:</label>
          <select class="form-select" name="category_id">
            <option value="">Selecione uma categoria</option>
            @foreach ($categories as $category)
              <option value="{{ $category->id }}" @selected(old('category_id'))>{{ $category->name }}</option>
            @endforeach
          </select>
        </div>


        <div class="col-sm-3">
          <label class="form-label">Autor:</label>
          <select class="form-select" name="author_id">
            <option value="">Selecione um autor</option>
            @foreach ($authors as $author)
              <option value="{{ $author->id }}" @selected(old('author_id'))>{{ $author->name }}</option>
            @endforeach
          </select>
        </div>

        <div class="col-sm-3">
          <label class="form-label">Foto:</label>
          <input type="file" class="form-control @error('photo') is-invalid @enderror" name="photo" value="{{ old('photo') }}">
        </div>

        <div class="mb-3 col-sm-2">
          <div class="form-check mt-3">
            <input class="form-check-input" type="checkbox" @checked(old('published')) id="published" name="published">
            <label class="form-check-label" for="published">
              Publicada
            </label>
          </div>
        </div>
      </div>
      <div class="row mt-3">
        <div class="mb-3 col-sm-12">
          <label class="form-label">conteudo</label>
          <textarea class="form-control editor @error('content') is-invalid @enderror global-editor" name="content" rows="50">{{ old('content') }}</textarea>
        </div>
      </div>
      <button type="submit" class="btn btn-primary">Criar</button>
    </div>
  </div>

</form>

<br><br><br>

<script src="{{asset("tinymce\\tinymce.min.js")}}"></script>

<script nonce="{{ csp_nonce() }}">
    function initEditor() {
        tinymce.init({
            selector: ".editor",
            theme: "silver",
            content_css: '/css/tinyTextArea-min.css',
            plugins: ['advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                    'anchor', 'searchreplace', 'visualblocks', 'fullscreen',
                    'insertdatetime', 'media', 'table', 'code', 'help', 'wordcount', 'hr'
            ],
            height: 300,
            resize: 'vertical',
            valid_elements : '*[*]',
            extended_valid_elements: 'script[language|type|src]',
            advcode_inline: true,
            tinymce_allow_script_urls: true,
            menubar: false,
            toolbar: 'undo redo | blocks | bold italic backcolor link | ' +
                    'alignleft aligncenter alignright alignjustify | ' +
                    'hr bullist numlist outdent indent | removeformat | code | help',
            language: 'pt_BR',
            setup: function(editor) {
                editor.on('keyup change undo', function () {
                editor.targetElm.innerHTML = editor.getContent();
                editor.targetElm.dispatchEvent(new Event("change"));
                });
            }
        });
    }

    $( document ).ready(function() {
      initEditor();
    });
</script>

@endsection
