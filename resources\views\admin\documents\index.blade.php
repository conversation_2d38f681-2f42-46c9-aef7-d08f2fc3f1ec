@extends('layouts.admin')
@section('title', 'Documentos')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="fw-bold py-3 m-0">
    Documentos
  </h4>
</div>

 <div class="card">
  <div class="card-header d-flex justify-content-between">
    <h5 class="">Documentos</h5>
  </div>
  <div class="card-body">
    <div class="d-flex align-items-center">
      <input type="text" class="form-control" id="ticket" placeholder="Digite o ticket" value="{{ request()->ticket }}" style="max-width: 150px">
      <input type="text" class="form-control ms-3" id="person" placeholder="Digite o cpf/nome da parte ou responsável" value="{{ request()->pessoa }}" style="max-width: 500px">
      <select type="text" class="form-select ms-3" id="clerk">
        <option value="">Todos os escreventes</option>
        @foreach ($clerks as $clerk)
          <option value="{{ $clerk->id }}" @selected(request()->escrevente == $clerk->id)>{{ $clerk->name }}</option>
        @endforeach
      </select>
      <select type="text" class="form-select ms-3" id="status" style="max-width: 200px;">
        <option value="">Todos documentos</option>
        <option value="opened" @selected(request()->status == 'opened')>Abertos</option>
        <option value="closed" @selected(request()->status == 'closed')>Fechados</option>
        <option value="canceled" @selected(request()->status == 'canceled')>Cancelados</option>
        <option value="dropped" @selected(request()->status == 'dropped')>Baixados</option>
      </select>
      <button class="btn btn-primary ms-3" onclick="filter()">Filtrar</button>
    </div>
  </div>
  <div class="table-responsive">
    <table class="table table-hover">
      <thead>
        <tr>
          <th>Ticket</th>
          <th>Última Atualização</th>
          <th>Tipo de Doc</th>
          <th>Parte</th>
          <th>CPF da Parte</th>
          <th>Escrevente</th>
          <th>Status</th>
          <th>Ação</th>
        </tr>
      </thead>
      <tbody class="table-border-bottom-0">
        @foreach ($docs as $doc)
          <tr>
            <tr>
              <td>{{ $doc->id }}</td>
              <td>{{ \Carbon\Carbon::parse($doc->last_user_update)->format('d/m/Y') }}</td>
              <td>{{ $doc->document_type }}</td>
              @if($doc->is_part)
                <td>{{ $doc->client->name }}</td>
                <td>{{ $doc->client->cpf }}</td>
              @else
                <td>{{ $doc->part_name }}</td>
                <td>{{ $doc->part_cpf }}</td>
              @endif
              <td>{{ $doc->clerk->name ?? '' }}</td>
              <td>{{ ucfirst(__('messages.' . $doc->status)) }}</td>
              <td>
                <div class="dropdown">
                  <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown" aria-expanded="false"><i class="bx bx-dots-vertical-rounded"></i></button>
                  <div class="dropdown-menu" style="">
                    <a class="dropdown-item" href="{{ route('admin.documents.show', [$doc->id]) }}"><i class="bx bx-show me-1"></i> Ver</a>
                  </div>
                </div>
              </td>
            </tr>
          </tr>
        @endforeach
      </tbody>
    </table>
  </div>
</div>

  <script nonce="{{ csp_nonce() }}">
    function filter() {
      var ticket = $('#ticket').val();
      var person = $('#person').val();
      var clerk = $('#clerk').val();
      var status = $('#status').val();

      var url = new URL(window.location.href);
      url.searchParams.set('ticket', ticket);
      url.searchParams.set('pessoa', person);
      url.searchParams.set('escrevente', clerk);
      url.searchParams.set('status', status);
      window.location = url.href;
    }
  </script>
@endsection