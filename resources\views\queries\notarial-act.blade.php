@extends('layouts.my-account')

@section('content')
<section class="account txtcontent">
  <div class="container-fluid">
	@if (!isset($act))
    <div class="px-1">
      <div class="breadcrumb">
        <a href="{{ route('my.services') }}">Meus Serviços</a>  >
        <span>Andamento do Ato Notarial</span>
      </div>
      <div class="d-flex justify-content-between align-items-start">
        <h1>Andamento do Ato Notarial</h1>
        <a href="{{ route('my.services') }}" class="btnDefault" style="background: #708082;">Voltar</a>
      </div>
    </div>
		<div class="row">
			<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
				<p><strong class="blue">PESQUISA POR PROTOCOLO</strong><br>Digite o número do CPF ou CNPJ e o número do protocolo impresso no documento e confira informações sobre o tipo de natureza praticada pelo ato, o imóvel e as partes envolvidas no negócio. </p>

				<form action="{{ route('queries.show.notarial.act') }}" method="POST">
                    @csrf
					<div class="formControl">
						<label>Selecione um tipo de pesquisa</label>
                        <select name="type" id="doc_type">
							<option value="cpf" @selected(request()->type == 'cpf')>CPF</option>
							<option value="cnpj" @selected(request()->type == 'cnpj')>CNPJ</option>
						</select>
					</div>
					<div class="formControl">
						<label>Informe o CPF/CNPJ:</label>
						<input type="text" required name="doc" id="cpfcnpj2" value="{{ request()->doc }}">
					</div>
					<div class="formControl">
						<label>Número do protocolo:</label>
						<input type="text" required name="proto" value="{{ request()->proto }}">
					</div>
					<div class="d-flex">
						<button class="btnDefault"  >Enviar</button>
					</div>
				</form>

                <script nonce="{{ csp_nonce() }}">
                    $(document).ready(function() {
                        // Obter os parâmetros da URL
                        const urlParams = new URLSearchParams(window.location.search);

                        // Obter o valor do parâmetro 'type'
                        const typeParam = urlParams.get('type');

                        const maskCPF = '000.000.000-000';
                        const maskCNPJ = '00.000.000/0000-00';
                        // Definir a máscara com base no parâmetro 'type'
                        const mask = (typeParam === 'cnpj') ? maskCNPJ : maskCPF;

                        // Aplicar a máscara ao campo
                        $('#cpfcnpj2').mask(mask);

                        $('#doc_type').change(function(){
                            $('#cpfcnpj2').unmask();
                            $('#cpfcnpj2').val('');
                            if($(this).val() == 'cnpj')
                                $('#cpfcnpj2').mask(maskCNPJ);
                            else
                                $('#cpfcnpj2').mask(maskCPF);
                        });
                    });
                </script>

			</div>

		</div>

    @else
    <div class="px-1">
      <div class="breadcrumb">
        <a href="{{ route('my.services') }}">Meus Serviços</a>  >
        <a href="{{ route('queries.notarial.act') }}">Andamento do Ato Notarial</a>  >
        <span>Resultado</span>
      </div>
      <div class="d-flex justify-content-between align-items-start">
        <h1>Andamento do Ato Notarial</h1>
        <a href="{{ route('queries.notarial.act') }}" class="btnDefault" style="background: #708082;">Voltar</a>
      </div>
    </div>
      <div class="row">

        <div class="resultadoBusca">

          <div class="headBusca">
            <h2>RESULTADO DA PESQUISA POR STATUS DE PROTOCOLO</h2>
          </div>

          @isset($act['andamentos'][0]->envioRI)
            <div class="row d-flex justify-content-between">

              <div class="infoBusca col-lg-6 p-0">
                <h3>Andamento no Registro Imobiliário</h3>
                <p>Clique no Registro Imobiliário para visualizar os status. Cadastre-se no Push 26 e receba gratuitamente as atualizações do andamento do ato.</p>
              </div>

              @isset($act['statusProtocolo']->protocolo)
                @php
                  $protocol = $act['statusProtocolo']->protocolo;
                  //$lastStatus = $act['andamentos'][0]->envioRI[0]->ultimoStatus;
                @endphp
                <div class="registroEmail">
                  <img src="{{ asset('img/push26.png') }}" alt="Cadastrar meu e-mail">
                  <div class="d-flex">
                    <a class="btnDefault" id="follow-protocol" onclick="followProtocol('{{ $doc }}', '{{ $protocol }}', '{{ $doc_type }}')" @if($followingAct) style="display: none" @endif>Acompanhar por e-mail</a>
                    <a id="unfollow-protocol" onclick="unfollowProtocol('{{ $protocol }}')" class="btnDefault red" @if(!$followingAct) style="display: none" @endif>Remover</a>
                  </div>
                </div>
              @endisset

            </div>
          @endisset

            <table class="registros">
              <tbody>
                <tr class="head">
                <th>Registro</th>
                <th>último Status</th>
                <th style="display: none">Cod. Interno</th>
                <th>Histórico</th>
              </tr>
              @isset($act['andamentos'][0]->envioRI)
                @foreach ($act['andamentos'][0]->envioRI as $andamento)
                  @php
                  $protocol_CodInterno = $andamento->codigo_interno;
                  @endphp
                    <tr>
                      <td class="registry">{{ $andamento->cartorio }} <span class="protocolo_cod_interno" style="display:none">{{ $protocol_CodInterno }}</span></td>
                      <td class="last-status">{{ $andamento->ultimoStatus }}</td>
                      <td class="cod_interno"  style="display: none">{{ $protocol_CodInterno }}</td>
                      <td><a class="btnTable" onclick="$('#{{ $andamento->codigo_interno }}').toggleClass('active'); $(this).toggleClass('active');"><span class="fa-solid"></span>Visualizar</a></td>
                      </tr>
                @endforeach
              @else
                  <td style="text-align: left;">Nenhum resultado encontrado para os parâmetros informados.<br />Ficou com dúvidas? Contate o setor de certidões do 26.</td>
              @endisset

              <tr>
            </tr></tbody></table>

          @isset($act['andamentos'][0]->envioRI)
            @foreach ($act['andamentos'][0]->envioRI as $andamento)
              <div class="registrosResultados" id="{{$andamento->codigo_interno}}">

                <h5>{{$andamento->cartorio}}</h5>
                <table>
                  <tbody><tr class="head">
                    <th>Data</th>
                    <th>Status</th>
                    <th>Detalhe</th>
                  </tr>

                    @foreach ($andamento->historico as $historico)
                      <tr>
                        <td>{{ $historico->data }}</td>
                        <td>{{ $historico->status }}</td>
                        @isset ($historico->arquivos)
                          <td>
                            @foreach ($historico->arquivos as $arquivo)
                              <a href="{{ $arquivo->url }}" class="btnTable download"><span class="fa-solid"></span>Download</a><br>
                            @endforeach
                          </td>
                        @else
                          <td></td>
                        @endisset
                      </tr>
                    @endforeach
              </tbody></table>

              </div>
            @endforeach
          @endisset

          @if (isset($act['statusProtocolo']) && $act['statusProtocolo']->situacaoDescricao != 'Incompleto' && $act['statusProtocolo']->situacaoDescricao != 'Sem Efeito' && $act['statusProtocolo']->situacaoDescricao != 'Em edição' && $act['statusProtocolo']->situacaoDescricao != 'Finalizado')
            <div class="registrosResultados active">

              <h3>Informação do Protocolo</h3>
              <table>
                <tbody><tr>
                  <td><strong>Tipo do Ato:</strong></td>
                  <td>{{ $act['statusProtocolo']->tipoato }}</td>
                </tr>
                <tr>
                  <td><strong>Nº do Protocolo:</strong></td>
                  <td>{{ $act['statusProtocolo']->protocolo }}</td>
                </tr>
                <tr>
                  <td><strong>Data de Lavratura:</strong></td>
                  <td>{{ $act['statusProtocolo']->dataLavratura }}</td>
                </tr>
                <tr>
                  <td><strong>Natureza:</strong></td>
                  <td>{{ $act['statusProtocolo']->natureza }}</td>
                </tr>
                <tr>
                  <td><strong>Livro:</strong></td>
                  <td>{{ $act['statusProtocolo']->livro }}</td>
                </tr>
                <tr>
                  <td><strong>Folha:</strong></td>
                  <td>{{ $act['statusProtocolo']->folha }}</td>
                </tr>
                <tr>
                  <td><strong>Situação do ato:</strong></td>
                  <td>{{ $act['statusProtocolo']->situacaoDescricao }}</td>
                </tr>
              </tbody></table>

            </div>

            @if(count($act['outorgantesProtocolo']) > 0 || count($act['outorgadosProtocolo']) > 0)
              <div class="registrosResultados active">

                <h3>Partes</h3>
                <table>
                  <tbody><tr class="head">
                    <th>Nome</th>
                    <th>Participação</th>
                  </tr>
                  @isset($act['outorgantesProtocolo'])
                    @foreach ($act['outorgantesProtocolo'] as $parte)
                      <tr>
                        <td>{{ $parte->nome }}</td>
                        <td>{{ $parte->participacao == "Parte" ? $parte->tipoParte : $parte->participacao}}</td>
                      </tr>
                    @endforeach
                  @endisset
                  @isset($act['outorgadosProtocolo'])
                    @foreach ($act['outorgadosProtocolo'] as $parte)
                      <tr>
                        <td>{{ $parte->nome }}</td>
                        <td>{{ $parte->participacao == "Parte" ? $parte->tipoParte : $parte->participacao}}</td>
                      </tr>
                    @endforeach
                  @endisset
                  <tr>
                </tr></tbody></table>

              </div>
            @endisset

            @if(isset($act['imoveisProtocolo']) > 0)
              <div class="registrosResultados active">

                <h3>Imóveis</h3>
                <table>
                  <tbody><tr class="head">
                    <th>Região</th>
                    <th>Endereço</th>
                    <th>Cidade</th>
                    <th>Tipo Imóvel</th>
                  </tr>
                  @foreach ($act['imoveisProtocolo'] as $imovel)
                  <tr>
                    <td>{{ $imovel->CidadeUF }}</td>
                    <td>{{ $imovel->Endereco }}, {{ $imovel->NumeroPredial }}</td>
                    <td>{{ $imovel->Regiao }}</td>
                    <td>{{ $imovel->TipoImovel }}</td>
                  </tr>
                  @endforeach
                  <tr>
                </tr></tbody></table>

              </div>
            @endisset
          @endif

        </div>

      </div>
    @endif

	</div>

</section>

<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script nonce="{{ csp_nonce() }}">
//  typeSelected();

  function followProtocol(doc, protocol, doc_type, cod_interno) {

    var registry = {};
    if ($('.registry').length > 0) {
      for (let i = 0; i < $('.registry').length; i++) {
        registry[$('.registry').eq(i).html()] = $('.last-status').eq(i).html();
      }
    }
    console.log(registry);


    var cod_interno = {};
    if ($('.cod_interno').length > 0) {
      for (let i = 0; i < $('.cod_interno').length; i++) {
        cod_interno[$('.cod_interno').eq(i).html()] = $('.cod_interno').eq(i).html();
      }
    }
    console.log(cod_interno);


    axios.post("{{ route('push26.follow.protocol') }}", {
      doc,
      protocol,
      doc_type,
      cod_interno,
      registry
    })
      .then((r) => {
        $('#follow-protocol').hide();
        $('#unfollow-protocol').show();
      });
  };

  function unfollowProtocol(protocol) {
    axios.post("{{ route('push26.unfollow.protocol') }}", {
      protocol: protocol
    })
      .then((r) => {
        $('#follow-protocol').show();
        $('#unfollow-protocol').hide();
      });
  }
</script>
@endsection
