@extends('layouts.admin')
@section('title', '<PERSON><PERSON>')

@section('breadcrumb')
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Menus / </span><span class="text-muted fw-light">Cabeçalho /</span> Editar
  </h4>
@endsection

@section('content')
<form method="POST" action="{{ route('menus.header.update', [$menu->id]) }}">
  @csrf

  <div class="card" x-data="menuComponent(@js($menu))">
    <h5 class="card-header">Menu</h5>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-2">
          <label class="form-label">Tipo</label>
          <select class="form-select" name="type" x-model="type">
            <option value="slug">slug</option>
            <option value="link">link</option>
          </select>
        </div>
        <div class="mb-3 col-sm-5">
          <label class="form-label">Titulo</label>
          <input type="text" class="form-control @error('title') is-invalid @enderror" placeholder="Digite o título" name="title" value="{{ $menu->title }}">
        </div>
        <template x-if="type == 'slug'">
          <div class="mb-3 col-sm-5">
            <label class="form-label">Slug</label>
            <input type="text" class="form-control @error('slug') is-invalid @enderror" placeholder="agende-seu-servico" name="slug" x-model="slug">
          </div>
        </template>

        <template x-if="type == 'link'">
          <div class="mb-3 col-sm-5">
            <label class="form-label">Link</label>
            <input type="text" class="form-control @error('link') is-invalid @enderror" placeholder="https://26notas.com.br/" name="link" x-model="link" x-on:keyup="filterLink">
            <template x-if="!$store.isValidLink && link.length > 0">
              <div class="invalid-feedback d-block">Link inválido</div>
            </template>
            <template x-if="$store.isValidLink">
              <div class="valid-feedback d-block">Link válido</div>
            </template>
          </div>
        </template>
      </div>
    </div>
  </div>

  <div id="sortable" x-data="submenuComponent(@js($menu->submenus), @js($errors->messages()))">
    <template x-for="(submenu, index) in submenus">
      <div class="card my-4">
        <div class="card-body">
          <div class="d-flex justify-content-between mb-1">
            <div class="d-flex">
              <i class='bx bxs-sort-alt cursor-move me-2'></i>
              <h5>Submenu</h5>
            </div>
            <i class='bx bx-trash text-danger cursor-pointer' x-on:click="remove(index)"></i>
          </div>
          <div class="row">
            <input type="hidden"x-model="submenu.id" :name="inputName(index, 'id')">
            <div class="mb-3 col-sm-6">
              <label class="form-label">Titulo</label>
              <input type="text" class="form-control" :class="{'is-invalid': hasErrors(index, 'title')}" placeholder="Digite o título" x-model="submenu.title" :name="inputName(index, 'title')">
            </div>
            <div class="mb-3 col-sm-6">
              <label class="form-label">Slug</label>
              <input type="text" class="form-control" :class="{'is-invalid': hasErrors(index, 'slug')}" placeholder="agende-seu-servico" x-model="submenu.slug" :name="'submenus[' + index + '][slug]'">
            </div>
          </div>
        </div>
      </div>
    </template>
    <div class="d-flex justify-content-center mt-4">
      <a x-on:click="add" class="cursor-pointer">Adicionar Submenu</a>
    </div>

    <input type="hidden" x-model="removedSubmenus" name="removed_submenus">
  </div>

  <button class="btn btn-primary mt-3">Salvar</button>
</form>



<script nonce="{{ csp_nonce() }}">
  var regexSlug = /^[a-z0-9]+(\-{1}[a-z0-9]+)*(\-)?$/;
  
  $( "#sortable" ).sortable();

  document.addEventListener('alpine:init', () => {
    Alpine.store('isValidLink', true);
  });

  function menuComponent(menu) {
    return {
      type: menu.type,
      slug: menu.slug,
      link: menu.link,
      filterLink() {
        var link = this.link;
        var reg = /^(?:http(s)?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\(\)\*\+,;=.]+$/;

        if (!reg.test(link)) {
          this.$store.isValidLink = false;
        } else {
          this.$store.isValidLink = true;
        }
      },
      filterSlug() {
        var slug = this.slug;

        if (!regexSlug.test(slug)) {
          this.slug = slug.slice(0, -1);
        }
      },
      removeLastHyphen() {
        var slug = this.slug;

        if (slug.substr(slug.length - 1) == '-') {
          this.slug = slug.slice(0, -1);
        }
      }
    }
  };

  function submenuComponent(initialSubmenus = [], validationErrors = []) {
    return {
      submenus: initialSubmenus,
      errors: validationErrors,
      removedSubmenusIds: [],
      add() {
        this.submenus.push({
          title: '',
          slug: ''
        });
      },
      remove(index) {
        this.removedSubmenusIds.push(this.submenus[index].id);
        this.submenus.splice(index, 1);
      },
      removedSubmenus() {
        return JSON.stringify(this.removedSubmenusIds);
      },
      inputName(index, input) {
        return 'submenus[' + index + '][' + input + ']';
      },
      hasErrors(index, input) {
        if (this.errors['submenus.' + index + '.' + input ]) {
          return true;
        }
        return false;
      }
    }
  }
</script>
@endsection