@extends('layouts.app')
@section('title', 'Login')
@section('content')
    <style>
        .input-group {
            position: relative;
        }

        .password {
            padding-right: 30px; /* Espaço para o ícone */
        }

        .toggle-password {
            position: absolute;
            right: 10px;
            top: 40%;
            transform: translateY(-50%);
            cursor: pointer;
        }
    </style>
    <section class="account txtcontent">

        <div class="container-fluid">

            <div class="row">

                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">

                    <h1>Acesso Cliente 26º</h1>
                    <p>Informe seus dados para acessar a conta.<br></p>

                    <form action="{{ route('login.token') . '#anchor' }}" method="POST">
                        @csrf
                        <div class="formControl">
                            <label for="email">E-mail</label>
                            <div class="input-group">
                                <input type="email" name="email" value="{{ old('email') }}" class="mb-0" required>
                                <small class="email-input-info"><strong>Aviso Importante:</strong> Usuários de hotmail.com e yahoo.com.br podem não receber nossos e-mails devido a políticas de bloqueio desses provedores. Recomendamos verificar a pasta de spam ou usar outro endereço de e-mail.
Agradecemos pela&nbsp;compreensão!</small>
                            </div>
                        </div>
                        <div class="formControl">
                            <label for="password">Digite sua senha</label>
                            <div class="input-group">
                                <input type="password" min="6" id="password" name="password" class="password">
                                <span class="toggle-password"><i class="fa fa-eye"></i></span>
                            </div>
                            <label><a href="/recuperar-senha" style="color: #00909d;">Esqueceu sua senha?</a></label>
                            <br>
                            <label><a href="/cadastro" style="color: #00909d;">Criar conta.</a></label>
                        </div>

                        <div class="formControl check">
                            <input type="checkbox" required>
                            <label>Li, estou de acordo com a <a href="/politica-de-privacidade" target="_blank">política
                                    de privacidade</a> do 26º Tabelionato de Notas.</label>
                        </div>
                        <div class="reCaptcha">
                            {!! htmlFormSnippet() !!}
                        </div>
                        <button class="btnDefault">Acessar</button>
                        <div id="anchor" class="anchorLogin"></div>
                        @if(session('success'))
                            <div class="message success">
                                <p>Sucesso</p>
                                <p>{{ session('success') }}</p>
                            </div>
                        @endif

                        @if($errors->any())
                            <div class="message error">
                                <p>Erro</p>
                                <ul class="mt-2" style="font-size: 15px;">
                                    @foreach ($errors->all() as $error)
                                        <li style="color:#a94442;">{!! $error !!}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                    </form>

                </div>

            </div>
        </div>
    </section>
    <script>
        $(document).ready(function () {
            $('.toggle-password').click(function () {
                // $(this) refere-se ao elemento .toggle-password que foi clicado
                var input = $(this).prev('.password'); // Seleciona o campo de senha anterior
                if (input.attr('type') === 'password') {
                    input.attr('type', 'text');
                } else {
                    input.attr('type', 'password');
                }
            });
        });
    </script>
@endsection
