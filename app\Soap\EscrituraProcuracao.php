<?php namespace App\Soap;

use Artisaninweb\SoapWrapper\Extension\SoapService;

class EscrituraProcuracao extends SoapService {

    /**
     * @var string
     */
    protected $name = 'escritura-procuracao';

    /**
     * @var string
     */
    protected $wsdl = 'http://servico26notas.cartorios.net/EscrituraProcuracaoWebService?wsdl';

    /**
     * @var boolean
     */
    protected $trace = true;

    /**
     * Get all the available functions
     *
     * @return mixed
     */
    public function functions()
    {
        return $this->getFunctions();
    }

    /**
     * Get all the available functions
     *
     * @return mixed
     */
    public function lastRequest()
    {
        return $this->getLastRequest();
    }

    public function _client(){
        return $this->client;
    }
}