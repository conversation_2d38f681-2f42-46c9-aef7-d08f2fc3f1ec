<?php

namespace App\Http\Controllers;

use App\Models\ClientCollaborator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\CollaboratorOccupation;
use App\Models\Client;
use App\Models\Page;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Gate;
use Carbon\Carbon;

class ClientController extends Controller
{
    public function myAcc()
    {
        $client = Client::find(Auth::id());
        $occupations = $this->getOccupationsWithMyClerks();

        return view('clients.account', ['client' => $client, 'occupations' => $occupations]);
    }

    private function getOccupationsWithMyClerks()
    {
        $client = Client::find(Auth::id());
        $allOccupations = CollaboratorOccupation::all();

        $clerksIds = $client->collaborators->pluck('id')->toArray();

        $occupations = [];
        foreach ($allOccupations as $key => $occupation) {
            $occupation['my_clerks'] = $occupation->collaborators('main')->whereIn('collaborators.id', $clerksIds)->get();
            $occupations[] = $occupation;
        }

        return $occupations;
    }

    public function addClerk(Request $r)
    {
        $id = Auth::id();
        $cc = ClientCollaborator::where([
            'collaborator_id' => $r->collaborator_id,
            'client_id' => $id
        ])->first();

        if (!$cc) {
            $cc = new ClientCollaborator;
            $cc->collaborator_id = $r->collaborator_id;
            $cc->client_id = $id;
            $cc->save();
        }
        return redirect()->back();
    }

    public function removeClerk($id)
    {
        ClientCollaborator::where('collaborator_id', $id)->delete();
        return redirect()->back();
    }

    public function myServices()
    {
        if (!Gate::allows('use_services'))
            return redirect()->route('my.acc')->withErrors([
                'error_client_cannot_use_services' => 'Queremos conhecer os nossos usuários, informe seus dados e usufrua dos nossos serviços.'
            ]);

        $page = Page::where([
            'type' => 'clientes_servicos',
        ])->first();

        $settings = json_decode($page->dynamic_page_settings);

        $settings->block1 = array_filter($settings->block1);
        $orderb1 = implode(',', $settings->block1);

        if ($orderb1 != '') {
            $settings->block1 = Page::published()->whereIn('id', $settings->block1)->orderByRaw("FIELD(id, $orderb1)")
                ->get();
        }

        $client = Auth::user();
        $occupations = $this->getOccupationsWithMyClerks();

        return view('clients.services', ['client' => $client, 'occupations' => $occupations, 'settings' => $settings]);
    }

    public function myAccUpdate(Request $r)
    {
        $validator = Validator::make($r->all(), [
            'name' => 'required',
            'cpf' => 'required',
            'birthdate' => 'required|date_format:Y-m-d,d/m/Y',
            'password' => 'nullable|min:6',
            'phone' => 'required',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        if (strpos($r->birthdate, '/')) {
            $r['birthdate'] = Carbon::createFromFormat('d/m/Y', $r['birthdate'])->format('Y-m-d');
        }


        $client = Client::find(Auth::id());
        $client->name = $r->name;
        $client->cpf = $r->cpf;
        $client->birthdate = $r->birthdate;
        $client->phone = $r->phone;
        $client->gender = $r->gender;

        if (!empty($r->password)) {
            if (!$client->is_active) {
                $client->is_active = 1;
            }
            $client->password = Hash::make($r->password);
        }

        $client->save();

        return redirect()->route('my.acc')->with('success', 'Dados cadastrados com sucesso.');
    }
}
