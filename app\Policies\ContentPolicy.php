<?php
namespace App\Policies;
use <PERSON><PERSON>\Csp\Keyword;
use <PERSON>tie\Csp\Directive;
use <PERSON><PERSON>\Csp\Policies\Basic;

class ContentPolicy extends Basic {
  public function configure()
  {
   // parent::configure();

    $this->addDirective(Directive::FONT, [
      Keyword::SELF,
      '*.gstatic.com',
      'cdn.jsdelivr.net',
      '*.tiny.cloud',
      '*.tinymce.com',
      '*.cdnfonts.com',
      'vlibras.gov.br',
      'fonts.gstatic.com',
      'fonts.googleapis.com',
    ]);
    
    

    
    $this->addDirective(Directive::STYLE, [
      Keyword::SELF,
      keyword::UNSAFE_INLINE,
      'cdn.jsdelivr.net',
      '26notasoficial.simplybook.me',
      '*.tiny.cloud',
      '*.tinymce.com',
      '*.googleapis.com',
    ]);
    
    $this->addDirective(Directive::FRAME, [
      Keyword::SELF,
      keyword::UNSAFE_INLINE,
      '*.simplybook.me',
      '*.google.com'
    ]);

    $this->addDirective(Directive::IMG, [
      Keyword::SELF,
      keyword::UNSAFE_INLINE,
      '*.tiny.cloud',
      '*.tinymce.com',
      'vlibras.gov.br',
      '*',
      'unsafe-inline',
      'data:',
    ]);

    
    
    $this->addDirective(Directive::SCRIPT, [
      keyword::UNSAFE_EVAL,
      keyword::SELF,
      keyword::UNSAFE_HASHES,
      keyword::UNSAFE_INLINE,
      'cdnjs.cloudflare.com',
      'cdn.jsdelivr.net',
      '*.gstatic.com',
      '*.jquery.com',
      '*.google.com',
      '*.simplybook.me',
      '26notasoficial.simplybook.me',
      'unpkg.com',
      '*.tiny.cloud',
      '*.tinymce.com',
      '*.github.io',
      'vlibras.gov.br',
      'blob:',
    ]);

    $this->addDirective(Directive::WORKER, [
      Keyword::SELF,
      'vlibras.gov.br',
      'blob:',
    ]);

    $this->addDirective(Directive::SCRIPT_ATTR, [
      keyword::SELF,
      keyword::UNSAFE_INLINE,
    ]);

    $this->addDirective(Directive::STYLE_ATTR, [
      keyword::SELF,
      keyword::UNSAFE_INLINE,
    ]);

    $this->addDirective(Directive::STYLE_ELEM, [
      keyword::SELF,
      keyword::UNSAFE_INLINE,
      '*.jquery.com',
      '*.googleapis.com',
      '*.cdnfonts.com',
      'cdn.jsdelivr.net',
      '*.tiny.cloud',
      '*.tinymce.com',
    ]);




    $this->addNonceForDirective(Directive::STYLE);
    $this->addNonceForDirective(Directive::SCRIPT);
    $this->addDirective(Directive::OBJECT, [
      keyword::NONE,
    ]);
  }
}
