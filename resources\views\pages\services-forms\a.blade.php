<div id="form-anchor"></div>
<form id="a" class="includePage" action="{{ route('forms.send') . '#anchor' }}" method="POST" enctype="multipart/form-data" style="position: relative">
  @csrf
<input type="hidden" name="form" value="a">
<input type="hidden" value="{{ $page->title }}" name="page_title">
  <input type="hidden" name="título" value="{{ $form->title }}">
  <input type="hidden" name="select_name" value="{{ $form->settings->select_name }}">
  <input type="hidden" name="recipients" value="{{ $form->recipients }}">
  <input type="hidden" name="email_template" value="{{ $form->email_template }}">
  <input type="hidden" name="service" value="{{ $page->title }}">
  <input type="hidden" name="bitrix" value="{{ $form->tipo_bitrix ?? "" }}">

  <div class="formControl">
    <label>Razão Social / Nome Completo</label>
    <input type="text" name="nome" value="{{ old('nome') }}" required>
  </div>
  <div class="formControl">
    <label>Telefone</label>
    <input type="text" name="telefone" value="{{ old('telefone') }}" required>
  </div>
  <div class="formControl">
    <label>E-mail</label>
    <input type="email" name="email" value="{{ old('email') }}" required>
  </div>
  <div class="formControl">
    <label>{{ $form->settings->select_name }}</label>
    <select onchange="selectChanged(this)" name="{{ $form->settings->select_name }}">
      <option value="">Selecione...</option>
      @php
        $options = array_filter(explode(';', $form->settings->select_options));
        $label = str_replace(" ", "_", $form->settings->select_name);
      @endphp
      @foreach ($options as $option)
        <option value="{{ $option }}" @selected($option == old($label))>{{ $option }}</option>
      @endforeach
      <option value="outros" @selected(old($label) == 'outros')>Outros</option>
    </select>
  </div>
  <div class="formControl" id="others_text" name="others_text" style="display: none;">
    <label>Outros</label>
    <input type="text" name="outros">
  </div>
  <div class="formControl file">
    <label>Envio de documento</label>
    <input type="file" name="file[]" style="color:black;" multiple>
  </div>
  <div class="formControl">
    <label>Mensagem</label>
    <textarea name="messagem" id="" cols="30" rows="10">{{ old('messagem') }}</textarea>
  </div>
  <div class="formControl check mb-1">
    <input type="checkbox" id="accordingly_checkbox" name="accordingly_checkbox" required>
    <label for="accordingly_checkbox">Li, estou de acordo com a <a href="/politica-de-privacidade" target="_blank">política de privacidade</a></label>
  </div>
  <div class="formControl check mt-1">
    <input type="checkbox" id="want_messages_checkbox" name="want_messages_checkbox">
    <label for="want_messages_checkbox">Desejo receber mensagens informativas do 26º Tabelionato de Notas.</label>
  </div>
  <div class="reCaptcha">
		{!! htmlFormSnippet() !!}
	</div>
  <div class="formControl">
    <button class="btnDefault red">Enviar</button>
  </div>

  <div id="anchor"></div>
  @if(session('form_success'))
    <div class="message success">
      <p>Formulário enviado com sucesso!</p>
      <p>Obrigado por usar os serviços do 26.</p>
      <p>Em breve faremos contato para tratar a sua solicitação.</p>
    </div>
  @endif

  @if(session('form_error') || $errors->any())
    <div class="message error">
      <p>Formulário não enviado.</p>
      @if ($errors->any())
        <ul class="mt-2">
          @foreach ($errors->all() as $error)
            <li style="color:#a94442;">{{ $error }}</li>
          @endforeach
        </ul>
      @endif
    </div>
  @endif
</form>

<script nonce="{{ csp_nonce() }}">
  function selectChanged(select)
  {
    if ($(select).val() == 'others') {
      $('#others_text').show();
    } else {
      $('#others_text').hide();
    }
  }
</script>
