<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Question;
use App\Models\QuestionCategories;

class QuestionController extends Controller
{
  public function questions(Request $r)
  {
    $txt = '%';
    if (!empty($r->busca)) {
      $txt = '%' . $r->busca . '%';
    }

    $categoriesIds = Question::select('category_id')
                            ->where('question', 'like', $txt)
                            ->groupBy('category_id')
                            ->pluck('category_id');

    $categories = QuestionCategories::whereIn('id', $categoriesIds)
                                  ->orderBy('index')
                                  ->get();

    return view('questions.questions', [
      'categories' => $categories
    ]);
  }
}
