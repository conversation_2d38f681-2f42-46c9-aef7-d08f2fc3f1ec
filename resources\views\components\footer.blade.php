<footer class="footer">
		
  <div class="container-fluid">
    
    <div id="footerContent" class="contentFooter">
      <div class="logo">
        <img src="{{ asset('img/logo.svg') }}" alt="Logo">
      </div>
      <nav>
        <ul class="menuFooter">
        @foreach ($menus as $menu)
          @if($menu->position == 'footer')
            @if ($menu->type == 'link')
              <li><a href="{{ $menu->link }}" @if($menu->blank) target="_blank" @endif>{{ $menu->title }}</a></li>
            @else
              @if ($menu->page->type == 'coded')
                <li><a href='{{ url("/{$menu->page->coded_endpoint}") }}' @if($menu->blank) target="_blank" @endif class="@if($menu->page->restricted_access) private-page @endif">{{ $menu->title }}</a></li>
              @elseif ($menu->page->template == 'default' || $menu->page->type == 'dynamic')
                <li><a href='{{ url("/{$menu->page->slug}") }}' @if($menu->blank) target="_blank" @endif class="@if($menu->page->restricted_access) private-page @endif">{{ $menu->title }}</a></li>
              @else
                <li><a href='{{ url("/" . __('messages.slug.' . $menu->page->template) . "/" . $menu->page->slug) }}' @if($menu->blank) target="_blank" @endif class="@if($menu->page->restricted_access) private-page @endif">{{ $menu->title }}</a></li>
              @endif
            @endif
          @endif
        @endforeach
        </ul>
      </nav>
    </div>

    <div class="row">
      
      <div class="col-lg-6 col-md-12 col-sm-12 col-xs-12">
        <p class="textAndress">
          <a href="https://goo.gl/maps/pWQ5vPEMvqCgd9jN9" target="_blank" title="Abrir o endereço no Google Maps">Praça João Mendes, 42, 1º ao 3º andar - Centro - São Paulo - SP</a><br>
          Atendimento de 2ª a 6ª feira, das 9 às 17 horas
        </p>
        <div class="btnsFooter">
        {{--  <a href="{{ route('contact.us') }}" class="btnLink">Fale Conosco</a> --}}
          <a href="{{ route('ouvidoria') }}" class="btnLink">Ouvidoria</a>
        </div>
      </div>
      <div class="col-lg-3 col-md-12 col-sm-12 col-xs-12 clCenter">
        <p class="subTitle">Responsabilidade</p>
        <img src="{{ asset('img/responsabilidaddeSocioambiental.png') }}" alt="Selo de Responsabilidade">
      </div>
      <div class="col-lg-3 col-md-12 col-sm-12 col-xs-12">
        <p class="subTitle">Nossas redes</p>
        <ul class="redesSociais">
          <li>
            <a href="https://www.linkedin.com/company/26-tabelionato-de-notas-de-s-o-paulo" target="_blank">
              <img src="{{ asset('img/redesSociais/linkedin.svg') }}" alt="Linkedin">
            </a>
          </li>
          <li>
            <a href="https://www.facebook.com/26notasoficial" target="_blank">
              <img src="{{ asset('img/redesSociais/facebook.svg') }}" alt="Facebook">
            </a>
          </li>
          <li>
            <a href="https://www.instagram.com/26notas/" target="_blank">
              <img src="{{ asset('img/redesSociais/instagram.svg') }}" alt="Instagram">
            </a>
          </li>
          <li>
            <a href="https://www.youtube.com/channel/UCXtj4T8fYu1EQSQY6wKR58g" target="_blank">
              <img src="{{ asset('img/redesSociais/youtube.svg') }}" alt="Youtube">
            </a>
          </li>
          <li>
            <a href="https://26notas.com.br/blog/" target="_blank">
              <img src="{{ asset('img/redesSociais/b.svg') }}" alt="B">
            </a>
          </li>
        </ul>
      </div>

    </div>

    <div class="copyright">
      
      <div class="infoCopyright">
        <p>26º Tabelionato de Notas de São Paulo. © 2015 CNPJ: 45.588.233/0001-95 Desde 1964. Todos os direitos reservados.</p>
        <a href="/politica-de-privacidade">Política de Privacidade</a>
        <a href="/aviso-legal">Aviso Legal</a>
      </div>
{{-- Por ser cartório não pode constar marca de empresas privadas com fins lucrativos.}}
      <a href="https://lampejos.com.br" target="_blank">
        <img src="{{ asset('img/lampejos.svg') }}" alt="Lampejos">
      </a>
{{--}}

    </div>

  </div>

</footer>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.16/jquery.mask.min.js" integrity="sha512-pHVGpX7F/27yZ0ISY+VVjyULApbDlD0/X0rgGbTqCE7WFW5MezNTWG/dnhtbBuICzsd0WQPgpE4REBLv+UqChw==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
{{-- <link src="{{ asset('js/jquery.autocomplete.js') }}"> --}}
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>

<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  <script nonce="{{ csp_nonce() }}">
    function search()
    {
      var txt = $('.searchInput').val();
        if (txt.length >= 3) {
        axios.get("{{ route('search') }}", {
          params: {
            txt,
            limit: 5
          }
        })
        .then((r) => {
          var html = '';

          if (r.data.length > 0) {
            r.data.forEach((page) => {
              html += '<li><a href="'+ page.final_link + '"';
              if (page.restricted_access) {
                html += ' class="private-page"';
              }
              html += '>';
              html += page.title;
              html += '</a></li>';
            });
            html += '<li><a onclick="submitSearchForm()" style="color: var(--azul); display:flex; justify-content: center;">Ver todos resultados</a></li>';
            $('.searchForm .result ul').html(html);
            $('.searchForm .result').show();
          } else {
            $('.searchForm .result').hide();
          }
        });
      } else {
        $('.searchForm .result').hide();
      }
    }
    function submitSearchForm(){
      $('.searchForm').submit();
    }
    function searchServices()
    {
      
      var txt = $('.uneedInput').val();
        if (txt.length >= 3) {
        axios.get("{{ route('home.services') }}", {
          params: {
            txt
          }
        })
        .then((r) => {
          var html = '';

          if (r.data.length > 0) {
            r.data.forEach((page) => {
              html += '<li><a href="/servicos/'+page.slug+'">';
              html += page.title;
              html += '</a></li>';
                $('.uneedForm .result ul').html(html);
            });
            $('.uneedForm .result').show();
          } else {
            $('.uneedForm .result').hide();
          }
        });
      } else {
        $('.uneedForm .result').hide();
      }
    }
    function closeResult()
    {
      setTimeout(() => {
        $('.result').hide();
        $('.result ul').html('');  
      }, 200);
    }

    function addToNewsletter(e, form)
    {
      e.preventDefault();
      var email = $(form).find('.newsletter').val();

      $.get("{{ route('newsletter.index') }}", {
        email
      }, function(r) {
        $(form).html('<p>Obrigado por se inscrever, agora você vai acompanhar todas as novidades!</p>');
      });
      
    }
  </script>

    {{-- Vlibras - https://vlibras.gov.br/doc/widget/installation/webpageintegration.html --}}
    <div vw class="enabled">
      <div vw-access-button class="active"></div>
      <div vw-plugin-wrapper>
        <div class="vw-plugin-top-wrapper"></div>
      </div>
    </div>
    <script src="https://vlibras.gov.br/app/vlibras-plugin.js"></script>
    <script nonce="{{ csp_nonce() }}">
      new window.VLibras.Widget('https://vlibras.gov.br/app');
    </script>
</body>
</html>