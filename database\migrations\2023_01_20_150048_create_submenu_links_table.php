<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('submenu_links', function (Blueprint $table) {
          $table->id();
          $table->string('title')->unique()->nullable();
          $table->string('type');
          $table->string('link')->nullable();
          $table->boolean('blank')->default(0);
          $table->unsignedBigInteger('page_id')->nullable();
          $table->unsignedBigInteger('submenu_id');
          $table->foreign('submenu_id')->references('id')->on('submenus');
          $table->unsignedTinyInteger('index');
          $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('submenu_links');
    }
};
