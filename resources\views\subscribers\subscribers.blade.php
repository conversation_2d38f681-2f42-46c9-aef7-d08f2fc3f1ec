@extends('layouts.app')
@section('title', 'MENSALISTAS')

@section('content')


<section class="escrituraOnline txtcontent" style="background-image: url('/img/mensalistas.jpg');">
  <div class="container-fluid lingueta">
    <h1 style="font-weight: bold">MENSALISTAS</h1>
    <p><PERSON><PERSON><PERSON> as vantagens de ser um cliente especial.</p>
  </div>
</section>
<section class="contentServico txtcontent">

	<div class="container-fluid">
		<div class="row">
			<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">

            <h2>Os clientes mensalistas são especiais porque tem até 56 dias para o pagamento de nossos serviços. Recebem atendimento em um balcão especial ou podem chamar um escrevente para abertura de fichas ou coleta de documentos para autenticação.</h2>

			</div>
		</div>

    <div class="row">
      <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
        <h2 class="red" style="margin-bottom: 32px">Especial por que...</h2>
        <ul>
          <li>Agilidade</li>
          <li>Confiabilidade</li>
          <li>Assessoria plena até a emissão do traslado</li>
          <li>Comodidade</li>
          <li>Credibilidade</li>
          <li>Encaminhamento da escritura para registro</li>
        </ul>
      </div>

      <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
        <h2 class="red" style="margin-bottom: 32px">Vantagens</h2>
        <ul>
          <li>Setor exclusivo</li>
          <li>Sem espera na fila</li>
          <li>Retirada e devolução dos serviços</li>
          <li>Faturamento dos serviços</li>
          <li>Extrato mensal dos serviços realizados</li>
          <li>Atendimento personalizado</li>
          <li>Solicitação via telefone ou e-mail</li>
          <li>Agendamento para retirada diária dos serviços</li>
          <li>Até 56 dias para pagamento</li>
          <li>Sistema "on-line" exclusivo</li>
        </ul>
      </div>
    </div>

    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12">
          <h1 class="mt-3">Se interessou?</h1>
          <h2>Envie sua mensagem</h2>
          {{-- <p>Questionamentos, sugestões, críticas e elogios podem ser encaminhados à Ouvidoria, mediante preenchimento do formulário abaixo.</p> --}}

          <form id="" class="includePage" action="{{ route('forms.send') . '#anchor' }}" method="POST" enctype="multipart/form-data" style="position: relative">
            @csrf
            <input type="hidden" name="email_subject" value="Formulário de Mensalistas">
            <input type="hidden" name="título" value="Mensalistas">
            <input type="hidden" name="recipients" value="<EMAIL>">
            <input type="hidden" name="email_template" value="mensalistas-user">
            <input type="hidden" name="form" value="mensalistas">
            <input type="hidden" name="bitrix" value="29">
            <input type="hidden" value="Mensalistas" name="page_title">

            <div class="formControl">
              <label>Razão Social / Nome Completo</label>
              <input type="text" name="nome" value="{{ old('nome') }}" required>
            </div>

            <div class="formControl">
              <label>Telefone com DDD</label>
              <input type="text" name="telefone" value="{{ old('telefone') }}" required>
            </div>

            <div class="formControl">
              <label>E-mail</label>
              <input type="email" name="email" value="{{ old('email') }}" required>
            </div>

            <div class="formControl">
              <label>Mensagem</label>
              <textarea name="mensagem" id="" cols="30" rows="10">{{ old('mensagem') }}</textarea>
            </div>

            <div class="formControl check mb-1">
              <input type="checkbox" id="accordingly_checkbox" name="accordingly_checkbox" required>
              <label for="accordingly_checkbox">Li, estou de acordo com a <a href="/politica-de-privacidade" target="_blank">política de privacidade</a></label>
            </div>
            <div class="formControl check mt-1">
              <input type="checkbox" id="want_messages_checkbox" name="want_messages_checkbox">
              <label for="want_messages_checkbox">Desejo receber mensagens informativas do 26º Tabelionato de Notas.</label>
            </div>
            <div class="reCaptcha">
              {!! htmlFormSnippet() !!}
            </div>
            <div class="formControl">
              <button class="btnDefault red">Enviar</button>
            </div>

            <div id="anchor"></div>
            @if(session('form_success'))
              <div class="message success">
                <p>Formulário enviado com sucesso!</p>
                <p>Obrigado por usar os serviços do 26.</p>
                <p>Em breve faremos contato para tratar a sua solicitação.</p>
              </div>
            @endif

            @if(session('form_error') || $errors->any())
              <div class="message error">
                <p>Formulário não enviado.</p>
                @if ($errors->any())
                  <ul class="mt-2">
                    @foreach ($errors->all() as $error)
                      <li style="color:#a94442;">{{ $error }}</li>
                    @endforeach
                  </ul>
                @endif
              </div>
            @endif
          </form>
        </div>
      </div>
    </div>
	</div>
</section>

@endsection
