@extends('layouts.app')
@section('title', $submenu->title)

@section('content')
<section class="contentIncludeMenu txtcontent">

	<div class="container-fluid">

		<h1>{{ $submenu->title }}</h1>
		<ul class="subMenu">
      <li class="closeSubMenu"><span>Voltar</span></li>
      @foreach ($submenu->links as $link)
        <li>
          <ul>
            @php
                $css = "";
                if(isset($link->color)){
                    $css = "color-" . $link->color;
                }
                if(isset($link->bold) && $link->bold == 1){
                    $css .= " strong";
                }
            @endphp
            @if ($link->type == 'link')
              <li><a href="{{ $link->link }}" title="{{ $link->title }}" @if($link->blank) target="_blank" @endif class="{{$css}}">{{ $link->title }}</a></li>
              <li>Acesse...</li>
              <li class="menuLink"><a href="{{ $link->link }}" title="{{ $link->title }}" class="btnLink {{$css}}" @if($link->blank) target="_blank" @endif>Saiba mais</a></li>
            @else
              @if ($link->page->type == 'coded')
                <li><a href='{{ url("/{$link->page->coded_endpoint}") }}' title="{{ $link->page->title}}"  @if($link->blank) target="_blank" @endif class="@if($link->page->restricted_access) private-page @endif {{$css}}">{{ $link->page->title }}</a></li>
                <li>{{ $link->page->description }}</li>
                <li class="menuLink"><a  href='{{ url("/{$link->page->coded_endpoint}") }}' class="btnLink">Saiba mais</a></li>
              @elseif ($link->page->template == 'default' || $link->page->type == 'dynamic')
                <li><a href='{{ url("/{$link->page->slug}") }}' title="{{ $link->page->title }}" @if($link->blank) target="_blank" @endif class="@if($link->page->restricted_access) private-page @endif {{$css}}">{{ $link->page->title }}</a></li>
                <li>{{ $link->page->description }}</li>
                <li class="menuLink"><a href='{{ url("/{$link->page->slug}") }}' class="btnLink">Saiba mais</a></li>
              @else
                <li><a href='{{ url("/" . __('messages.slug.' . $link->page->template) . "/" . $link->page->slug) }}' title="{{ $link->page->title }}" @if($link->blank) target="_blank" @endif class="@if($link->page->restricted_access) private-page @endif {{$css}}">{{ $link->page->title }}</a></li>
                <li>{{ $link->page->description }}</li>
                <li class="menuLink"><a href='{{ url("/" . __('messages.slug.' . $link->page->template) . "/" . $link->page->slug) }}' title="{{ $link->title }}" class="btnLink">Saiba mais</a></li>
              @endif

            @endif
          </ul>
        </li>
      @endforeach
    </ul>
		<div class="row listAtalhos">
      @foreach ($dynamic_page_settings->shortcuts as $shortcut)
        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12">
          <div class="blocoAtalhoDefault bgGray">
            <div class="iconeAtalho" style="color: #2a5c6e; font-size: 35px;">
              {!! $shortcut->icon !!}
            </div>
            <div class="infoAtalho">
              <p>{{ $shortcut->title }}</p>
              <a href="{{ $shortcut->link }}" class="btnLink">{{ $shortcut->text_link }}</a>
            </div>
          </div>
        </div>
      @endforeach
		</div>

	</div>

</section>
@endsection
