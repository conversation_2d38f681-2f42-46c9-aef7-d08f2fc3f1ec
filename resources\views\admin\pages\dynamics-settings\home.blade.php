@extends('layouts.admin')
@section('title', 'Home')

@section('content')
@php
  if (!empty($dynamic_page_settings))
    $settings = json_decode($dynamic_page_settings);
@endphp

<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="fw-bold py-3 m-0">
    <span class="text-muted fw-light">Páginas / Dinamicas / </span> Home
  </h4>
</div>

<form method="POST" action="{{ route('pages.dynamics.update', ['home']) }}" enctype="multipart/form-data">
  @csrf

  <div class="card">
    <h5 class="card-header">Configurações</h5>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Titulo</label>
          <input type="text" class="form-control @error('title') is-invalid @enderror" placeholder="Digite o título" name="title" value="home" required readonly>
        </div>
      </div>

      <div class="row">
        <div class="mb-3 col-sm-5">
          <label class="form-label">Descrição breve</label>
          <textarea type="text" class="form-control @error('description') is-invalid @enderror" placeholder="Digite a descrição" name="description" rows="4">{{ $page->description ?? '' }}</textarea>
        </div>
      </div>
    </div>
  </div>

  <div class="card mt-4">
    <h5 class="card-header">Banner Principal</h5>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-4">
          <label class="form-label">Imagem do banner</label>
          <input type="file" class="form-control @error('title') is-invalid @enderror" name="banner_img"><br>
          @isset($settings->banner_img->hash_name)
            <img src="{{ asset('storage/' . $settings->banner_img->hash_name) }}" style="max-width:315px;">
          @endisset
        </div>



        <div class="mb-3 col-sm-4">
          <label class="form-label">Bloco 1 de páginas</label><br>
          @for ($i = 0; $i < 6; $i++)
          <label class="form-label">Página {{$i + 1}}</label>
            <select class="form-select" name="block1[]">
              <option value="">Selecionar...</option>
              @foreach ($pages as $thePage)
                <option value="{{$thePage->id}}" @if(isset($settings->block1[$i]) && $settings->block1[$i] == $thePage->id) selected="selected" @endif>{{$thePage->title}}</option>
              @endforeach
            </select>
            <div class="row" style="margin-bottom: 1.3rem">
                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="radio" @if(isset($settings->{"color1" . $i}) && $settings->{"color1" . $i} == 'padrao') checked="checked" @endif value="padrao" name="color1{{$i}}">
                                <label class="form-check-label">
                                Cor padrão
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="radio" value="azul" id="corazul" @if(isset($settings->{"color1" . $i}) && $settings->{"color1" . $i} == 'azul')  checked="checked" @endif name="color1{{$i}}">
                                <label class="form-check-label">
                                Cor azul
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="radio" value="vermelho" id="corvermelho" @if(isset($settings->{"color1" . $i}) && $settings->{"color1" . $i} == 'vermelho') checked="checked" @endif name="color1{{$i}}">
                                <label class="form-check-label">
                                Cor vermelho
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="checkbox" x-model="link.bold" @if(isset($settings->{"bold1" . $i}) && $settings->{"bold1" . $i} == 'on') checked="checked" @endif name="bold1{{$i}}">
                                <label class="form-check-label">
                                Negrito
                                </label>
                            </div>
                        </div>
                        <div class="col-md-5">
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="checkbox" x-model="link.blank" @if(isset($settings->{"blank1" . $i}) && $settings->{"blank1" . $i} == 'on') checked="checked" @endif name="blank1{{$i}}">
                                <label class="form-check-label">
                                    Abrir em nova guia
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
          @endfor
        </div>

        <div class="mb-3 col-sm-4">
          <label class="form-label">Bloco 2 de páginas</label><br>
          @for ($i = 0; $i < 6; $i++)
          <label class="form-label">Página {{$i + 1}}</label>
            <select class="form-select" name="block2[]">
              <option value="">Selecionar...</option>
              @foreach ($pages as $thePage)
                <option value="{{$thePage->id}}" @if(isset($settings->block2[$i]) && $settings->block2[$i] == $thePage->id) selected="selected" @endif>{{$thePage->title}}</option>
              @endforeach
            </select>
            <div class="row" style="margin-bottom: 1.3rem">
                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="radio" @if(isset($settings->{"color2" . $i}) && $settings->{"color2" . $i} == 'padrao') checked="checked" @endif value="padrao" name="color2{{$i}}">
                                <label class="form-check-label">
                                Cor padrão
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="radio" value="azul" id="corazul" @if(isset($settings->{"color2" . $i}) && $settings->{"color2" . $i} == 'azul')  checked="checked" @endif name="color2{{$i}}">
                                <label class="form-check-label">
                                Cor azul
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="radio" value="vermelho" id="corvermelho" @if(isset($settings->{"color2" . $i}) && $settings->{"color2" . $i} == 'vermelho') checked="checked" @endif name="color2{{$i}}">
                                <label class="form-check-label">
                                Cor vermelho
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="checkbox" x-model="link.bold" @if(isset($settings->{"bold2" . $i}) && $settings->{"bold2" . $i} == 'on') checked="checked" @endif name="bold2{{$i}}">
                                <label class="form-check-label">
                                Negrito
                                </label>
                            </div>
                        </div>
                        <div class="col-md-5">
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="checkbox" x-model="link.blank" @if(isset($settings->{"blank2" . $i}) && $settings->{"blank2" . $i} == 'on') checked="checked" @endif name="blank2{{$i}}">
                                <label class="form-check-label">
                                    Abrir em nova guia
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
          @endfor
        </div>
      </div>
    </div>
  </div>

  <div class="card mt-4">
    <h5 class="card-header">Atalhos</h5>
    <div class="card-body">
      <div class="row">
        @for ($i = 0; $i < 4; $i++)
          <div class="mb-3 col-sm-3">
            <h6>Atalho #{{ $i+1 }}</h6>
            <label class="form-label">Titulo</label>
            <input type="text" class="form-control @error('title') is-invalid @enderror" placeholder="Digite o título" name="shortcuts[{{ $i }}][title]" value="{{ $settings->shortcuts[$i]->title ?? '' }}"><br>

            <label class="form-label">Ícone</label> (<a href="https://fontawesome.com/search?o=r&m=free" target="_blank">ver ícones</a>)
            <input type="text" class="form-control @error('title') is-invalid @enderror" placeholder="Digite o código do ícone" name="shortcuts[{{ $i }}][icon]" value="{{ $settings->shortcuts[$i]->icon ?? '' }}"><br>

            <label class="form-label">Texto do Link</label>
            <input type="text" class="form-control @error('title') is-invalid @enderror" placeholder="Digite o texto do link" name="shortcuts[{{ $i }}][text_link]" value="{{ $settings->shortcuts[$i]->text_link ?? '' }}"><br>

            <label class="form-label">Link</label>
            <input type="text" class="form-control @error('title') is-invalid @enderror" placeholder="Digite o link" name="shortcuts[{{ $i }}][link]" value="{{ $settings->shortcuts[$i]->link ?? '' }}"><br>
          </div>
        @endfor
      </div>
    </div>
  </div>

  <div class="card mt-4">
    <h5 class="card-header">Atalhos com foto</h5>
    <div class="card-body">
      <div class="row">
        @for ($i = 0; $i < 2; $i++)
          <div class="mb-3 col-sm-6">
            <h6>Atalho #{{ $i + 1 }} </h6>
            <label class="form-label">Titulo</label>
            <input type="text" class="form-control @error('title') is-invalid @enderror" placeholder="Digite o título" name="image_shortcuts[{{ $i }}][title]" value="{{ $settings->image_shortcuts[$i]->title ?? '' }}"><br>

            <label class="form-label">Texto do Link</label>
            <input type="text" class="form-control @error('title') is-invalid @enderror" placeholder="Digite o texto do link" name="image_shortcuts[{{ $i }}][text_link]" value="{{ $settings->image_shortcuts[$i]->text_link ?? '' }}"><br>

            <label class="form-label">Link</label>
            <input type="text" class="form-control @error('title') is-invalid @enderror" placeholder="Digite o link" name="image_shortcuts[{{ $i }}][link]" value="{{ $settings->image_shortcuts[$i]->link ?? '' }}"><br>

            <label class="form-label">Foto</label>
            <input type="file" class="form-control @error('title') is-invalid @enderror" name="image_shortcuts[{{ $i }}][image]"><br>

            @isset($settings->image_shortcuts[$i]->image->hash_name)
              <img src="{{ asset('storage/' . $settings->image_shortcuts[$i]->image->hash_name) }}">
            @endisset

          </div>
        @endfor
      </div>
    </div>
  </div>

  <div class="card mt-4">
    <h5 class="card-header">Seção chamada para ação</h5>
    <div class="card-body">
      <div class="row">
        <div class="col-md-6">
          <label class="form-label">Texto da seção</label>
          <textarea type="text" class="form-control" rows="14" placeholder="Digite o texto da seção" name="cta[text]">{{ $settings->cta->text ?? '' }}</textarea><br>
        </div>
        <div class="mb-3 col-sm-6">


          <label class="form-label">Link (opcional - deixe em branco para não exibir)</label>
          <input type="text" class="form-control" placeholder="Digite o link" name="cta[link]" value="{{ $settings->cta->link?? '' }}"><br>

          <label class="form-label">Texto do Link</label>
          <input type="text" class="form-control" placeholder="Digite o texto do link" name="cta[text_link]" value="{{ $settings->cta->text_link ?? '' }}"><br>

          <label class="form-label">Imagem</label>
          <input type="file" class="form-control" name="cta[image]"><br>
          @isset($settings->cta->image->hash_name)
            <img src="{{ asset('storage/' . $settings->cta->image->hash_name) }}" style="max-width:315px;">
          @endisset
        </div>
      </div>
    </div>
  </div>

  <button class="btn btn-primary mt-5">Salvar</button>
</form>


<br><br><br><br><br><br><br>
<script nonce="{{ csp_nonce() }}">
  $('input').removeAttr('required'); // TODO: remove
</script>
@endsection
