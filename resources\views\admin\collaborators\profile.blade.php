@extends('layouts.admin')
@section('title', '<PERSON><PERSON>ta')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="fw-bold py-3 m-0">
    <PERSON><PERSON>
  </h4>
</div>

<form class="card my-4" method="POST" action="{{ route('collaborator.profile.update') }}">
  @csrf
  <h5 class="card-header"><PERSON><PERSON></h5>
  <div class="card-body">
    <div class="d-flex align-items-center">
      <div class="flex-grow-1 me-3">
        <label class="form-label">Nome</label>
        <input type="text" class="form-control @error('name') is-invalid @enderror" placeholder="Digite o nome" name="name" value="{{ $user->name }}" readonly>
      </div>
      <div class="flex-grow-1 me-3">
        <label class="form-label">Nome</label>
        <input type="text" class="form-control @error('name') is-invalid @enderror" placeholder="Digite o nome" name="name" value="{{ $user->name }}" readonly>
      </div>
    </div>

    <div class="d-flex align-items-center mt-3">
      <div class="flex-grow-1 me-3">
        <label class="form-label">Email</label>
        <input type="text" class="form-control @error('name') is-invalid @enderror" placeholder="Digite o e-mail" name="email" value="{{ $user->email }}" readonly>
      </div>
      <div class="flex-grow-1 me-3">
        <label class="form-label">Nova Senha</label>
        <input type="text" class="form-control @error('name') is-invalid @enderror" placeholder="Digite uma nova senha" name="password" value="">
      </div>
    </div>
    <button class="btn btn-primary mt-3">Salvar</button>
  </div>
</form>

  <script nonce="{{ csp_nonce() }}">
    $( "#sortable" ).sortable();

    $( ".accordion-button" ).on('click', function(){
      $(".accordion-collapse").collapse('hide');
    });
  </script>
@endsection