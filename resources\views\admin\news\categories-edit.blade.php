@extends('layouts.admin')
@section('title', 'Categorias')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="fw-bold py-3 m-0">
    <span class="text-muted fw-light">Novidades / </span> Categorias
  </h4>
</div>

<form class="card my-4" method="POST" action="{{ route('news.categories.update', [$category->id]) }}">
  @csrf
  <h5 class="card-header">Editar Categoria</h5>
  <div class="card-body">
    <div class="d-flex align-items-center">
      <div class="flex-grow-1 me-3">
        <label class="form-label">Nome</label>
        <input type="text" class="form-control @error('name') is-invalid @enderror" placeholder="Insira o nome da categoria" name="name" value="{{ $category->name }}">
      </div>
    </div>
    <button class="btn btn-primary mt-3"><PERSON>var</button>
  </div>
</form>
@endsection