<?php

namespace App\Providers;
use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use App\Models\Collaborator;
use App\Models\Client;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        Gate::before(function ($user, string $ability) {
          if ($user->type == 'admin')
            return true;
        });

        Gate::define('manage_boleto', function (Collaborator $user) {
          return in_array('manage_boleto', json_decode($user->permissions));
        });

        Gate::define('use_boleto', function (Collaborator $user) {
          return in_array('use_boleto', json_decode($user->permissions));
        });

        Gate::define('manage_collaborators', function (Collaborator $user) {
          return in_array('manage_collaborators', json_decode($user->permissions));
        });

        Gate::define('manage_this_user', function (Collaborator $me, Collaborator $user) {
          return $me->can('manage_collaborators') && $user->type != 'admin';
        });

        Gate::define('use_documents', function (Collaborator $user) {
          return in_array('use_documents', json_decode($user->permissions));
        });

        Gate::define('manager_newsletter', function (Collaborator $user) {
            return in_array('manager_newsletter', json_decode($user->permissions));
        });

        Gate::define('pedidos_certidao', function (Collaborator $user) {
            return in_array('pedidos_certidao', json_decode($user->permissions));
        });

        Gate::define('manager_formularios', function (Collaborator $user) {
            return in_array('manager_formularios', json_decode($user->permissions));
        });

        Gate::define('use_services', function (Client $client) {
          if (empty($client->name) || empty($client->phone) || empty($client->cpf) || empty($client->birthdate))
            return false;

          return true;
        });
    }
}
