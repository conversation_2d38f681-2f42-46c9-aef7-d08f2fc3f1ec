<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Page;
use App\Models\CollaboratorOccupation;
use Illuminate\Support\Facades\Mail;
use App\Mail\SendNotaryFeeResult;
use App\Models\Boleto;
use App\Models\NewsBanners;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Exceptions\HttpResponseException;

class PageController extends Controller
{
  /**
   * TEMPLATES FUNCTIONS
   */
  private function getPage($slug, $template)
  {
    $page = Page::published()->where([
      'slug' => $slug,
      'template' => $template
    ])->first();

    if (empty($page))
      abort(404);

    if ($page->restricted_access) {
      if (!Auth::check()) {
        throw new HttpResponseException(redirect()->route('login'));

      } else if (!Gate::allows('use_services')) {
        $redirect = redirect()->route('my.acc')->withErrors([
          'error_client_cannot_use_services' => 'Queremos conhecer os nossos usuários, informe seus dados e usufrua dos nossos serviços.'
        ]);

        throw new HttpResponseException($redirect);
      }
    }

    return $page;
  }

  public function services($slug)
  {
    $slugMutations = [
      // 'de' => 'para',
    ];

    if (array_key_exists($slug, $slugMutations)) {
      return redirect()->route('services', $slugMutations[$slug]);
    }

    $page = $this->getPage($slug, 'services');

    $form = json_decode($page->template_form_settings);
    $collaboratorOccupations = CollaboratorOccupation::all();

    return view('pages.services', [
      'page' => $page,
      'form' => $form,
      'collaboratorOccupations' => $collaboratorOccupations
    ]);
  }

  public function default($slug)
  {
    $page = $this->getPage($slug, 'default');

    $banners = json_decode($page->general_settings)->banner_sidebar ?? [];
    if ($banners)
      $banners = NewsBanners::all();


    return view('pages.default', [
      'page' => $page,
      'banners' => $banners
    ]);
  }

  public function contracts($slug)
  {
    $page = $this->getPage($slug, 'contracts');

    return view('pages.contracts', [
      'page' => $page,
    ]);
  }

  /**
   * DYNAMIC PAGES FUNCTIONS
   */
  public function priceList()
  {

    $page = Page::where([
      'type' => 'dynamic',
      'dynamic_page' => 'price_list'
    ])->first();
    $page->dynamic_page_settings = json_decode($page->dynamic_page_settings);
    return view('price-list.price-list', [
      'page' => $page
    ]);
  }
  public function priceListSendMail(Request $r)
  {
    $data = $r->except(['email', 'name']);
    Mail::to($r->email)->send(new SendNotaryFeeResult($data, $r->name));
  }

  public function institutional()
  {
    $page = Page::where([
      'type' => 'dynamic',
      'dynamic_page' => 'institutional'
    ])->first();
    $page->dynamic_page_settings = json_decode($page->dynamic_page_settings);
    return view('institutional.institutional', [
      'page' => $page,
      'settings' => $page->dynamic_page_settings
    ]);
  }

  /**
   * GENERAL PAGES FUNCTIONS
   */
  public function listContracts()
  {
    $contracts = Page::published()->where('template', 'contracts')->get();

    return view('contracts.contracts', [
      'contracts' => $contracts,
    ]);
  }

  public function ouvidoria()
  {
    return view('contact.ouvidoria');
  }

  public function subscribers()
  {
    return view('subscribers.subscribers');
  }

  public function WithYou26()
  {
    return view('with-you-26.with-you-26');
  }

  public function onlinePayment(Request $r)
  {
    if (!empty($r->codigo)) {

      $boleto = Boleto::where('access_password', $r->codigo)->first();

      if (!empty($boleto))
        return view('online-payment.show', ['boleto' => $boleto]);

      return redirect()->route('online.payment')->withInput()->with('error', 'Código de acesso inválido!');
    }

    return view('online-payment.index');
  }

  public function blog26()
  {
    return redirect()->away('https://www.26notas.com.br/blog/');
  }
}
