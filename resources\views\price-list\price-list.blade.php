@extends('layouts.app')
@section('title', 'Tabela de preços')

@section('content')

<section class="account contentServico txtcontent">
	<div class="container-fluid">
    <div class="px-1">
      <div class="breadcrumb">
        <a href="{{ route('my.services') }}">Meus Serviços</a>  >
        <span>Tabela de Preços</span>
      </div>
      <div class="d-flex justify-content-between align-items-start">
        <h1>Tabela de Preços</h1>
        <a href="{{ route('my.services') }}" class="btnDefault" style="background: #708082;">Voltar</a>
      </div>
    </div>
		<div class="row">
      {!! $page->dynamic_page_settings->before_calc !!}
		</div>
    <div class="row" id="calcform">
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="padding: 0px;">
        <h2>ESCRITURA COM VALOR DECLARADO</h2>
        <p>Digite o valor base de cálculo, escolha o tipo de escritura e clique no botão "Calcular".</p>

        <form id="form-base" class="includePage" action="{{ route('forms.send') . '#anchor' }}" method="POST" enctype="multipart/form-data" style="position: relative; margin-top:0px">
          <div id="anchor"></div>
          @csrf
          <div class="formControl" style="margin-bottom: 0;">
            <label>Base de cálculo (R$)</label>
            <input type="text" name="base" id="base" class="money" required>
          </div>

          <div class="formControl">
            <label>Tipo de Escritura</label>
            <div class="typeRadio">
              <input type="radio" name="type" id="on" value="on" checked>
              <label for="on">Onerosa (ITBI) - {{ $page->dynamic_page_settings->onerousDeed }}% sobre o valor informado (cidade de São Paulo)</label><br>
            </div>
            <div class="typeRadio">
              <input type="radio" id="non" name="type" value="non">
              <label for="non">Não Onerosa (ITCMD) - {{ $page->dynamic_page_settings->nonOnerousDeed }}% sobre o valor informado (estado de São Paulo)</label><br>
            </div>
          </div>

          <div class="formControl">
            <button type="button" class="btnDefault red" onclick="calc()">Calcular</button>
          </div>
        
          @if(session('form_success'))
            <div class="message success">
              <p>Formulário enviado com sucesso!</p>
              <p>Obrigado por usar os serviços do 26.</p>
              <p>Em breve faremos contato para tratar a sua solicitação.</p>
            </div>
          @endif
          
          @if(session('form_error'))
            <div class="message error">
              <p>Formulário não enviado.</p>
            </div>
          @endif
        </form>

        
			</div>
		</div>

    <div class="row result" style="display: none;">
      <div class="custas-notariais-btn-recalculo" style="min-width: 100%;">
        <button type="button" class="btnDefault red" onclick="newCalc()" style="border: none;">Fazer novo calculo</button>
      </div>

      <div class="custas-notariais-result">
        <h2>Tabela de Preços</h2>
        <table>
          <tbody>
            <tr class="head">
              <th>Valor Informado (R$)</th>
              <th>Escritura (R$)</th>
              <th>Imposto (R$)</th>
              <th>Registro (R$)</th>
              <th>Valor Total (R$)</th>
            </tr>
            <tr>
              <td class="money value">R$ 1500,00</td>
              <td class="deed">R$ 1500,00</td>
              <td class="tax">R$ 1500,00</td>
              <td class="registry">R$ 1500,00</td>
              <td class="total">R$ 1500,00</td>
            </tr>
            <tr>
          </tr>
          </tbody>
        </table>
        <div class="custas-notariais-result-mobile">
          <p>
            Valor Informado: <span class="money value">XXX</span><br>
            Escritura: <span class="deed">XXX</span><br>
            Imposto: <span class="tax">XXX</span><br>
            Registro: <span class="registry">XXX</span><br>
            Valor Total: <span class="total">XXX</span>
          </p>
        </div>
        <div class="row custas-notariais-infos">
          <div class="col-md-12" style="padding: 0px;">
            <p style="color:red; font-weight: bold">Atenção!</p>
            
            <p><b>Escritura:</b> Os valores resultantes deste cálculo são <span style="color: red">meramente estimativos</span>. E não contemplam valores de documentos ou certidões necessários ao ato notarial solicitado.</p>
            
            <p><b>Imposto:</b> Na escritura onerosa incide o ITBI (cidade de São Paulo) correspondente a 3% sobre o valor informado. Na escritura não onerosa incide o ITCMD (estado de São Paulo) correspondente a 4% sobre o valor informado.</p>
            
            <p><b>Isenções:</b> ITBI, clique <a href="https://www.prefeitura.sp.gov.br/cidade/secretarias/fazenda/servicos/itbi/index.php?p=2517" target="_blank">aqui</a>. ITCMD, clique <a href="https://portal.fazenda.sp.gov.br/servicos/itcmd" target="_blank">aqui</a>.</p>

            <p><b>Registro imobiliário:</b> Os valores resultantes deste cálculo são <span style="color: red">meramente estimativos</span>. E não contemplam valores de prenotação, averbação, ou documentos e certidões exigidos para o registro, dentre outros.</p>
          </div>
        </div>
      </div>

          
        
        <div class="custas-notariais-sendmail">
          <div style="min-width: 100%">
            <h2 style="margin: 0;">RESULTADO POR EMAIL</h2>
          <p>Receba o resultado em seu e-mail informando seus dados abaixo</p>
          </div>

          <form id="send-mail-form" class="includePage" action="{{ route('forms.send') . '#anchor' }}" method="POST" enctype="multipart/form-data" style="position: relative">
            <div id="anchor" alt="form anchor" style="top: -200px; position: absolute;z-index: -1000;"></div>
            @csrf
            <div class="formControl">
              <label>Informe seu nome</label>
              <input type="text" name="name" id="name" required>
              
              <br>
              <label>Informe seu e-mail</label>
              <input type="email" name="email" id="email" required>
            </div>
            <div class="formControl" style="margin-top: 24px;">
              <button type="button" class="btnDefault red" onclick="sendEmail(this)">Enviar</button>
            </div>
              <div class="message success" style="display: none;">
                <p>Cálculo enviado com sucesso.</p>
                <p>Obrigado por usar os serviços do 26.</p>
              </div>
              <div class="message error" style="display: none;">
                <p>Formulário não enviado.</p>
              </div>
          </form>
        </div>
      </div>
    <div class="row">
      {!! $page->dynamic_page_settings->after_calc !!}
		</div>
	</div>
</section>

<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

<script nonce="{{ csp_nonce() }}">


function formatNumberWithTwoDecimals(number) {
  return parseFloat(number).toLocaleString('pt-BR', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
}

  function calc()
  {
    if (!$("#form-base").valid())
      return;

    var value = $('#base').val();
        value = value.replaceAll('.', '');
        value = value.replace(',', '.');

    var settings = @js($page->dynamic_page_settings);
        propertyTracks = settings.propertyTracks;
        emolumentsAndTaxesTracks = settings.emolumentsAndTaxesTracks;

    var registry = 0;
    for (let track = 0; track < propertyTracks.length; track++) {
      const el = propertyTracks[track];
      if (value >= +el.of && value <= +el.until) {
        registry = +el.emoluments;
        registry = registry.toFixed(2);
        break;
      }
    }

    var deed = 0;
    for (let track = 0; track < emolumentsAndTaxesTracks.length; track++) {
      const el = emolumentsAndTaxesTracks[track];
      if (value >= +el.of && value <= +el.until) {
        deed = +el.emoluments;
        deed += +el.taxes;
        deed = deed.toFixed(2);
        break;
      }
    }

    var total = 0;
    var tax = 'isento';
    if ($('#on').is(":checked") && value > +settings.maximumITBIExemption) {
      tax = (+settings.onerousDeed) * (+value) / 100;
    }
    if ($('#non').is(":checked") && value > +settings.maximumITCMDExemption) {
      tax = (+settings.nonOnerousDeed) * (+value) / 100;
    }
    if (tax != 'isento') {
      tax = tax.toFixed(2);
      total = +tax;
      tax = 'R$ ' + formatNumberWithTwoDecimals(tax);
    }

    total += +registry + +deed;
    total = total.toFixed(2);

    
    $('.value').html('R$ ' + $('#base').val());
    // $('.deed').html('R$ ' + maskVal(deed));
    $('.deed').html('R$ ' + formatNumberWithTwoDecimals(deed));
    $('.tax').html(tax);
    // $('.registry').html('R$ ' + maskVal(registry));
    $('.registry').html('R$ ' + formatNumberWithTwoDecimals(registry));
    // $('.total').html('R$ ' + maskVal(total));
    $('.total').html('R$ ' + formatNumberWithTwoDecimals(total));
    $('#calcform').hide();
    $('.result').show('slow');
  }

  function maskVal(val)
  {
    val = +val;
    return val.toLocaleString('pt-BR');
  }

  function sendEmail(btn)
  {
    if (!$("#send-mail-form").valid())
      return;

    $(btn).html('<div class="spinner"></div>');

    axios.post("{{ route('price.list.sendmail') }}", {
      value: $('.value').html(),
      deed: $('.deed').html(),
      tax: $('.tax').html(),
      registry: $('.registry').html(),
      total: $('.total').html(),
      name: $('#name').val(),
      email: $('#email').val()
    })
    .then(function (response) {
      $(btn).parent().hide();
      $('#send-mail-form').find('.success').show();
    })
    .catch(function (error) {
      $(btn).html('Enviar');
      $('#send-mail-form').find('.error').show();
    });
  }

  function newCalc()
  {
    $('#calcform').show('slow');
    $('.result').hide();

    $('#value').html('');
    $('#deed').html('');
    $('#tax').html('');
    $('#registry').html('');
    $('#total').html('');
    $('#base').val('');

    $('#send-mail-form').find('.message').hide();
    $('#send-mail-form').find('button').parent().show();
    $('#send-mail-form').find('button').html('Enviar');
  }
</script>
@endsection