@extends('layouts.admin')
@section('title', 'Areas de Atuação')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="fw-bold py-3 m-0">
    <span class="text-muted fw-light">Colaboradores / </span> Areas de atuação
  </h4>
</div>

<form class="card my-4" method="POST">
  @csrf
  <h5 class="card-header">Adicionar Area de atuação</h5>
  <div class="card-body">
    <div class="d-flex align-items-center">
      <div class="flex-grow-1 me-3">
        <label class="form-label">Nome</label>
        <input type="text" class="form-control @error('name') is-invalid @enderror" placeholder="Digite o nome" name="name" value="{{ old('name') }}">
      </div>
    </div>
    <button class="btn btn-primary mt-3">Adicionar</button>
  </div>
</form>

 <div class="card">
  <h5 class="card-header">Areas de atuação</h5>
  <div class="table-responsive text-nowrap">
    <table class="table table-hover">
      <thead>
        <tr>
          <th>Título</th>
          <th>Ações</th>
        </tr>
      </thead>
      <tbody class="table-border-bottom-0" id="sortable">
        @foreach ($occupations as $occupation)
          <tr>
            <td><strong>{{ $occupation->name }}</strong></td>
            <td>
              <div class="dropdown">
                <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown" aria-expanded="false"><i class="bx bx-dots-vertical-rounded"></i></button>
                <div class="dropdown-menu">
                  <a class="dropdown-item" href="{{ route('collaborators.occupations.edit', [$occupation->id]) }}"><i class="bx bx-edit-alt me-1"></i> Edit</a>
                  <form method="POST" action="{{ route('collaborators.occupations.destroy', [$occupation->id]) }}">
                    @csrf
                    @method('delete')
                    <button class="dropdown-item" href="javascript:void(0);" onclick="return confirm('Tem certeza disso? Essa ação deletará a categoria e removerá ela de todas as novidades')"><i class="bx bx-trash me-1"></i> Delete</button>
                  </form>
                </div>
              </div>
            </td>
          </tr>
        @endforeach
      </tbody>
    </table>
  </div>
</div>

  <script nonce="{{ csp_nonce() }}">
    $( "#sortable" ).sortable();

    $( ".accordion-button" ).on('click', function(){
      $(".accordion-collapse").collapse('hide');
    });
  </script>
@endsection