@extends('layouts.admin')
@section('title', 'Documentos')

@section('content')
<h4 class="fw-bold py-3 mb-4">
  <span class="text-muted fw-light">Documentos / </span> Ticket
</h4>

<div class="card mt-4">
  <div class="card-header">
    <h5>Ticket</h5>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="mb-3 col-sm-4">
        <label>Nome do responsável pela solicitação:</label>
        <input type="text" class="form-control" value="{{ $doc->client->name }}" readonly>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-sm-4">
        <label>Telefone:</label>
        <input type="text" class="form-control" value="{{ $doc->client->phone }}" readonly>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-sm-4">
        <label>E-mail:</label>
        <input type="text" class="form-control" value="{{ $doc->client->email }}" readonly>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-sm-4">
        <label>CPF:</label>
        <input type="text" class="form-control" value="{{ $doc->client->cpf }}" readonly>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-sm-4">
        <label>Tipo do ato notarial:</label>
        <input type="text" class="form-control" value="{{ $doc->document_type }}" readonly>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-sm-4">
        <label>Documentos:</label>
        @foreach (json_decode($doc->documents) as $document)
          <p style="margin:0px;">{{ $doc->fileSentAt($document) }} | {{ $document->file_name }} | <a href="{{ route('admin.documents.download', ['path' => $document->path, 'file_name' => $document->file_name]) }}">Download</a></p>
        @endforeach
          <h5 class="mt-3">
            <a href="{{ route('admin.documents.download.all', [$doc->id]) }}">Baixar todos documentos</a>
          </h5>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-sm-4">
        <label>Você é parte do ato:</label>
        <input type="text" class="form-control" value="@if (!$doc->is_part)Não @else Sim @endif" readonly>
      </div>
    </div>

    @if (!$doc->is_part)
      <div class="row">
        <div class="mb-3 col-sm-4">
          <label>Nome completo de uma das partes:</label>
          <input type="text" class="form-control" value="{{ $doc->part_name }}" readonly>
        </div>
      </div>

      <div class="row">
        <div class="mb-3 col-sm-4">
          <label>CPF da parte acima:</label>
          <input type="text" class="form-control" value="{{ $doc->part_cpf }}" readonly>
        </div>
      </div>
    @endif

    <div class="row">
      <div class="mb-3 col-sm-4">
        <label>Escrevente que lhe atendeu:</label>
        <input type="text" class="form-control" value="{{ $doc->clerk->name ?? 1 }}" readonly>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-sm-4">
        <label>Mensagem (opcional):</label>
        <textarea name="message" class="form-control" rows="5" readonly >{{ $doc->message }}</textarea>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-sm-4">
        <label>Status:</label>
        <input type="text" class="form-control" value="{{ ucfirst(__('messages.'.$doc->status)) }}" readonly>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-sm-4">
        @if ($doc->status == 'closed')
          <a href="{{ route('admin.documents.reopen', [$doc->id]) }}" class="btn btn-primary" id="cancel-button" style="margin-right: 16px;" onclick="return confirm('Tem certeza que deseja reabrir esse ticket?')">Reabrir Ticket</a>
          <a href="{{ route('admin.documents.drop', [$doc->id]) }}" class="btn btn-danger" id="cancel-button" onclick="return confirm('Tem certeza que deseja baixar esse ticket?')">Baixar Ticket</a>
        @endif
      </div>
    </div>
    <hr>
    <div class="row">
      <div class="mb-3 col-12">
        @foreach ($log as $item)
          <p class="mb-1">{{ \Carbon\Carbon::parse($item->date)->format('d/m/Y \à\s H:i:s') }} | {{ $item->message }}</p>
        @endforeach
      </div>
    </div>
  </div>
</div>

<br><br><br>

@endsection

<script nonce="{{ csp_nonce() }}">

</script>