@extends('layouts.my-account')

@section('content')
<section class="account txtcontent">
  <div class="container-fluid">
    <div class="px-1">
      <div class="breadcrumb">
        <a href="{{ route('my.services') }}">Meus Serviços</a>  >
        <a href="{{ route('documents.index') }}">Envio de documentos</a>  >
        <span>Novo Documento</span>
      </div>
      <div class="d-flex justify-content-between align-items-start">
        <h1>Novo Documento</h1>
        <a href="{{ route('documents.index') }}" class="btnDefault" style="background: #708082;">Voltar</a>
      </div>
    </div>
			<div class="row">
			<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">

				<p>Preencha o formulário para abrir um novo ticket enviando os documentos necessários ao ato solicitado.<br>O escrevente receberá uma notificação dos arquivos.</p>

        @if ($errors->any())
          <div style="
            margin-top: 24px;
            padding: 24px;
            margin-bottom: 1rem;
            border: 0 solid transparent;
            border-radius: 0.375rem;background-color: #fff2d6;
      border-color: #ffe6b3;
      color: #ffab00;width: 100%;font-size:15.5px;" role="alert">
              <ul>
                @foreach ($errors->all() as $error)
                  <li>{{ $error }}</li>
                @endforeach
              </ul>
            </div>
        @endif

				<form action="{{ route('documents.store') }}" method="POST" enctype="multipart/form-data">
          @csrf
          <div class="formControl">
						<label>Nome do responsável pela solicitação:</label>
						<input type="text" name="name" value="{{ old('name', $client->name) }}" required>
					</div>
          <div class="formControl">
						<label>Telefone:</label>
						<input type="text" name="phone" value="{{ old('phone', $client->phone) }}" required>
					</div>
          <div class="formControl">
						<label>E-mail:</label>
						<input type="text" name="email" value="{{ $client->email }}" readonly required>
					</div>
          <div class="formControl">
						<label>CPF:</label>
						<input type="text" name="cpf" value="{{ old('cpf', $client->cpf) }}" required>
					</div>
          <div class="formControl">
						<label>Selecione o ato notarial</label>
						<select name="doc_type" required>
              <option value="">Selecionar...</option>
              <option value="Alienação Fiduciária" @selected(old('doc_type') == 'Alienação Fiduciária')>Alienação Fiduciária</option>
              <option value="Apostila, Autenticação e Reconhecimento de firma " @selected(old('doc_type') == 'Apostila, Autenticação e Reconhecimento de firma ')>Apostila, Autenticação e Reconhecimento de firma </option>
              <option value="Ata Notarial em Geral" @selected(old('doc_type') == 'Ata Notarial em Geral')>Ata Notarial em Geral</option>
              <option value="Ata Notarial para Adjudicação Compulsória " @selected(old('doc_type') == 'Ata Notarial para Adjudicação Compulsória ')>Ata Notarial para Adjudicação Compulsória </option>
              <option value="Ata Notarial para Usucapião" @selected(old('doc_type') == 'Ata Notarial para Usucapião')>Ata Notarial para Usucapião</option>
              <option value="Carta de Sentença Notarial" @selected(old('doc_type') == 'Carta de Sentença Notarial')>Carta de Sentença Notarial</option>
              <option value="Certidões de Ato Notarial" @selected(old('doc_type') == 'Certidões de Ato Notarial')>Certidões de Ato Notarial</option>
              <option value="Certificado Digital e-Notariado" @selected(old('doc_type') == 'Certificado Digital e-Notariado')>Certificado Digital e-Notariado</option>
              <option value="Escritura de Compra e Venda" @selected(old('doc_type') == 'Escritura de Compra e Venda')>Escritura de Compra e Venda</option>
              <option value="Escritura de Compra e Venda com Alienação Fiduciária" @selected(old('doc_type') == 'Escritura de Compra e Venda com Alienação Fiduciária')>Escritura de Compra e Venda com Alienação Fiduciária</option>
              <option value="Escritura de Digital" @selected(old('doc_type') == 'Escritura de Digital')>Escritura de Digital</option>
              <option value="Escritura de Doação" @selected(old('doc_type') == 'Escritura de Doação')>Escritura de Doação</option>
              <option value="Escritura de Inventário e Partilha" @selected(old('doc_type') == 'Escritura de Inventário e Partilha')>Escritura de Inventário e Partilha</option>
              <option value="Escritura de Pacto Antenupcial" @selected(old('doc_type') == 'Escritura de Pacto Antenupcial')>Escritura de Pacto Antenupcial</option>
              <option value="Escritura de Permuta" @selected(old('doc_type') == 'Escritura de Permuta')>Escritura de Permuta</option>
              <option value="Escritura de Separação ou Divórcio" @selected(old('doc_type') == 'Escritura de Separação ou Divórcio')>Escritura de Separação ou Divórcio</option>
              <option value="Escritura de Testamento" @selected(old('doc_type') == 'Escritura de Testamento')>Escritura de Testamento</option>
              <option value="Escritura de União Estável" @selected(old('doc_type') == 'Escritura de União Estável')>Escritura de União Estável</option>
              <option value="Escritura Outras" @selected(old('doc_type') == 'Escritura Outras')>Escritura Outras</option>
              <option value="Financeiro" @selected(old('doc_type') == 'Financeiro')>Financeiro</option>
              <option value="Mensalistas" @selected(old('doc_type') == 'Mensalistas')>Mensalistas</option>
              {{--<option value="Testamento" @selected(old('doc_type') == 'Testamento')>Testamento</option>--}}
              <option value="Procuração Pública" @selected(old('doc_type') == 'Procuração Pública')>Procuração Pública</option>
              
						</select>
					</div>
					<div class="formControl">
						<label>Documentos (Tamanho máximo do envio 64MB):</label>
						<div style="border: 1px solid var(--cinza);padding:16px;margin-bottom:16px;">
              <input type="file" multiple onchange="filesUpload(event)" style="border:none;padding: 0;margin:0;" required> 
              <input id="documents" type="file" multiple name="documents[]" style="display: none!important">
              <div id="files-list" style="padding-top: 16px;">
                
              </div>
              <p><span id="files-total">0</span> MB enviados</p>
            </div>
					</div>
					<div class="formControl">
						<label>Você é parte do ato:</label>
						<select name="is_part" onchange="isPartChanged(this)" required>
              <option value="">Selecionar...</option>
							<option value="1" @selected(old('is_part') == '1')>Sim</option>
              <option value="0" @selected(old('is_part') == '0')>Não</option>
						</select>
					</div>
          <div class="formControl part_fields" id="part_name" style="display:none;">
						<label>Nome completo de uma das partes:</label>
						<input type="text" name="part_name" value="{{ old('part_name') }}">
					</div>
          <div class="formControl part_fields" id="part_cpf" style="display:none;">
						<label>CPF da parte acima:</label>
						<input type="text" name="part_cpf" value="{{ old('part_cpf') }}">
					</div>
          <div class="formControl">
						<label>Selecione quem lhe atende:</label>
						<select name="clerk">
							<option value="">Selecionar...</option>
              @foreach ($clerks as $clerk)
                <option value="{{ $clerk->id }}" @selected(old('clerk') == $clerk->id)>{{ $clerk->name }}</option>
              @endforeach
						</select>
					</div>
          <div class="formControl" id="message">
						<label>Mensagem (opcional)</label>
						<textarea name="message" rows="5">{{ old('message') }}</textarea>
					</div>
          <div class="formControl" style="margin-top: 24px;">
            <label>Você enviará mais arquivos?</label>
            <div class="typeRadio">
              <input type="radio" name="more_files" id="yes" value="1" @checked(old('more_files') == '1')>
              <label for="yes"><strong>Sim</strong> - o ticket ficará aberto, possibilitando o envio de documentos complementares.</label><br>
            </div>
            <div class="typeRadio">
              <input type="radio" id="no" name="more_files" value="0" @checked(old('more_files', '0') == '0')>
              <label for="no"><strong>Não</strong> - o ticket será fechado, impossibilitando o envio de documentos complementares.</label><br>
            </div>
          </div>
          <div class="formControl check checkLGPD mt-0">
            <input type="checkbox" required="">
            <label>Declaro que sou parte ou represento as partes interessadas neste pedido de ato notarial e me responsabilizo pela preservação da vida privada e pelos dados pessoais próprios ou de terceiros (CF, art. 5º, inciso X e Lei Geral de Proteção de Dados Pessoais - Lei n° 13.709/2018).</label>
          </div>

					<div class="d-flex">
						<button class="btnDefault">Enviar</button>
					</div>
				</form>
			</div>
		</div>
	</div>
</section>
@endsection

<script nonce="{{ csp_nonce() }}">

  var files = [];
  function filesUpload(e)
  {
    for (let i = 0; i < e.target.files.length; i++) {
      files.push(e.target.files[i]);
    }
    updateFilesList();
  }

  function updateFilesList()
  {
    var list = '';
    var documents = new DataTransfer();
    var total = 0;
    files.forEach((file, index) => {
      var mb = (file.size / 1024 / 1024).toFixed(2);
      var link = '<a href="#" onclick="removeFile(event, '+index+')">Remover</a>';
      list += '<p style="margin:0;">' + file.name + ' | ' + mb + ' MB' + ' | ' + link +'</p>';
      documents.items.add(file);
      total += file.size;
    });
    total = (total / 1024 / 1024).toFixed(2);
    if (total > 64) {
      $('#files-total').css('color', 'red');
    } else {
      $('#files-total').css('color', 'var(--cinza)');
    }
    $('#files-total').html(total);
    $('#files-list').html(list);

    document.getElementById('documents').files = documents.files;
  }

  function removeFile(e, index)
  {
    e.preventDefault();
    files.splice(index, 1);
    updateFilesList();
  }

  function isPartChanged(select)
  {
    if ($(select).val() == '0') {
      $('.part_fields').show();
    } else {
      $('.part_fields').hide();
    }
  }

</script>