<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
  <link rel="icon" type="image/x-icon" href="{{ asset('/img/favicon/favicon.png') }}" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0">

	<title>@hasSection('title') {{ ucfirst(strtolower($__env->yieldContent('title'))) }} - @endif 26º Tabelionato de Notas de São Paulo</title>

  <link rel="stylesheet" type="text/css" href="{{ asset('css/all.min.css') }}">
  <link rel="stylesheet" type="text/css" href="{{ asset('css/app.css') }}">
  <link rel="stylesheet" type="text/css" href="{{ asset('css/header.css') }}">
  <link rel="stylesheet" type="text/css" href="{{ asset('css/flexboxgrid.css') }}">
  <link rel="stylesheet" type="text/css" href="{{ asset('css/structure.css') }}">
  <link rel="stylesheet" type="text/css" href="{{ asset('css/custom26notas.css?v=1109') }}">


  <link rel="stylesheet" href="//code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
	<script src="https://code.jquery.com/jquery-3.6.4.min.js" integrity="sha256-oP6HI9z1XaZNBrJURtCoUT5SUnxFr8s3BzRl+cbzUq8=" crossorigin="anonymous"></script>
  <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.js"></script>
  <script src="{{ asset('js/all.min.js') }}"></script>

  {{-- <meta name="grecaptcha-key" content="{{config('recaptcha.v3.public_key')}}"> --}}
  {{-- <script src="https://www.google.com/recaptcha/api.js?render={{config('recaptcha.v3.public_key')}}"></script> --}}
  <script src="{{ asset('js/app.js') }}"></script>
  {!! htmlScriptTagJsApi() !!}
  <!-- Google Tag Manager -->
  <script nonce="mZJudfPa83iSTLGkL6JrwmJv1njPMaH4" >(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-NN47MK7');</script>
    <!-- End Google Tag Manager -->
</head>
<body class="{{ $finalOfUrl }}">
  <!-- Google Tag Manager (noscript) -->
  <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NN47MK7"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->

	<header class="header">

		<div class="container-fluid">

			<div class="subHeader">

				<ul class="menuAtalhos">
          <li><a href="#conteudo" accesskey="1">Ir para conteúdo [1]</a></li>
          <li><a href="/menu/servicos" accesskey="2">Ir para Serviços [2]</a></li>
					<li><a href="#footerContent" accesskey="3">Ir para rodapé [3]</a></li>
					<li><a href="/acessibilidade" accesskey="4">Acessibilidade [4]</a></li>
				</ul>

				<ul class="menuAtalhos">
					<li><a href="{{ route('questions') }}" title="Pergunte ao Tabelião">Pergunte ao Tabelião</a></li>
					<li><a href="{{ route('news') }}" title="Novidades">Novidades</a></li>
					<li><a href="https://www.26notas.com.br/blog/" title="Blog do 26">Blog do 26</a></li>
					<li><a href="{{ route('ouvidoria') }}" title="Ouvidoria">Ouvidoria</a></li>
				</ul>

			</div>

			<div class="contentHeader">

				<div class="logo">
					<a href="/">
						<img src="{{ asset('img/logo.svg') }}" alt="26º Tabelionato">
					</a>
				</div>


				@if (isset($menu) && $menu == 'my-account')
          <nav class="col-lg-8">
            <div class="menuHeader d-flex justify-content-between">
              <ul class="d-flex">
                <li><a href="{{ route('my.services') }}">Meus serviços</a></li>
                <li><a href="{{ route('my.acc') }}">Minha conta</a></li>
                <li><a class="red" href="{{ route('logout') }}">Sair</a></li>
              </ul>
              <ul>
                <li><a href="{{ route('index') }}">Acesso ao site</a></li>
              </ul>
            </div>
          </nav>
        @else
          <form action="{{ route('search') }}" class="searchForm">
            <input type="text" placeholder="Digite para pesquisar" name="txt" class="searchInput" value="{{ request()->txt }}" onkeyup="search()" onblur="closeResult()" autocomplete="off">
            <div class="result"><ul></ul></div>

            <input type="submit" class="cursor-p">
          </form>
          <nav class="col-lg-5">
            <ul id="menuTopo" class="menuHeader">
              @foreach ($menus as $menu)
                @if($menu->position == 'header')
                  @if (count($menu->submenus) > 0)
                    <li class="hasSubMenu">
                      <a href="{{ route('menu', [$menu->slug]) }}">{{ $menu->title }}</a>
                      <ul class="subMenu">
                        <li class="closeSubMenu"><span>Voltar</span></li>
                        @foreach ($menu->submenus as $submenu)
                          <li class="subMenuCol subMenuCol_{{ Str::slug(strtolower($submenu->title), '') }}">
                            <ul class="subMenuColItem">
                              <li class="subMenuColItemTitle"><a href="{{ route('submenu', [$submenu->slug]) }}">{{ $submenu->title }}</a></li>
                              @foreach ($submenu->links as $link)
                                @php
                                    $css = "";
                                    if(isset($link->color)){
                                        $css = "color-" . $link->color;
                                    }
                                    if(isset($link->bold) && $link->bold == 1){
                                        $css .= " strong";
                                    }
                                @endphp
                                @if ($link->type == 'link')
                                  <li class="subMenuColItemLinkFirst"><a href="{{ $link->link }}" @if($link->blank) target="_blank" @endif class="{{$css}}">{{ $link->title }}</a></li>
                                @else
                                  @if ($link->page->type == 'coded')
                                    <li class="subMenuColItemLink"><a href="/{{ $link->page->coded_endpoint }}" @if($link->blank) target="_blank" @endif  class="@if($link->page->restricted_access) private-page @endif {{$css}}">{{ $link->page->title }}</a></li>
                                  @elseif ($link->page->template == 'default' || $link->page->type == 'dynamic')
                                   <li><a href="/{{ $link->page->slug }}" @if($link->blank) target="_blank" @endif class="@if($link->page->restricted_access) private-page @endif {{$css}}">{{ $link->page->title }}</a></li>
                                  @else
                                    <li><a href="/{{ __('messages.slug.' . $link->page->template) }}/{{ $link->page->slug }}" @if($link->blank) target="_blank" @endif class="@if($link->page->restricted_access) private-page @endif {{$css}}">{{ $link->page->title }}</a></li>
                                  @endif
                                @endif
                              @endforeach
                            </ul>
                          </li>
                        @endforeach
                      </ul>
                    </li>
                  @else
                    <li><a href="/{{ $menu->slug }}">{{ $menu->title }}</a></li>
                  @endif
                @endif
              @endforeach

              <li class="menu-mobile"><a href="{{ route('institutional') }}">Quem Somos</a></li>
              <li class="menu-mobile"><a href="{{ route('questions') }}">Pergunte ao Tabelião</a></li>
              <li class="menu-mobile"><a href="{{ route('news') }}">Novidades</a></li>
              <li class="menu-mobile"><a href="https://www.26notas.com.br/blog/">Blog do 26</a></li>
              <li class="menu-mobile"><a href="{{ route('ouvidoria') }}">Ouvidoria</a></li>

              <li><a href="{{ route('login') }}" title="Cliente" id="areaCliente"><i class="padlock"></i> Cliente</a></li>
            </ul>
          </nav>
				@endif

				<button class="open-menu" onclick="document.querySelector('header').classList.toggle('active'); this.classList.toggle('opened');this.setAttribute('aria-expanded', this.classList.contains('opened'))" aria-label="Main Menu">
					<svg width="100" height="100" viewBox="0 0 100 100">
						<path class="line line1" d="M 20,29.000046 H 80.000231 C 80.000231,29.000046 94.498839,28.817352 94.532987,66.711331 94.543142,77.980673 90.966081,81.670246 85.259173,81.668997 79.552261,81.667751 75.000211,74.999942 75.000211,74.999942 L 25.000021,25.000058" />
						<path class="line line2" d="M 20,50 H 80" />
						<path class="line line3" d="M 20,70.999954 H 80.000231 C 80.000231,70.999954 94.498839,71.182648 94.532987,33.288669 94.543142,22.019327 90.966081,18.329754 85.259173,18.331003 79.552261,18.332249 75.000211,25.000058 75.000211,25.000058 L 25.000021,74.999942" />
					</svg>
				</button>

			</div>

		</div>

	</header>
	<div class="debug"></div>
