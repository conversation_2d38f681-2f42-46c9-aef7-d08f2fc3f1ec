@extends('layouts.admin')
@section('title', 'Dados do Cliente')

@section('content')
<style type="text/css">
    .btnReenviarBitrix{font-size: 0.8em; cursor:pointer; font-weight: normal; text-decoration: underline;}
</style>
<h4 class="fw-bold py-3 mb-4">
  <span class="text-muted fw-light">Clientes / </span> <b>Informações</b>
</h4>

<div class="card mt-4">
  <div class="card-header">
    <h5>Dados do Cliente</h5>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="mb-4 col-sm-4">
        <b>NOME/RAZÃO SOCIAL</b><br />
        {{$fields->nome ?? ''}}
      </div>
      <div class="mb-4 col-sm-6">
        <b>SERVIÇO</b><br />
        {{$form->page_title}}
      </div>
      <div class="mb-4 col-sm-2">
        <b>STATUS</b><br />
        @if($form->bitrix_status == "OK")
        <span class="txt_verde">Enviado ao Bitrix <i class="bx bx-check-double"></i></span>
        @elseif($form->bitrix_status != "")
            <span class="txt_vermelho">Erro no envio Bitrix <i class="bx bx-error-alt"></i></span><br />
            <form action="{{route('admin.formularios.reenviar_form_bitrix', $form->id)}}" method="POST">
                @csrf
                <span onclick="this.parentNode.submit();" class="btnReenviarBitrix">tentar enviar novamente</span>
            </form>
        @else
            -
        @endif
      </div>
    </div>

    <div class="row">
      <div class="mb-4 col-sm-4">
        <b>TELEFONE</b><br />
        {{$fields->telefone ?? ''}}
      </div>
      <div class="mb-2 col-sm-3">
        <b>E-MAIL</b><br />
        {{$fields->email ?? ''}}
      </div>
      <div class="mb-2 col-sm-3">
        <b>ESCREVENTE</b><br />
        @isset($fields->Escrevente)
            @php
                $escrevente = \App\Models\Collaborator::find($fields->Escrevente);
                if($escrevente)
                    echo $escrevente->name;
                else {
                    echo "-";
                }
            @endphp
        @else
                -
        @endisset
      </div>
      <div class="mb-2 col-sm-2">
        <b>ACEITA NEWSLETTER</b><br />
        @isset($fields->want_messages_checkbox)
            Sim
        @else
            Não
        @endif
      </div>
    </div>
    <br /><br />
    <div class="row">
      <div class="mb-3 col-sm-4">
        <b>Dados do formulário enviado dia {{ $form->created_at->format('d/m/Y') }}.</b>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-sm-4">
        @isset($fields->form)
            @php
                $fController = new \App\Http\Controllers\FormController;
            @endphp
                {!!nl2br($fController->retornaDadosFormulario($fields))!!}
        @endisset
      </div>
    </div>
    <div class="row">
        <div class="mb-3 col-sm-4">
            <form onsubmit="return confirm('Tem certeza de que deseja excluir esse formulário?');" action="{{route('admin.formularios.delete', $form->id)}}" method="POST">
                @csrf
                <button class="btn btn-danger">Excluir</button>
            </form>
        </div>
    </div>
    <br /><br /><br />
    <div class="row">
        <div class="mb-4 col-sm-4 txt_verde">
          <b>Outras @isset($outrosForms) {{count($outrosForms)}} @else 0
          @endisset solicitações do cliente via formulário de serviços:</b>
        </div>
    </div>
    <div class="row">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                <tr>
                    <th>NOME</th>
                    <th>CPF/CNPJ</th>
                    <th>ESCREVENTE</th>
                    <th>DATA</th>
                    <th>SERVIÇO</th>
                    <th>BITRIX</th>
                    <th>AÇÃO</th>
                </tr>
                </thead>
                <tbody class="table-border-bottom-0">
                    @isset($outrosForms)
                        @foreach ($outrosForms as $form)
                            <tr>
                                <td>{{ json_decode($form->fields)->nome ?? json_decode($form->fields)->Nome ??  json_decode($form->fields)->Nome_Completo ?? '' }}</td>
                                <td>{{ json_decode($form->fields)->CPF ?? json_decode($form->fields)->CNPJ ?? '' }}</td>
                                <td>
                                @isset(json_decode($form->fields)->Escrevente)
                                    @php
                                        $escrevente = \App\Models\Collaborator::find(json_decode($form->fields)->Escrevente);
                                        if($escrevente)
                                            echo $escrevente->name;
                                        else {
                                            echo "-";
                                        }
                                    @endphp
                                @else
                                        -
                                @endisset
                                </td>
                                <td>{{ $form->created_at->format('d/m/Y H:i') }}</td>
                                <td>{{ $form->page_title }}</td>
                                <td>
                                @if($form->bitrix_status == "OK")
                                    <i class="bx bx-check-double" title="Enviado com sucesso"></i>
                                @elseif($form->bitrix_status != "")
                                    <i class="bx bx-error-alt txt_vermelho" title="Erro no envio"></i>
                                @else
                                    -
                                @endif
                            <td>
                            <a target="_blank" href="{{route('admin.formularios.details', $form->id)}}"><i class="bx bx-show me-1"></i></a>
                            </td>
                        </tr>
                        @endforeach
                    @endisset
                </tbody>
            </table>
        </div>
    </div>
  </div>
</div>

<br><br><br>

@endsection
