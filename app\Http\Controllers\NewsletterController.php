<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Newsletter;

class NewsletterController extends Controller
{
  public function index(Request $r)
  {
    try{
        $name = $r['nome'] ?? '';
        $nl = new Newsletter;
        $nl->name = $name;
        $nl->email = $r['email'];
        $nl->save();
    } catch (\Exception $e) {
    }



    // $url = "https://www.26notas.com.br/blog/newslettermailpoet/addpoetnewsletter.php";

    // $curl = curl_init($url);
    // curl_setopt($curl, CURLOPT_URL, $url);
    // curl_setopt($curl, CURLOPT_POST, true);
    // curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);

    // $headers = array(
    //   "Content-Type: application/x-www-form-urlencoded",
    // );
    // curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

    // $name = $r['nome'] ?? '';
    // $data = "email=" . $r['email'] . "&name=" . $name;

    // curl_setopt($curl, CURLOPT_POSTFIELDS, $data);

    // //for debug only!
    // //curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    // //curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

    // $resp = curl_exec($curl);
    // curl_close($curl);
  }
}
