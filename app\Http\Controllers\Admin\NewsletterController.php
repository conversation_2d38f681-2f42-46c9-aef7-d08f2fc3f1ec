<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Newsletter;
use App\Exports\NewsletterExport;
use Maatwebsite\Excel\Facades\Excel;

class NewsletterController extends Controller
{
  public function index(Request $r)
  {
    $news = Newsletter::where('name', 'like', "%$r->busca%")->orWhere('email', 'like', "%$r->busca%")->paginate(30);

    return view('admin.newsletter.index', ['news' => $news]);
  }

  public function delete($id, Request $r){
    $news = Newsletter::find($id);
    if($news != null){
        $news->delete();
    }
    return redirect('admin/newsletter?page=' . $r->query('page'))->with('success', 'E-mail excluído com sucesso!');
  }

  public function export(Request $r){
    if(\Auth::guard('admin')->user()->type == 'admin'){
        $news = Newsletter::where('name', 'like', "%$r->busca%")->orWhere('email', 'like', "%$r->busca%");
        $now = now()->format('Y-m-d_H-i:s');
        return Excel::download(new NewsletterExport($news->get()), "newsletters_$now.xls");
    }
  }
}
