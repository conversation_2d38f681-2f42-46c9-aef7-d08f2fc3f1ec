@extends('layouts.my-account')

@section('content')
<section class="account txtcontent">

	<div class="container-fluid">
    <div class="px-1">
      <div class="breadcrumb">
        <a href="{{ route('my.services') }}">Meus Serviç<PERSON></a>  >
        <span>Push26 - Visualizar Registros</span>
      </div>
      <div class="d-flex justify-content-between align-items-start">
        <h1>Push26 - Visualizar Registros</h1>
        <a href="{{ route('my.services') }}" class="btnDefault" style="background: #708082;">Voltar</a>
      </div>
    </div>
		<div class="row">
      <div class="resultadoBusca">
        <div class="headBusca">
          <h2>Push26 - Notificações do Registro Imobiliário</h2>
        </div>
        <div class="row d-flex justify-content-between">

          @if (count($registers) > 0)
            <div class="infoBusca col-lg-6 p-0">
              <p>O e-mail {{ $registers[0]['email'] }} está cadastrado para receber as notificações abaixo.
                Se deseja remover a notificação do ato, clique em excluir.</p>
            </div>
          @else
            <div class="infoBusca col-lg-6 p-0">
              <p>Você ainda não se cadastrou para receber notificação.</p>
            </div>
          @endif
        </div>

        <div class="registrosResultados active">

          <h3>Imóveis</h3>
          <table>
            <tbody>
              <tr class="head">
                <th>Protocolo</th>
                <th>Registros</th>
                <th>Documento</th>
                <th>Ação</th>
              </tr>
              {{-- ORIGINAL
              @foreach ($registers as $register)
                <tr>
                  <td>
                    <form action="{{ route('queries.notarial.act') }}">
                      @php
                        $proto = $register->protocol;
                        if (substr($register->protocol, 0, 2) == '00') {
                          $proto = substr($register->protocol, 2);
                        }
                        // Usar explode para dividir a string no caractere '|'
                        $protocolo_parts = explode('|', $proto);
                        $proto = $protocolo_parts[0]; // primeira parte
                        $protocolo_cod_interno = $protocolo_parts[1]; // segunda parte
                      @endphp
                      <input type="hidden" name="type" value="{{ $register->doc_type }}">
                      <input type="hidden" name="doc" value="{{ $register->doc }}">
                      <input type="hidden" name="proto" value="{{ $proto }}">
                      {{--
                        <button style="text-decoration: underline; background:transparent; border:none; cursor:pointer;color: var(--cinza);">{{ $register->protocol }}</button>

                      <button style="text-decoration: underline; background:transparent; border:none; cursor:pointer;color: var(--cinza);">{{ $proto }}</button>
                    </form>

                  </td>
                  <td style="max-width: 400px;">{{ $register->registry }} >>>>>>> {{ $protocolo_cod_interno }} <<<<<<<</td>
                  <td>{{ $register->doc }}</td>
                  <td><a onclick="unfollowProtocol('{{ $register->protocol }}', this)" style="text-decoration: underline;">Remover</a></td>
                </tr>
              @endforeach
              FIM ORIGINAL --}}

              @foreach ($registers as $key => $register)
              <tr>
                <td>
                  @php
                      $proto = $register->protocol;
                      if (substr($register->protocol, 0, 2) == '00') {
                        $proto = substr($register->protocol, 2);
                      }
                      // Usar explode para dividir a string no caractere '|'
                      $protocolo_parts = explode('|', $proto);
                      $proto = $protocolo_parts[0]; // primeira parte
                      $protocolo_cod_interno = $protocolo_parts[1]; // segunda parte

                      // Verificar se o protocolo já foi exibido
                      $protocoloExibido = isset($exibidos[$proto]);

                      // Elimina pontuação do documetno  CPF/CNPJ
                      $documento = preg_replace("/[^0-9]/", "", $register->doc);

                      // Se ainda não foi exibido, marcar como exibido e mostrar os botões
                      if (!$protocoloExibido) {
                        $exibidos[$proto] = true;
                      }
                    @endphp

                    @if (!$protocoloExibido)
                      <form action="{{ route('queries.notarial.act') }}">
                          <input type="hidden" name="type" value="{{ $register->doc_type }}">
                          <input type="hidden" name="doc" value="{{ $documento }}">
                          <input type="hidden" name="proto" value="{{ $proto }}">
                          <button style="text-decoration: underline; background:transparent; border:none; cursor:pointer;color: var(--cinza);">{{ $proto }}</button>
                      </form>
                    @else
                      <span style="padding-left: 20px">&#10551;</span>
                    @endif
                </td>
                <td style="max-width: 400px;">
                  {{ $register->registry }} <span style="display: none">{{ $protocolo_cod_interno }}</span>
                </td>
                <td>@if (!$protocoloExibido) {{ $register->doc }} @endif</td>
                <td>
                  @if (!$protocoloExibido)
                    <a onclick="unfollowProtocol('{{ $register->protocol }}', this)" style="text-decoration: underline;">Remover</a>
                  @endif
                </td>
              </tr>
            @endforeach


            </tbody>
          </table>

        </div>
      </div>

		</div>

	</div>

</section>
<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script nonce="{{ csp_nonce() }}">
  function unfollowProtocol(protocol, column) {

    if (!confirm('Tem certeza que deseja deixar de acompanhar esse ato?'))
      return;

    axios.post("{{ route('push26.unfollow.protocol') }}", {
      protocol: protocol
    })
      .then((r) => {
        $(column).parents('tr').remove();
        // Recarregar a página
      window.location.reload();
      });
  }
</script>
@endsection
