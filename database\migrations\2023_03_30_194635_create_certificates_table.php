<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
      Schema::create('certificates', function (Blueprint $table) {
          $table->id();
          $table->text('partes')->nullable();
          $table->string('livro')->nullable();
          $table->string('folhas')->nullable();
          $table->string('tipo_solicitante')->nullable();
          $table->string('cpf')->nullable();
          $table->string('cnpj')->nullable();
          $table->string('nome_solicitante')->nullable();
          $table->string('email')->nullable();
          $table->string('telefone_solicitante')->nullable();
          $table->string('forma_entrega')->nullable();
          $table->string('tipo_certidao')->nullable();
          $table->string('cep')->nullable();
          $table->string('logradouro')->nullable();
          $table->string('numero')->nullable();
          $table->string('complemento')->nullable();
          $table->string('bairro')->nullable();
          $table->string('cidade')->nullable();
          $table->string('uf')->nullable();
          $table->string('valor')->nullable();
          $table->string('valor_sedex')->nullable();
          $table->string('custo_midia')->nullable();
          $table->timestamps();
      });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('certificates');
    }
};
