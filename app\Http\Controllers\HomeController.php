<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\News;
use App\Models\Page;
use Illuminate\Support\Facades\DB;

class HomeController extends Controller
{
  public function index()
  {
    $page = Page::where([
      'type' => 'dynamic',
      'dynamic_page' => 'home'
    ])->first();
    $settings = json_decode($page->dynamic_page_settings);
    $news = News::published()->take(4)->orderBy('created_at', 'desc')->get();

    $settings->block1 = array_filter($settings->block1);
    $orderb1 = implode(',', $settings->block1);
    if ($orderb1 != '') {
      $settings->block1 = Page::published()->whereIn('id', $settings->block1)->orderByRaw("FIELD(id, $orderb1)")
      ->get();
    }

    $settings->block2 = array_filter($settings->block2);
    $orderb2 = implode(',', $settings->block2);
    if ($orderb2 != '') {
      $settings->block2 = Page::published()->whereIn('id', $settings->block2)->orderByRaw("FIELD(id, $orderb2)")
      ->get();
    }

    return view('home', [
      'news' => $news,
      'page' => $page,
      'settings' => $settings
    ]);
  }

  public function search(Request $r)
  {
    if (empty($r->txt))
      return redirect()->back();

      $search = '%' . $r['txt'] . '%';

      $news = News::selectRaw('title, "news" as type, NULL, slug, NULL, NULL')
                    ->published()
                    ->where(function($query) use ($search) {
                      $query->where('content', 'like', $search)
                          ->orWhere('title', 'like', $search);
                    });

      $pages = Page::select('title', 'type', 'template', 'slug', 'coded_endpoint', 'restricted_access')
                      ->published()
                      ->where(function($query) use ($search) {
                        $query->where('title', 'like', $search)
                            ->orWhere('description', 'like', $search)
                            ->orWhere('dynamic_page_settings', 'like', $search)
                            ->orWhere('template_settings', 'like', $search)
                            ->orWhere('coded_endpoint', 'like', $search);
                    });


      $results = $pages->union($news)
                      ->orderBy('title');



      if (!empty($r->limit)) {
        $results = $results->take($r->limit)->get();
        $results = $this->getResultsFinalLink($results);
        return response()->json($results);
      } else {
        $results = $results->paginate(30);
        $results = $this->getResultsFinalLink($results);
        return view('search', [
          'results' => $results
        ]);
      }
  }

  private function getResultsFinalLink($results)
  {
    foreach ($results as $result) {
      if ($result->type == 'dynamic' || $result->template == 'default') {
        $result['final_link'] = "/$result->slug";
        $result['label'] = 'Página';
      } else if ($result->type == 'news') {
        $result['final_link'] = route('news.show', [$result->slug]);
        $result['label'] = 'Novidades';
      } elseif ($result->type == 'coded') {
        $result['final_link'] = "/$result->coded_endpoint";
        $result['label'] = 'Página';
      } else {
        $template = __('messages.slug.' . $result->template);
        $result['final_link'] = "/$template/$result->slug";
        $result['label'] = ucfirst(__('messages.' . $result->template));
      }
    }

    return $results;
  }

  public function services(Request $r)
  {
    $search = '%' . $r['txt'] . '%';
    $page = Page::select('title', 'slug')->published()->where([
      ['title', 'like', $search],
      ['template', '=', 'services']
    ])->get();

    return response()->json($page);
  }
}
