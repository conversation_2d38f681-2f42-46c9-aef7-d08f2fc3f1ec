@extends('layouts.my-account')

@section('content')
<section class="account txtcontent">
  <div class="container-fluid">
    <div class="px-1">
      <div class="breadcrumb">
        <a href="{{ route('my.services') }}">Meus Serviços</a>  >
        <a href="{{ route('documents.index') }}">Envio de documentos</a>  >
        <span>Documento</span>
      </div>
      <div class="d-flex justify-content-between align-items-start">
        <h1>Documento</h1>
        <a href="{{ route('documents.index') }}" class="btnDefault" style="background: #708082;">Voltar</a>
      </div>
    </div>
			<div class="row">
			<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">

				<p>Preencha o formulário para abrir um novo ticket enviando os documentos necessários ao ato solicitado.<br>O escrevente receberá uma notificação dos arquivos.</p>

        @if ($errors->any())
          <div style="
            margin-top: 24px;
            padding: 24px;
            margin-bottom: 1rem;
            border: 0 solid transparent;
            border-radius: 0.375rem;background-color: #fff2d6;
      border-color: #ffe6b3;
      color: #ffab00;width: 100%;font-size:15.5px;" role="alert">
              <ul>
                @foreach ($errors->all() as $error)
                  <li>{{ $error }}</li>
                @endforeach
              </ul>
            </div>
        @endif

				<form action="{{ route('documents.update', ['id' => $doc->id]) }}" method="POST" enctype="multipart/form-data">
          @csrf
          <div class="formControl">
						<label>Nome do responsável pela solicitação:</label>
						<input type="text" name="name" value="{{ $doc->client->name }}" readonly>
					</div>
          <div class="formControl">
						<label>Telefone:</label>
						<input type="text" name="phone" value="{{ $doc->client->phone }}" readonly>
					</div>
          <div class="formControl">
						<label>E-mail:</label>
						<input type="text" name="email" value="{{ $doc->client->email }}" readonly>
					</div>
          <div class="formControl">
						<label>CPF:</label>
						<input type="text" name="cpf" value="{{ $doc->client->cpf }}" readonly>
					</div>
          <div class="formControl">
						<label>Tipo do ato notarial:</label>
            <input type="text" value="{{ $doc->document_type }}" readonly>
					</div>
					<div class="formControl">
						<label>Documentos (Tamanho máximo do envio {{$remainingMB}}MB):</label>
           
						<div style="border: 1px solid var(--cinza);padding:16px;margin-bottom:16px;">
              @if($doc->status == 'opened') 
                <input type="file" multiple onchange="filesUpload(event)" style="border:none;padding: 0;margin:0;" > 
                <input id="documents" type="file" multiple name="documents[]" style="display: none!important">
              @endif
              <div style="padding-top: 16px;">
                @foreach (json_decode($doc->documents) as $document)
                  <p style="margin:0px;">{{ $doc->fileSentAt($document) }} | {{ $document->file_name }} | <a href="{{ route('documents.download', ['path' => $document->path, 'file_name' => $document->file_name]) }}">Download</a></p>
                @endforeach
              </div>
              @if($doc->status == 'opened') 
                <hr>
                <div id="files-list">
                  
                </div>
                <p><span id="files-total">0</span> MB enviados</p>
              @endif
            </div>
					</div>
					<div class="formControl">
						<label>Você é parte do ato:</label>
            <input type="text" value="@if($doc->is_part) Sim @else Não @endif" readonly>
					</div>
          @if(!$doc->is_part)
            <div class="formControl part_fields" id="part_name">
              <label>Nome completo de uma das partes:</label>
              <input type="text" name="part_name" value="{{ $doc->part_name }}" readonly>
            </div>
            <div class="formControl part_fields" id="part_cpf">
              <label>CPF da parte acima:</label>
              <input type="text" name="part_cpf" value="{{ $doc->part_cpf }}" readonly>
            </div>
          @endif
          <div class="formControl">
						<label>Selecione quem lhe atende:</label>
            <input type="text" value="{{ $doc->clerk->name ?? '' }}" readonly>
					</div>
          <div class="formControl" id="message">
						<label>Mensagem (opcional)</label>
						<textarea name="message" rows="5" readonly style="background-color: #eeeeee;">{{ $doc->message }}</textarea>
					</div>
          <div class="formControl" style="margin-top: 24px;">
            <label>Você enviará mais arquivos?</label>
            <div class="typeRadio">
              <input type="radio" name="more_files" id="yes" value="1" @checked($doc->status == 'opened') @disabled($doc->status != 'opened')>
              <label for="yes">Sim</label><br>
            </div>
            <div class="typeRadio">
              <input type="radio" id="no" name="more_files" value="0" @checked($doc->status != 'opened') @disabled($doc->status != 'opened')>
              <label for="no">Não</label><br>
            </div>
          </div>
          <div class="formControl check checkLGPD mt-0">
            <input type="checkbox" required="" @if($doc->status != 'opened') checked onclick="return false;"  @endif>
            <label>Declaro que sou parte ou represento as partes interessadas neste pedido de ato notarial e me responsabilizo pela preservação da vida privada e pelos dados pessoais próprios ou de terceiros (CF, art. 5º, inciso X e Lei Geral de Proteção de Dados Pessoais - Lei n° 13.709/2018).</label>
          </div>

					<div class="d-flex">
            @if($doc->status == 'opened')
						  <button class="btnDefault"  >Enviar</button>
              <a href="{{ route('documents.cancel', [$doc->id]) }}" class="btnDefault red" id="cancel-button" style="margin-left: 16px;">Cancelar Protocolo</a>
            @endif
					</div>
				</form>
			</div>
		</div>
	</div>
</section>
@endsection

<script nonce="{{ csp_nonce() }}">

  var files = [];
  var remainingMB = '{{ $remainingMB }}';
  function filesUpload(e)
  {
    for (let i = 0; i < e.target.files.length; i++) {
      files.push(e.target.files[i]);
    }
    updateFilesList();
  }

  function updateFilesList()
  {
    var list = '';
    var documents = new DataTransfer();
    var total = 0;
    files.forEach((file, index) => {
      var mb = (file.size / 1024 / 1024).toFixed(2);
      var link = '<a href="#" onclick="removeFile(event, '+index+')">Remover</a>';
      list += '<p style="margin:0px;">' + file.name + ' | ' + mb + ' MB' + ' | ' + link +'</p>';
      documents.items.add(file);
      total += file.size;
    });
    total = (total / 1024 / 1024).toFixed(2);
    if (total > remainingMB) {
      $('#files-total').css('color', 'red');
    } else {
      $('#files-total').css('color', 'var(--cinza)');
    }
    $('#files-total').html(total);
    $('#files-list').html(list);

    document.getElementById('documents').files = documents.files;
  }

  function removeFile(e, index)
  {
    e.preventDefault();
    files.splice(index, 1);
    updateFilesList();
  }

  function loading()
  {
    var loading = '<div class="spinner"></div>';
    $('#send-button').html(loading);
  }

  function isPartChanged(select)
  {
    if ($(select).val() == '0') {
      $('.part_fields').show();
    } else {
      $('.part_fields').hide();
    }
  }

</script>