<?php
namespace App\Services\Financas;

use App\Entities\Financeiro\Boleto;
use App\Services\Bradesco\Comprador;
use App\Services\Bradesco\Endereco;
use App\Services\Bradesco\Pagamento;
use App\Services\Financas\Exception\BoletoException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;


class BoletoService {
    const DEFAULT_ERROR_MESSAGE = "Erro ao emitir boleto";
    const DEFAULT_CLIENT_ERROR_CODE = 422;
    private $data;
    private $errors;
    private $validValues;
    private $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * @param $input
     * @return $this
     * @throws BoletoException
     */
    public function setData($input) {
        if (count($input) === 0) {
            $this->setErrors(['valores_vazios' => 'É preciso informar o valor dos campos para a emissão de um boleto']);
            throw new BoletoException(
                self::DEFAULT_ERROR_MESSAGE,
                self::DEFAULT_CLIENT_ERROR_CODE,
                null,
                $this->errors
            );
        }

        $this->data = $input;

        $this->validateValues();
        if (!$this->validValues) {
            $this->setErrors(['valores_inconsistentes' => 'A soma dos valores individuais é diferente do total informado']);
            throw new BoletoException(
                self::DEFAULT_ERROR_MESSAGE,
                self::DEFAULT_CLIENT_ERROR_CODE,
                null,
                $this->errors
            );
        }

        return $this;
    }

    /**
     * @return \stdClass
     * @throws BoletoException
     */
    public function emiteBoleto() {
        try {
            DB::beginTransaction();
            $this->data['data_emissao_boleto'] = Carbon::now();

            $boletoModel = Boleto::create($this->data);
            $endereco = new Endereco();
            $endereco->setCep($boletoModel->cep);
            $endereco->setLogradouro($boletoModel->logradouro);
            $endereco->setNumero($boletoModel->numero);
            $endereco->setBairro($boletoModel->bairro);
            $endereco->setCidade($boletoModel->cidade);
            $endereco->setComplemento($boletoModel->complemento);
            $endereco->setUf($boletoModel->uf);
            $comprador = new Comprador($this->getRequest(), $endereco);
            $comprador->setDocumento($boletoModel->documento);
            $comprador->setNome($boletoModel->nome_razao_social);

            $subtotal =  ($boletoModel->custas_integrais + $boletoModel->outros_registros);

            if($boletoModel->cobrar_deposito_previo) {
                $total = $subtotal + $boletoModel->deposito_previo;
            } else if($subtotal <= $boletoModel->deposito_previo) {
                $total = 0;
                var_dump(2);
            } else {
                $total = $subtotal - $boletoModel->deposito_previo;
            }

            $valor = $total + 245;

            $boletoEmitido = Pagamento::generateBoletoRequest(
                'ca'.$boletoModel->id . '' . $boletoModel->protocolo,
                $valor,
                $comprador,
                null,
                null,
                null,
                $boletoModel->protocolo
            );

            $boletoModel->data_emissao_boleto = $boletoEmitido->data_emissao;
            $boletoModel->linha_digitavel_boleto = $boletoEmitido->linha;
            $boletoModel->url = $boletoEmitido->url;

            $boletoModel->save();
            DB::commit();

            return $boletoEmitido;
        } catch (\Exception $e) {
            DB::rollback();
            $this->setErrors(['store' => "Erro ao salvar dados do boleto"]);
            Log::error($e->getMessage() . ' ' . $e->getTraceAsString());
            throw new BoletoException("Erro ao salvar dados do boleto", 510, $e, $this->errors);
        }
    }

    private function validateValues() {
        $depositoPrevio = $this->data['deposito_previo'];
        $custasIntegrais = $this->data['custas_integrais'];
        $outros = $this->data['outros_registros'];
        $saldo = $this->data['saldo'];

        $subTotal = ((float) $custasIntegrais + (float) $outros);

        if($this->data['cobrar_deposito_previo']) {
            $totalValues = $subTotal + (float) $depositoPrevio;
        } else if ($subTotal <= (float) $depositoPrevio) {
            $totalValues = 0;
        } else {
            $totalValues = (float) $subTotal - $depositoPrevio;
        }

        $this->validValues = round($totalValues, 2) === round((float) $saldo, 2);
    }

    private function setErrors($errorsArray) {
        $this->errors = (object) $errorsArray;
    }

    private function getRequest() {
        return $this->request;
    }

    public static function getTotal(Boleto $boleto) {
        $depositoPrevio = $boleto->deposito_previo / 100;
        $subtotal = ($boleto->custas_integrais/100 + $boleto->outros_registros/100);
        if($boleto->cobrar_deposito_previo) {
            $total = $subtotal + $depositoPrevio;
        } else if ($subtotal <= $depositoPrevio) {
            $total = 0;
        } else {
            $total = $subtotal - $depositoPrevio;
        }

        return $total;
    }

    public static function enviaEmail(Boleto $boleto, $email, $linha, $anexo)
    {
        if(empty($linha) && empty($anexo)) {
            throw new \Exception('É preciso informar o parâmetro linha e/ou o parâmetro anexo');
        }

        $attachment = null;
        $anexo = $anexo === 'true';

        if($anexo) {
            $attachment = self::getAttachment($boleto);
        }

        $boleto->total = self::getTotal($boleto);

        \Mail::send('emails.financeiro.boleto', ['boleto' => $boleto->toArray(),  'linha' => $linha === 'true', 'anexo' => $anexo === 'true'], function($message) use ($email, $anexo, $attachment) {
            $message->to($email, $email)->subject('Boleto para pagamento de Ato no 26º Tabelionato');
            if($anexo) {
                $message->attach($attachment);
            }
        });
    }

    private static function getAttachment(Boleto $boleto) {
        $fileName = self::getBoletoFileNameFromUrl($boleto->url);
        $filePathAndName = storage_path('boletos-atos/'.$fileName . '.pdf');

        if(!File::exists($filePathAndName) || self::conditionToRedownloadBoleto($filePathAndName)) {
            copy($boleto->url, $filePathAndName);
        }

        return $filePathAndName;
    }

    private static function getBoletoFileNameFromUrl($url)
    {
        $urlArray = explode('=', $url);
        return end($urlArray);
    }

    private static function conditionToRedownloadBoleto($filePathAndName)
    {
        return (time() - File::lastModified($filePathAndName) / env('TTL_BOLETO', 172800) > .8);
    }
}
