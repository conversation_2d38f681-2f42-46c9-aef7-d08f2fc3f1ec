<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Sysmeta;

class SysmetaController extends Controller
{
    public function updateCertificateSettings(Request $r)
    {
      Sysmeta::where('meta_key', 'certificate_settings')->update([
        'meta_value' => json_encode($r->except('_token'))
      ]);

      return redirect()->back()->with('success', 'Configurações de certidão salvas com sucesso!');
    }

    public function updateEmailTrasladoSettings(Request $r){
        Sysmeta::where('meta_key', 'mail_traslado_settings')->update([
            'meta_value' => json_encode($r->except('_token'))
        ]);
      return redirect()->back()->with('success', 'Configurações do e-mail traslado salvas com sucesso!');
    }
}
