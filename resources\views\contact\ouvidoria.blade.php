@extends('layouts.app')
@section('title', 'Ouvidoria')

@section('content')
<section class="contentServico txtcontent">

	<div class="container-fluid">
		<div class="row">
			<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12">
        <h1>Ouvidoria</h1>
        <p>
          Para enviar uma informação, informe os dados solicitados.<br>
          Sua solicitação será respondida em até 48 horas.<br>
          Dúvidas sobre serviços utilize o formulário próprio, <a href="/menu/servicos" title="Serviços">acesse</a>.
        </p>

        <form class="includePage" id="ouvidoria-form" action="{{ route('forms.send') . '#anchor' }}" method="POST" enctype="multipart/form-data" style="position: relative" onsubmit="sendForm(event)">
          @csrf
          <input type="hidden" name="Página" value="Ouvidoria">
          <input type="hidden" name="recipients" value="<EMAIL>">
          <input type="hidden" name="email_template" value="thank-you-email-to-user">
          <input type="hidden" name="form" value="ouvidoria">
          <input type="hidden" name="bitrix" value="28">
          <input type="hidden" value="Ouvidoria" name="page_title">

          <div class="formControl">
            <label>Tipo do assunto:</label>
            <select name="Tipo do Assunto" required>
              <option value="">Selecione...</option>
              <option value="Crítica">Crítica</option>
              <option value="Denúncia">Denúncia</option>
              <option value="Dúvida">Dúvida</option>
              <option value="Denúncia">Fale com o Tabelião</option>
              <option value="Sugestão">Sugestão</option>
              <option value="Trabalhe conosco">Trabalhe conosco</option>
              <option value="Outros">Outros</option>
            </select>
          </div>

          <div class="formControl">
            <label>Título</label>
            <input type="text" name="título" required>
          </div>

          <div class="formControl">
            <label>Nome Completo</label>
            <input type="text" id="name" name="nome" required>
          </div>

          <div class="formControl">
            <label>Telefone com DDD</label>
            <input type="text" name="telefone" required>
          </div>

          <div class="formControl">
            <label>E-mail</label>
            <input type="email" name="email" required>
          </div>

          <div class="formControl file">
            <label>Envio de documento</label>
            <input type="file" name="file[]" style="color:black;" multiple>
          </div>

          <div class="formControl">
            <label>Mensagem</label>
            <textarea name="mensagem" id="" cols="30" rows="10" required></textarea>
          </div>

          <div class="formControl check mb-1">
            <input type="checkbox" id="accordingly_checkbox" name="accordingly_checkbox" required>
            <label for="accordingly_checkbox">Li, estou de acordo com a <a href="/politica-de-privacidade" target="_blank">política de privacidade</a></label>
          </div>
          <div class="formControl check mt-1">
            <input type="checkbox" id="want_messages_checkbox" name="want_messages_checkbox">
            <label for="want_messages_checkbox">Desejo receber mensagens informativas do 26º Tabelionato de Notas.</label>
          </div>
          <div class="reCaptcha">
            {!! htmlFormSnippet() !!}
          </div>
          <div class="formControl">
            <button class="btnDefault red">Enviar</button>
          </div>

          <div id="anchor"></div>
          @if(session('form_success'))
            <div class="message success">
              <p>Formulário enviado com sucesso!</p>
              <p>Obrigado por usar os serviços do 26.</p>
              <p>Em breve faremos contato para tratar a sua solicitação.</p>
            </div>
          @endif

          @if(session('form_error') || $errors->any())
            <div class="message error">
              <p>Formulário não enviado.</p>
              @if ($errors->any())
                <ul class="mt-2">
                  @foreach ($errors->all() as $error)
                    <li style="color:#a94442;">{{ $error }}</li>
                  @endforeach
                </ul>
              @endif
            </div>
          @endif
        </form>
			</div>

      <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12">
				<div class="boxWidgetSidebar boxWidgetSidebarFormContato">
					<p class="red"><strong>Posso ajudar?</strong></p>

					{{--
            <p>
              <a href="{{ route('contact.us') }}">Fale Conosco</a><br>
					    <a href="{{ route('ouvidoria') }}" class="current">Ouvidoria</a><br>
					    <a href="{{ route('sac') }}">SAC</a><br>
            </p>
          --}}

          <div class="row">
            <div class="col-icon">
              <i class="fa-solid fa-comment-dots"></i>
            </div>
            <div class="col-info">
              SAC - Serviço de Atendimento ao Cliente<br>
              <a href="mailto:<EMAIL>" title="Clique para enviar e-<NAME_EMAIL>"><EMAIL></a><br>
              <a class="phone-link" href="tel:(11) 3111-9706" title="Clique para entrar em contato com o SAC">(11) 3111-9706</a>
            </div>
          </div>

          <div class="row">
            <div class="col-icon">
              <i class="fa-solid fa-phone"></i>
            </div>
            <div class="col-info">
              Geral<br>
              <a href="mailto:<EMAIL>" title="Clique para enviar e-<NAME_EMAIL>"><EMAIL></a><br>
              <a class="phone-link" href="tel:(11) 3111-9700" title="Clique para entrar em contato">(11) 3111-9700</a>
            </div>
          </div>


				</div>
			</div>



		</div>
	</div>
</section>

<script nonce="{{ csp_nonce() }}">
  function sendForm(e) {
    // e.preventDefault();

    // // var name = $('#name').val();
    // //     name = name.split(' ');

    // var sub = $('#email_subject').val();
    //     sub = sub.replace(':name', 'aaaaa');
    //     $('#email_subject').val(sub);

    // $('#ouvidoria-form').submit();
  }
</script>
@endsection
