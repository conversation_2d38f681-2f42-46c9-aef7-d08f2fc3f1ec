@extends('components.admin.header')
@section('title', 'Nova Senha')

@section('head')
  <link rel="stylesheet" href="{{ asset('/css/page-auth.css') }}" />
@endsection

<body>

  <div class="container-xxl">

    <div class="authentication-wrapper authentication-basic container-p-y">

      <div class="authentication-inner">

        <div class="card">

          <div class="card-body">

            <div class="app-brand justify-content-center">

              <a href="index.html" class="app-brand-link gap-2">

                <img src="{{ asset('img/logo.png') }}" alt="26Notas">

              </a>

            </div>

            <h4 class="mb-4 text-center">Nova Senha</h4>

            <form id="formAuthentication" class="mb-3" action="{{ route('admin.new.password', [
              'email' => request()->email,
              'token' => request()->token
            ]) }}" method="POST">
              @csrf

              <div class="mb-3">

                <label for="email" class="form-label">senha</label>

                <input type="password" class="form-control" id="password" name="password" placeholder="Digite a nova senha" autofocus required />

              </div>

              @error('password')
                <div class="mb-4 mt-1">
                  <span style="color: red;">{{ $message }}</span>
                </div>
              @enderror

              <div class="mb-3">

                <button class="btn btn-primary d-grid w-100" type="submit">Definir Nova Senha</button>

              </div>
              <a href="{{ route('admin.login') }}">
                <small>Fazer Login</small>
              </a>

            </form>

          </div>

        </div>

      </div>

    </div>

  </div>

  <x-admin.footer />
