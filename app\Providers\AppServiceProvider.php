<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\View;
use App\Models\Menu;
use Illuminate\Pagination\Paginator;
use App\Models\Sysmeta;
use Illuminate\Support\Facades\Validator;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind('path.public', function () {
            return base_path() . '/html';
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Schema::defaultStringLength(191);

        $menus = Menu::orderBy('index')->get();
        View::share('menus', $menus);

        Paginator::defaultView('vendor.pagination.bootstrap-5');

        $finalOfUrl = explode('/', request()->url());
        $finalOfUrl = count($finalOfUrl) > 3 ? end($finalOfUrl) : 'home';


        View::share('finalOfUrl', $finalOfUrl);
        View::share('certificateSettings', $this->getCertificateSettings());
        View::share('email_trasladoSettings', $this->getMailTrasladoSettings());

        Validator::extend('cpf', function ($attribute, $value, $parameters, $validator) {
            // Função para validar CPF
            $cpf = preg_replace('/[^0-9]/', '', $value);

            if (strlen($cpf) != 11 || preg_match('/(\d)\1{10}/', $cpf)) {
                return false;
            }

            for ($t = 9; $t < 11; $t++) {
                for ($d = 0, $c = 0; $c < $t; $c++) {
                    $d += $cpf[$c] * (($t + 1) - $c);
                }
                $d = ((10 * $d) % 11) % 10;
                if ($cpf[$c] != $d) {
                    return false;
                }
            }

            return true;
        }, 'O CPF informado é inválido.');

        Validator::extend('cnpj', function ($attribute, $value, $parameters, $validator) {
            // Função para validar CNPJ
            $cnpj = preg_replace('/[^0-9]/', '', $value);

            if (strlen($cnpj) != 14 || preg_match('/(\d)\1{13}/', $cnpj)) {
                return false;
            }

            $tamanho = strlen($cnpj) - 2;
            $numeros = substr($cnpj, 0, $tamanho);
            $digitos = substr($cnpj, $tamanho);

            $soma = 0;
            $pos = $tamanho - 7;
            for ($i = $tamanho; $i >= 1; $i--) {
                $soma += $numeros[$tamanho - $i] * $pos--;
                if ($pos < 2) {
                    $pos = 9;
                }
            }

            $resultado = $soma % 11 < 2 ? 0 : 11 - $soma % 11;
            if ($resultado != $digitos[0]) {
                return false;
            }

            $tamanho = $tamanho + 1;
            $numeros = substr($cnpj, 0, $tamanho);
            $soma = 0;
            $pos = $tamanho - 7;
            for ($i = $tamanho; $i >= 1; $i--) {
                $soma += $numeros[$tamanho - $i] * $pos--;
                if ($pos < 2) {
                    $pos = 9;
                }
            }

            $resultado = $soma % 11 < 2 ? 0 : 11 - $soma % 11;
            return $resultado == $digitos[1];
        }, 'O CNPJ informado é inválido.');
    }

    private function getCertificateSettings()
    {
        $defaults = json_encode([
            'value' => 83.40,
            'boleto_fee' => 2.45,
            'widthCM' => 22,
            'heightCM' => 2,
            'lengthCM' => 36,
            'weightKG' => 0.3
        ]);

        $certificateSettings = Sysmeta::firstOrCreate(
            ['meta_key' => 'certificate_settings'],
            ['meta_value' => $defaults]
        );

        return json_decode($certificateSettings->meta_value);
    }

    private function getMailTrasladoSettings()
    {
        $defaults = json_encode([
            'conteudo_traslado' => '',
            'conteudo_newsletter' => ''
        ]);

        $email_trasladoSettings = Sysmeta::firstOrCreate(
            ['meta_key' => 'mail_traslado_settings'],
            ['meta_value' => $defaults]
        );

        return json_decode($email_trasladoSettings->meta_value);
    }
}
