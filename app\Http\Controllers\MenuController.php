<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Menu;
use App\Models\Submenu;
use App\Models\Page;

class MenuController extends Controller
{
  public function menu($slug)
  {
    $menu = Menu::where('slug', $slug)->first();
    if (empty($menu))
      return abort(404);
      
    $page = Page::where([
      'type' => 'dynamic',
      'dynamic_page' => 'home'
    ])->first();
    $dynamic_page_settings = $page->dynamic_page_settings ? json_decode($page->dynamic_page_settings) : [];
    return view('menu.menu', [
      'menu' => $menu,
      'dynamic_page_settings' => $dynamic_page_settings
    ]);
  }

  public function submenu($slug)
  {
    $submenu = Submenu::where('slug', $slug)->first();
    if (empty($submenu))
      return abort(404);

    $page = Page::where([
      'type' => 'dynamic',
      'dynamic_page' => 'home'
    ])->first();
    $dynamic_page_settings = $page->dynamic_page_settings ? json_decode($page->dynamic_page_settings) : [];
    return view('menu.submenu', [
      'submenu' => $submenu,
      'dynamic_page_settings' => $dynamic_page_settings
    ]);
  }
}
