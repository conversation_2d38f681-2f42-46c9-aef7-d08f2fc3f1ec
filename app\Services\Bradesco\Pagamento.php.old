<?php

namespace Cartorio26\Services\Bradesco;

use Cartorio26\Certidao;
use Cartorio26\Services\Bradesco\Exception\PagamentoException;
use GuzzleHttp\Client;
use Sentry\SentryLaravel\SentryFacade;

class Pagamento {

    public static function generateTransferRequest($codigo, $valor, Comprador $comprador) {
        date_default_timezone_set('America/Sao_Paulo');
        $transferRequest = new \stdClass();
        $pedido = new \stdClass();

        $transferRequest->merchant_id = env('BRADESCO_MERCHANT_ID');
        $transferRequest->meio_pagamento = env('BRADESCO_COD_MEIO_PAGAMENTO_TRANSFERENCIA');

        $pedido->numero = 'ped_'.$codigo.'_'.rand(1000000,9000000);
        $pedido->valor = $valor;
        $pedido->descricao = 'Certidão de Ato Notarial Arquivado';

        $transferRequest->pedido = $pedido;
        $transferRequest->comprador = $comprador;

        return self::doTransferRequest($transferRequest);
    }

    public static function generateBoletoRequest($codigo, $valor, Comprador $comprador, Certidao $certidao, $sedex, $customidia) {
        $boletoRequest = new \stdClass();
        $pedido = new \stdClass();
        $boleto = new Boleto($valor, $certidao, $sedex, $customidia);

        $boletoRequest->merchant_id = env('BRADESCO_MERCHANT_ID');
        $boletoRequest->meio_pagamento = env('BRADESCO_COD_MEIO_PAGAMENTO_BOLETO');

        $pedido->numero = 'ped_'.$codigo.'_'.rand(1000000,9000000);
        $pedido->valor = $valor;
        $pedido->descricao = 'Certidão de Ato Notarial Arquivado';

        $boletoRequest->pedido = $pedido;
        $boletoRequest->comprador = $comprador;
        $boletoRequest->boleto = $boleto;

        try {
            return self::doBoletoRequest($boletoRequest);
        } catch (PagamentoException $e) {
            app('sentry')->captureException($e);
            $paymentData = new \stdClass();
            $paymentData->error = true;
            return $paymentData;
        }
    }


    private static function doTransferRequest($transferRequest) {
        $uri = env('BRADESCO_TRANSFER_URI');
        $headers = [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'Authorization' => 'Basic ' . base64_encode(env('BRADESCO_MERCHANT_ID') . ':' . env('BRADESCO_KEY'))
        ];
        $body = json_encode($transferRequest);

        $client = new Client();
        $response = $client->post($uri, ['headers' => $headers, 'body' => $body]);

        $responseBody = $response->getBody();
        $response = json_decode($responseBody);
        $transferUrl = $response->transferencia->url_acesso;

        return $transferUrl;
    }

    private static function doBoletoRequest($transferRequest) {
        $uri = env('BRADESCO_BOLETO_URI');
        $headers = [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'Authorization' => 'Basic ' . base64_encode(env('BRADESCO_MERCHANT_ID') . ':' . env('BRADESCO_KEY'))
        ];
        $body = json_encode($transferRequest);


        $client = new Client();
        $response = $client->post($uri, ['headers' => $headers, 'body' => $body]);

        $responseBody = $response->getBody();
        $response = json_decode($responseBody);

        $paymentData = new \stdClass();
        if(empty($response->boleto)) {
            throw new PagamentoException($response->mensagem, $response->detalhes);
        }
        $paymentData->url = $response->boleto->url_acesso;
        $paymentData->linha = $response->boleto->linha_digitavel;
        $paymentData->linha_formatada = $response->boleto->linha_digitavel_formatada;

        return $paymentData;
    }
}
