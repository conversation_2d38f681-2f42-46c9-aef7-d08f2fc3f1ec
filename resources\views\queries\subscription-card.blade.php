@extends('layouts.my-account')

@section('content')
<section class="account txtcontent">
  <div class="container-fluid">
	@if (!isset($resposta))
    <div class="px-1">
      <div class="breadcrumb">
        <a href="{{ route('my.services') }}">Meus Serviços</a>  >
        <span>Cartão de Assinatura</span>
      </div>
      <div class="d-flex justify-content-between align-items-start">
        <h1>Cartão de Assinatura</h1>
        <a href="{{ route('my.services') }}" class="btnDefault" style="background: #708082;">Voltar</a>
      </div>
    </div>
		<div class="row">
			<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">

				<p><strong class="blue">PESQUISA PELO CARTÃO DE ASSINATURA</strong><br>Verifique se a parte possui cadastro em nosso cartório. Informe o nome ou CPF que deseja pesquisar.</p>

				<form action="{{ route('queries.show.subscription.card') }}" method="POST">
          @csrf
          <div class="formControl">
            <label for="">Pesquisar por:</label>
						<select name="cartao_nome_cpf" id="cartao_nome_cpf" class="form-control" onchange="toggleFields()">
              <option value="0" selected="">Nome</option>
              <option value="1">CPF</option>
            </select>
					</div>
					<div class="formControl" id="cartao_nome">
						<label>Informe o nome:</label>
						<input type="text" name="cartao_nome" min="7" pattern="[A-Za-zÀ-ÿ\s]+" title="Digite apenas letras" required>
					</div>
          <div class="formControl" id="cartao_cpf" style="display: none;">
						<label>Informe o CPF:</label>
						<input id="cpf" class="cpf" type="text" name="cartao_cpf" pattern="[0-9.-]+" title="Digite apenas números">
					</div>
					<div class="d-flex">
						<button class="btnDefault">Enviar</button>
					</div>
				</form>

			</div>

		</div>

    @else
      <div class="px-1">
        <div class="breadcrumb">
          <a href="{{ route('my.services') }}">Meus Serviços</a>  >
          <a href="{{ route('queries.subscription.card') }}">Cartão de Assinatura</a>  >
          <span>Resultado</span>
        </div>
        <div class="d-flex justify-content-between align-items-start">
          <h1>Cartão de Assinatura</h1>
          <a href="{{ route('queries.subscription.card') }}" class="btnDefault" style="background: #708082;">Voltar</a>
        </div>
      </div>
      <div class="row">
        <div class="resultadoBusca">

          <div class="headBusca">
            <h2>{{ $resultadoText }}</h2>
          </div>

          <table class="registros">
            <tbody>
              <tr class="head">
                <th>Código</th>
                <th>Nome</th>
                <th>Documento</th>
                <th>Data de Cadastro</th>
              </tr>
              @if (!isset($resposta['results'][0]->message))
                @foreach ($resposta['results'] as $resultado)
                    @foreach ($resultado as $data)
                    <tr>
                        <td>{{ $data->Codigo ?? '' }}</td>
                        <td>{{ $data->Nome }}</td>
                        <td>{{ substr($data->CPF, 0, 3) }}...</td>
                        <td>{{ !isset($data->DataUltAlteracao) ? "Não informado" : date_format(date_create($data->DataUltAlteracao),"d/m/Y") }}</td>
                    </tr>
                    @endforeach
                @endforeach
              @else
                <tr>
                  <td colspan="5">
                    <p style="text-align: center">Sem Resultado</p>
                  </td>
                </tr>
              @endif
            <tr>
          </tr></tbody></table>
        </div>
      </div>
    @endif

	</div>

</section>

<script nonce="{{ csp_nonce() }}">
  function toggleFields() {
    if ($('#cartao_nome_cpf').val() == '0') {
      $('#cartao_nome').show();
      $('#cartao_nome').children('input').attr('required', true);

      $('#cartao_cpf').hide();
      $('#cartao_cpf').children('input').attr('required', false);
    } else {
      $('#cartao_nome').hide();
      $('#cartao_nome').children('input').attr('required', false);

      $('#cartao_cpf').show();
      $('#cartao_cpf').children('input').attr('required', true);
    }
  };

  $( document ).ready(function() {
    setTimeout(() => {
      toggleFields();
    }, 1);
  })

</script>
@endsection
