<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

use App\Models\News;
use App\Models\NewsCategories;
use App\Models\NewsAuthors;
use App\Models\NewsBanners;

class NewsController extends Controller
{
  public function categories()
  {
    $categories = NewsCategories::all();
    return view('admin.news.categories', [
      'categories' => $categories
    ]);
  }

  public function categoriesStore(Request $r)
  {
    $validator = Validator::make($r->all(), [
      'name' => 'required'
    ]);

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }

    $category = new NewsCategories;
    $category->name = $r->name;
    $category->save();
    return redirect()->back()->with('success', 'Categoria criada com sucesso!');
  }

  public function categoriesEdit($id)
  {
    $category = NewsCategories::find($id);
    return view ('admin.news.categories-edit', [
      'category' => $category
    ]);
  }

  public function categoriesUpdate(Request $r, $id)
  {
    $validator = Validator::make($r->all(), [
      'name' => 'required'
    ]);

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }

    $category = NewsCategories::find($id);
    $category->name = $r->name;
    $category->save();
    return redirect()->route('news.categories')->with('success', 'Categoria Editada com sucesso!');
  }

  public function categoriesDestroy($id)
  { 
    NewsCategories::destroy($id);
    return redirect()->route('news.categories')->with('success', 'Categoria excluída com sucesso!');
  }

  public function authors()
  {
    $authors = NewsAuthors: <AUTHORS>
    return view('admin.news.authors', [
      'authors' => $authors,
    ]);
  }

  public function authorsStore(Request $r)
  {
    $validator = Validator::make($r->all(), [
      'name' => 'required'
    ]);

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }

    $author = new NewsAuthors;
    $author->name = $r->name;
    $author->occupation = $r->occupation;
    $author->save();
    return redirect()->back()->with('success', 'Autor criado com sucesso!');
  }

  public function authorsEdit($id)
  {
    $author = NewsAuthors: <AUTHORS>
    return view ('admin.news.authors-edit', [
      'author' => $author
    ]);
  }

  public function authorsUpdate(Request $r, $id)
  {
    $validator = Validator::make($r->all(), [
      'name' => 'required',
    ]);

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }

    $author = NewsAuthors: <AUTHORS>
    $author->name = $r->name;
    $author->occupation = $r->occupation;
    $author->save();
    return redirect()->route('news.authors')->with('success', 'Autor Editado com sucesso!');
  }

  public function authorsDestroy($id)
  { 
    NewsAuthors: <AUTHORS>
    return redirect()->route('news.authors')->with('success', 'Autor excluído com sucesso!');
  }

  public function banners()
  {
    $banners = NewsBanners::all();
    return view('admin.news.banners', [
      'banner1' => $banners[0],
      'banner2' => $banners[1]
    ]);
  }

  public function bannersUpdate(Request $r, $id)
  {
    $validator = Validator::make($r->all(), [
      'title' => 'required'
    ]);

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }

    $banner = NewsBanners::find($id);
    $banner->title = $r->title;
    $banner->button_text = $r->button_text;
    $banner->button_link = $r->button_link;

    if (!empty($r->photo)) {
      $file = $r->file('photo');
      $name = $file->hashName();
      $path = $file->storeAs('public', $name);
      $banner->photo = $name;
    }

    $banner->show = boolval($r->show);
    $banner->save();
    return redirect()->back()->with('success', 'Banner editado com sucesso!');
  }

  public function news(Request $r)
  {
    $where[] = ['id', '!=', null];

    if (!empty($r->categoria)) 
      $where[] = ['category_id', '=', $r->categoria];

    if (!empty($r->titulo))
      $where[] = ['title', 'like', '%' . $r->titulo . '%'];

    $news = News::where($where)->orderBy('created_at', 'desc')->paginate(30);
    $categories = NewsCategories::all();

    return view('admin.news.news', [
      'news' => $news,
      'categories' => $categories,
    ]);
  }

  public function newsCreate()
  {
    $categories = NewsCategories::all();
    $authors = NewsAuthors: <AUTHORS>
    return view('admin.news.news-create', [
      'categories' => $categories,
      'authors' => $authors
    ]);
  }

  public function newsStore(Request $r)
  {
    $validator = Validator::make($r->all(), [
      'title' => 'required',
      'category_id' => 'required',
      'author_id' => 'required',
      'content' => 'required'
    ]);

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }

    $news = new News;
    $news->title = $r->title;
    $news->slug = $this->slugify($r->title);
    $news->category_id = $r->category_id;
    $news->author_id = $r->author_id;
    $news->published = boolval($r->published);
    $news->content = $r->content;

    if (!empty($r->photo)) {
      $file = $r->file('photo');
      $name = $file->hashName();
      $path = $file->storeAs('public', $name);
      $news->photo = $name;
    }

    $news->save();
    return redirect()->route('news.news')->with('success', 'Novidade criada com sucesso!');
  }

  public function slugify($text, string $divider = '-')
  {
    $text = preg_replace('~[^\pL\d]+~u', $divider, $text);
    $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);
    $text = preg_replace('~[^-\w]+~', '', $text);
    $text = trim($text, $divider);
    $text = preg_replace('~-+~', $divider, $text);
    $text = strtolower($text);
    return $text;
  }

  public function newsEdit($id)
  {
    $categories = NewsCategories::all();
    $authors = NewsAuthors: <AUTHORS>
    $news = News::find($id);
    return view('admin.news.news-edit', [
      'categories' => $categories,
      'authors' => $authors,
      'news' => $news
    ]);
  }

  public function newsUpdate(Request $r, $id)
  {
    $validator = Validator::make($r->all(), [
      'title' => 'required',
      'category_id' => 'required',
      'author_id' => 'required',
      'content' => 'required'
    ]);

    if ($validator->fails()) {
      return redirect()->back()->withErrors($validator)->withInput();
    }

    $news = News::find($id);
    $news->title = $r->title;
    $news->slug = $this->slugify($r->title);
    $news->category_id = $r->category_id;
    $news->author_id = $r->author_id;
    $news->published = boolval($r->published);
    $news->content = $r->content;

    if (!empty($r->photo)) {
      $file = $r->file('photo');
      $name = $file->hashName();
      $path = $file->storeAs('public', $name);
      $news->photo = $name;
    }

    $news->save();
    return redirect()->route('news.news')->with('success', 'Novidade editada com sucesso!');
  }

  public function newsDestroy($id)
  { 
    News::destroy($id);
    return redirect()->route('news.news')->with('success', 'Novidade excluída com sucesso!');
  }
}
