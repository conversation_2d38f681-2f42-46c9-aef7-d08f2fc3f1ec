<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Mail;
use App\Mail\CollaboratorRecoveryPassword;
use App\Models\Collaborator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Carbon;

class AuthController extends Controller
{
  public function login()
  {
    if (Auth::guard('admin')->user())
    return redirect()->route('admin');

    return view('admin.auth.login');
  }

  public function auth(Request $r)
  {
    $credentials = $r->validate([
      'email' => ['required', 'email'],
      'password' => ['required'],
    ]);

    if (Auth::guard('admin')->attempt($credentials)) {
      $r->session()->regenerate();

      return redirect()->route('admin');
    }

    return back()->withErrors([
        'email' => 'Essas credenciais não foram encontradas em nossos registros.',
    ])->onlyInput('email');
  }

  public function logout()
  {
    Auth::guard('admin')->logout();
    return redirect()->route('index');
  }

  public function recovery()
  {
    return view('admin.auth.recovery');
  }

  public function recoveryEmail(Request $r)
  {
    $r->validate([
      'email' => ['required', 'email'],
    ]);
    
    
    $this->deleteResetToken($r->email);

    $token = Str::random(32);
    DB::table('password_resets')->insert([
      'email' => $r->email,
      'token' => $token,
      'created_at' => Carbon::now()
    ]);
    $url = route('admin.new.password', ['email' => $r->email, 'token' => $token]);
    if (Mail::to($r->email)->send(new CollaboratorRecoveryPassword($url))) {
      return redirect()->route('admin.recovery')->with('success', 'Te enviamos um e-mail de recuperação de senha.');
    }

    return back()->withErrors([
      'email' => 'Essas credenciais não foram encontradas em nossos registros.',
    ])->onlyInput('email');
  }

  public function newPassword(Request $r)
  {
    $this->validateResetToken($r->email, $r->token);
    
    return view('admin.auth.new-password');
  }

  public function updatePassword(Request $r)
  {
    $r->validate([
      'password' => ['required']
    ]);

    $this->validateResetToken($r->email, $r->token);

    $collaborator = Collaborator::where('email', $r->email)->first();
    $collaborator->password = Hash::make($r->password);
    $collaborator->save();
    $this->deleteResetToken($r->email);

    return redirect()->route('admin.login')->with('success', 'Senha alterada com sucesso.');
  }

  private function validateResetToken($email, $token)
  {
    $pr = DB::table('password_resets')->where([
      'email' => $email,
      'token' => $token
    ])->first();

    if (empty($pr)) {
      abort(401);
    } else {     
      $expires = Carbon::parse($pr->created_at)->addMinutes(30);

      if (Carbon::now()->gt($expires))
        abort(401);
    }
  }

  private function deleteResetToken($email)
  {
    DB::table('password_resets')->where([
      'email' => $email
    ])->delete();
  }
}
