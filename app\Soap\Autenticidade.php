<?php namespace App\Soap;

use Artisaninweb\SoapWrapper\Extension\SoapService;

class Autenticidade extends SoapService {

    /**
     * @var string
     */
    protected $name = 'autenticidade';

    /**
     * @var string
     */
    protected $wsdl = 'http://servico26notas.cartorios.net/VerificacaoAutenticidadeWSImplService?wsdl';

    /**
     * @var boolean
     */
    protected $trace = true;

    /**
     * Get all the available functions
     *
     * @return mixed
     */
    public function functions()
    {
        return $this->getFunctions();
    }
}