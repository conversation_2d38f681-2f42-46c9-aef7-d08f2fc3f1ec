<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\PedidoCertidao;
use App\Models\FormFilling;
use Illuminate\Support\Facades\Mail;
use App\Mail\SendPedidoCertidao;
use App\Services\Bradesco\Comprador;
use App\Services\Bradesco\Endereco;
use App\Services\Bradesco\Pagamento;
use App\Models\Certificate;
use App\Models\Sysmeta;

class PedidosCertidaoController extends Controller
{
    public function index(Request $r)
    {
        $where = [];

        if (!empty($r->pedido))
            $where[] = ['pedido_id', '=', $r->pedido];

        if (!empty($r->status))
            $where[] = ['status', '=', $r->status];

        $pedidos = PedidoCertidao::where($where)->orderByDesc('id')->paginate(30);

        return view('admin.pedidos_certidao.index', [
            'pedidos' => $pedidos
        ]);
    }
    private function getCertificateSettings()
    {
        $certificateSettings = Sysmeta::where('meta_key', 'certificate_settings')->first();
        return json_decode($certificateSettings->meta_value);
    }

    public function show($id)
    {
        $pedido = PedidoCertidao::find($id);
        $form = FormFilling::find($pedido->form_id);
        $certificateSettings = $this->getCertificateSettings();
        return view('admin.pedidos_certidao.show', ['pedido' => $pedido, 'form' => json_decode($form->fields), 'textElegivel' => $certificateSettings->MensagemElegivel ?? '', 'textInelegivel' => $certificateSettings->MensagemNaoElegivel ?? '']);
    }

    public function update(Request $r)
    {
        $certificateSettings = $this->getCertificateSettings();
        $pedido = PedidoCertidao::find($r->id);
        $form = json_decode(FormFilling::find($pedido->form_id)->fields);

        if ($r->status == 0) {
            $pedido->status = 'I';
            if ($r->mensagemPersonalizada == '1') {
                $texto = $r->textInelegivel;
            } else {
                $texto = $certificateSettings->MensagemNaoElegivel;
            }
            $titulo = "Solicitação em Análise – Ação Necessária";
        } else {

            $boleto = $this->generateBoleto($pedido->pedido_id, $r);

            if($boleto['status'] == 'error') {
                return redirect()->route('admin.pedidos_certidao.index')->with('error', $boleto['message']);
            }

            $pedido->link = $boleto['url'];
            $pedido->barcode = $boleto['linha'];
            $pedido->access_password = $boleto['senha_acesso'];
            $pedido->data_emissao_boleto = date('Y-m-d H:i:s');
            $pedido->status = 'E';
            if ($r->mensagemPersonalizada == '1') {
                $texto = $r->textElegivel;
            } else {
                $texto = $certificateSettings->MensagemElegivel;
            }
            $texto = str_ireplace('{{valorpix}}', number_format($form->Valor + $form->Taxa_de_entrega + $form->Opção_digital, 2, ',', '.'), $texto);
            $texto = str_ireplace('{{valorboleto}}', number_format($boleto['valor_boleto'], 2, ',', '.'), $texto);
            $texto = str_ireplace('{{boletocodigobarras}}', $pedido->barcode, $texto);
            $texto = str_ireplace('{{chavepix}}', $certificateSettings->ChavePix, $texto);

            $titulo = "Solicitação da certidão aprovada – Dados para Pagamento";
        }

        $pedido->save();

        $texto = str_ireplace('{{protocolo}}', $pedido->pedido_id, $texto);
        $texto = str_ireplace('{{tipoato}}', $form->Tipo, $texto);
        $texto = str_ireplace('{{finalidade}}', $form->Finalidade_da_solicitação, $texto);
        $texto = str_ireplace('{{partes}}', $form->Partes, $texto);
        $texto = str_ireplace('{{livro}}', $form->Livro, $texto);
        $texto = str_ireplace('{{folha}}', $form->Folha, $texto);
        $texto = str_ireplace('{{nome}}', $form->nome, $texto);
        $texto = str_ireplace('{{nomerazaosocial}}', $form->nome, $texto);
        $texto = str_ireplace('{{telefone}}', $form->telefone, $texto);
        $texto = str_ireplace('{{email}}', $form->email, $texto);
        $texto = str_ireplace('{{tipocertidao}}', $form->Tipo_da_Certidão, $texto);
        $texto = str_ireplace('{{formaentrega}}', $form->Forma_de_Entrega, $texto);
        $texto = str_ireplace('{{tipoenvio}}', $form->tipo_envio_select, $texto);
        $texto = str_ireplace('{{cep}}', $form->CEP, $texto);
        $texto = str_ireplace('{{logradouro}}', $form->Logradouro, $texto);
        $texto = str_ireplace('{{numero}}', $form->Número, $texto);
        $texto = str_ireplace('{{complemento}}', $form->Complemento, $texto);
        $texto = str_ireplace('{{bairro}}', $form->Bairro, $texto);
        $texto = str_ireplace('{{cidade}}', $form->Cidade, $texto);
        $texto = str_ireplace('{{uf}}', $form->UF, $texto);
        $texto = str_ireplace('{{cpfcnpj}}', $form->CPF ?? $pedido->CNPJ, $texto);

        $this->sendByMail($texto, $titulo, $form->email);
        return redirect()->route('admin.pedidos_certidao.index')->with('success', 'E-mail enviado com sucesso!');
    }

    public function generateBoleto($pedido_id, $request)
    {
        $certificateSettings = $this->getCertificateSettings();
        $certidao = Certificate::find($pedido_id);

        $endereco = new Endereco();
        $endereco->setCep($certidao->cep);
        $endereco->setLogradouro($certidao->logradouro);
        $endereco->setNumero($certidao->numero);
        $endereco->setComplemento($certidao->complemento);
        $endereco->setBairro($certidao->bairro);
        $endereco->setCidade($certidao->cidade);
        $endereco->setUf($certidao->uf);

        $comprador = new Comprador($request, $endereco);
        $comprador->setNome($certidao->nome_solicitante);
        if ($certidao->tipo_solicitante === 'pj') {
            $documento = $certidao->cnpj;
        } else {
            $documento = $certidao->cpf;
        }
        $comprador->setDocumento($documento);

        $valorTotal = $certidao->valor + $certidao->valor_sedex + $certificateSettings->boleto_fee;

        if (isset($certidao->custo_midia))
            $valorTotal += $certidao->custo_midia;

        $valor = str_replace('.', '', number_format((float)$valorTotal, 2, '.', ''));

        $dadosDePagamento = Pagamento::generateBoletoRequest($certidao->id, $valor, $comprador, $certidao, ($certidao->forma_entrega === 'correios'), $certidao->custo_midia);

        if ($dadosDePagamento->error) {
            return [
                'status' => 'error',
                'message' => $dadosDePagamento->MessageError
            ];
        }

        $strong = true;
        $bytes = openssl_random_pseudo_bytes(5, $strong);
        if ($bytes === false) {
            abort(500);
        }
        $senha = bin2hex($bytes);

        $return = [
            'status' => 'success',
            'message' => '',
            'url' => $dadosDePagamento->url,
            'linha' => $dadosDePagamento->linha,
            'linha_formatada' => $dadosDePagamento->linha_formatada,
            'senha_acesso' => $senha,
            'valor_boleto' => $valorTotal
        ];

        return $return;
    }

    public function sendByMail($texto, $titulo, $emailPara)
    {
        if (Mail::to($emailPara)
            ->cc(['<EMAIL>', '<EMAIL>', '<EMAIL>'])
            ->send(new SendPedidoCertidao($texto, $titulo))
        ) {
            return response()->json([], 200);
        }
    }
}
