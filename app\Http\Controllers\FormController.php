<?php

namespace App\Http\Controllers;

use App\Bitrix\IntegracaoBitrix;
use App\Models\Client;
use App\Models\Collaborator;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Mail\SendForm;
use App\Mail\ThankYouEmailToUser;
use App\Http\Controllers\NewsletterController;
use Illuminate\Support\Facades\Storage;
use App\Services\Bradesco\Comprador;
use App\Services\Bradesco\Endereco;
use App\Services\Bradesco\Pagamento;
use App\Models\Certificate;
use App\Mail\CertificateRequestProtocol;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\FormFillingController;
use App\Models\PedidoCertidao;
use App\Models\Sysmeta;

class FormController extends Controller
{
    private $valorTotal = 0;
    private $valorTotalENotarial = 0;
    private $valorTotalEntrega = 0;

    private function getEmailSubject(Request $r, $userSubject = false)
    {
        $subject = (!empty($r->email_subject)) ? $r->email_subject : 'send form........';
        $template = $r->email_template;

        $options = [
            'servico-resposta-padrao-user',
            'servico-resposta-colaboradores01-user',
            'servico-resposta-colaboradores02-user',
            'servico-carta-sentenca-notarial-user',
            'servico-escritura-testamento-publico-user',
            'servico-autorizacao-viagem-menores-user',
        ];
        if (in_array($template, $options)) {
            $name = explode(' ', $r->nome);
            $name = $name[0];
            $service = $r->service;

            if ($userSubject) {
                $subject = $name . ' recebemos suas informações para o ato ' . $service;
            } else {
                $subject = 'Uma nova solicitação de ' . $name . ' em ' . $service;
            }

            return $subject;
        }


        $options = [
            'thank-you-email-to-user',
        ];
        if (in_array($template, $options)) {
            $name = explode(' ', $r->nome);
            $name = $name[0];

            if ($userSubject) {
                $subject = 'Agradecemos o contato ' . $name;
            } else {
                $subject = 'Formulário de ouvidoria enviado por ' . $name;
            }
            return $subject;
        }

        $options = [
            'mensalistas-user',
        ];
        if (in_array($template, $options)) {
            $name = explode(' ', $r->nome);
            $name = $name[0];

            if ($userSubject) {
                $subject = $name . ' agradecemos o interesse em se tornar mensalista do 26 Notas';
            } else {
                $subject = 'Formulário de mensalistas enviado por ' . $name;
            }

            return $subject;
        }

        $options = [
            'certificate-request-protocol',
        ];
        if (in_array($template, $options)) {
            $name = explode(' ', $r->nome);
            $name = $name[0];
            $subject = 'Pedido de certidão de ato notarial enviado por ' . $name;

            return $subject;
        }

        return $subject;
    }

    public function send(Request $r)
    {
        $form = $r->form ?? '';
        $data = $r->except(['file', '_token', 'recipients', 'form', 'g-recaptcha-response', 'email_subject', 'email_template', 'service', 'accordingly_checkbox', 'want_messages_checkbox']);
        $isDigital = ($r->Forma_de_Entrega ?? $r->input('Forma_de_Entrega')) === 'eletronica';
        $subject = (!empty($r->email_subject)) ? $r->email_subject : 'send form........';

        if ($form == 'd') {
            $rules = [
                'g-recaptcha-response' => 'recaptcha',
                'email' => 'required|email'
            ];

            if ($r->Tipo_pessoa == 'pf')
                $rules['CPF'] = 'required|cpf';
            else
                $rules['CNPJ'] = 'required|cnpj';

            if (!$isDigital) {
                $rules['Bairro'] = 'required';
                $rules['Cidade'] = 'required';
                $rules['UF'] = 'required';
            }
            $validator = Validator::make(request()->all(), $rules);
        } else {
            $validator = Validator::make(request()->all(), [
                'g-recaptcha-response' => 'recaptcha',
                'email' => 'required|email',
            ]);
        }

        if ($validator->fails()) {
            if ($r->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $formData = [];
        if ($form == 'd') {
            return $this->sendFormD($r);
        }

        $formFilling = new FormFillingController;
        $integracaoBitrix = new IntegracaoBitrix;

        $escrevente = Collaborator::where('id', $r->Escrevente)->first();
        $escrevente_email = $escrevente->email ?? null;
        $escrevente_name  = $escrevente->name ?? null;

        if ($escrevente_name !== null) {
            $data['Escrevente'] = $escrevente_name;
            $r['escrevente_nome'] = $escrevente_name;
        }

        $form_id = $formFilling->onlySave($r, "Pendente Envio", null, "Form 26");

        $integracaoBitrix->enviarFormParaBitrix($form_id);

        $files = [];
        $path = '';
        if ($r->file) {
            foreach ($r->file('file') as $file) {
                $fileName = $file->getClientOriginalName();
                $fileNameWithoutExtension = pathinfo($fileName, PATHINFO_FILENAME);
                $fileName = $this->sanitizeFileName($fileNameWithoutExtension);
                $timestamp = Carbon::now()->format('d-m-Y_H\h_i\m');
                $extension = $file->getClientOriginalExtension();
                $hashName = $fileName . "_" . $timestamp . "_" . "." . $extension;
                $path = $file->storeAs('', $hashName);

                $files[] = $path;
            }
        }

        $emails = [];

        if (!empty($r->escrevente_email)) {
            $emails[] = $escrevente_email;
        }

        if (!empty($r->recipients)) {
            $recipientEmails = array_filter(explode(";", $r->recipients));
            $emails = array_merge($emails, $recipientEmails);
        }

        $failures = [];

        foreach ($emails as $email) {
            if (!Mail::to($email)->send(new SendForm($subject, $data, $files))) {
                $failures[] = $email;
            }
        }

        if (empty($failures)) {
            Storage::delete($path);
            if ($form != 'd' && !empty($r->email)) {
                $name = explode(' ', $r->nome)[0];
                $sub = $this->getEmailSubject($r, true);
                Mail::to($r->email)->send(new ThankYouEmailToUser($sub, $name, $r->email_template));
                if (boolval($r->want_messages_checkbox)) {
                    $newsletter = new NewsletterController;
                    $newsletter = $newsletter->index($r);
                }
            }
        } else {
            if ($r->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Falha ao enviar e-mail.'
                ], 500);
            }
            return redirect()->back()->with('form_error', true)->withInput();
        }

        if ($r->ajax()) {
            return response()->json([
                'success' => true,
                'form_success' => true,
                'formData' => $formData
            ]);
        }

        return redirect()->back()->with([
            'form_success' => true,
            'formData' => $formData
        ])->withInput();
    }


    public function sendFormD(Request $r)
    {
        $failures = [];
        $formData = [];
        $subject = $this->getEmailSubject($r);
        $req = clone $r;

        foreach ($req["Tipo"] as $idx => $tipo) {
            $propriedade_arquivos = 'arquivos_' . $req->hdnIndice[$idx];
            if (explode('|', $req->Tipo[$idx] ?? '')[2] == "1" && !$req->hasFile($propriedade_arquivos)) {
                return response()->json([
                    'success' => false,
                    'errors' => [
                        'Tipo' => ['É necessário informar pelo menos 1 arquivo para o tipo ' . explode('|', $req->Tipo[$idx] ?? '')[0] . "."]
                    ]
                ], 422);
            }
        }

        foreach ($req["Tipo"] as $idx => $tipo) {
            $propriedade = 'Dados_do_documento_' . $req->hdnIndice[$idx];
            $propriedade_arquivos = 'arquivos_' . $req->hdnIndice[$idx];

            $r["Tipo"] = explode('|', $req->Tipo[$idx] ?? '')[0];
            $r["Dados_para_busca"] = $req->Dados_para_busca[$idx] ?? '';
            $r["Dados_do_documento"] = $req->$propriedade ?? '';
            $r["Finalidade_da_solicitação"] = $req->Finalidade_da_solicitação[$idx] ?? '';
            $r["Partes"] = $req->Partes[$idx] ?? '';
            $r["Livro"] = $req->Livro[$idx] ?? '';
            $r["Folha"] = $req->Folha[$idx] ?? '';
            $formData = $this->formD($r);
            $r['Número_do_Pedido'] = $formData['certificate_id'];
            $data = $formData['request'];
            $data['Número_do_Pedido'] = $formData['certificate_id'];
            $subject .= ' - Protocolo ' . $data['Número_do_Pedido'];
            $valor = $r['Valor'];
            if (isset($r["Taxa_de_entrega"])) {
                $valor += $r["Taxa_de_entrega"];
            }
            if (isset($r["Opção_digital"])) {
                $valor += $r["Opção_digital"];
            }

            $formFilling = new FormFillingController;
            $integracaoBitrix = new IntegracaoBitrix;

            $escrevente = Collaborator::where('id', $r->Escrevente)->first();
            $escrevente_email = $escrevente->email ?? null;
            $escrevente_name  = $escrevente->name ?? null;

            if ($escrevente_name !== null) {
                $data['Escrevente'] = $escrevente_name;
                $r['escrevente_nome'] = $escrevente_name;
            }

            $files = [];
            $path = '';
            if ($r->hasFile($propriedade_arquivos)) {
                foreach ($r->file($propriedade_arquivos) as $file) {
                    $fileName = $file->getClientOriginalName();
                    $fileName = pathinfo($fileName, PATHINFO_FILENAME);
                    $timestamp = Carbon::now()->format('d-m-Y_H\h_i\m');
                    $extension = $file->getClientOriginalExtension();
                    $hashName = $fileName . "_" . $timestamp . "_" . "." . $extension;
                    $path = $file->storeAs('pedidos_certidao', $hashName);

                    $files[] = $path;
                }
                $r["arquivos"] = $files;
            }

            $form_id = $formFilling->onlySave($r, "Pendente Envio", null, "Form 26");

            $pedido = new PedidoCertidao();
            $pedido->form_id = $form_id;
            $pedido->pedido_id = $r['Número_do_Pedido'];
            $pedido->name = $r->nome;
            $pedido->document = $r->CPF ?? $r->CNPJ;
            $pedido->status = 'P';
            $pedido->save();

            $integracaoBitrix->enviarFormParaBitrix($form_id);

            $emails = [];

            if (!empty($r->escrevente_email)) {
                $emails[] = $escrevente_email;
            }

            if (!empty($r->recipients)) {
                $recipientEmails = array_filter(explode(";", $r->recipients));
                $emails = array_merge($emails, $recipientEmails);
            }

            foreach ($emails as $email) {
                if (!Mail::to($email)->send(new SendForm($subject, $data, $files))) {
                    $failures[] = $email;
                }
            }
        }

        $formData['request']["Valor"] = $this->valorTotal;
        $formData['request']["Taxa_de_entrega"] = $this->valorTotalEntrega;
        $formData['request']["Opção_digital"] = $this->valorTotalENotarial;

        if (empty($failures)) {
            Storage::delete($path);
        } else {
            if ($r->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Falha ao enviar e-mail.'
                ], 500);
            }
            return redirect()->back()->with('form_error', true)->withInput();
        }

        if ($r->ajax()) {
            return response()->json([
                'success' => true,
                'form_success' => true,
                'formData' => $formData
            ]);
        }

        return redirect()->back()->with([
            'form_success' => true,
            'formData' => $formData
        ])->withInput();
    }

    public function formD(Request $r)
    {
        $certificateSettings = $this->getCertificateSettings();

        $deliveryFee = null;

        $valorENotariado = "0";

        if (isset($certificateSettings->value_e_notariado) && $r->Forma_de_Entrega == 'eletronica')
            $valorENotariado = number_format($certificateSettings->value_e_notariado, 2, '.', ',');

        if ($r->Forma_de_Entrega == 'correios') {

            if ($r->tipo_envio_select == '' || $r->CEP == '')
                abort(500);

            $rDelivery = new Request;
            $rDelivery['type_correios'] = $r->tipo_envio_select;
            $rDelivery['cep'] = $r->CEP;

            $deliveryFee = $this->getCorreiosDeliveryFee($rDelivery);
        }

        $r->merge([
            'Valor' => number_format($certificateSettings->value, 2, '.', ','),
            'Taxa_de_entrega' => $deliveryFee,
            'Opção_digital' => $valorENotariado
        ]);

        $certificate = new Certificate;
        $certificate->partes = $r->Partes;
        $certificate->livro = $r->Livro;
        $certificate->folhas = $r->Folha;
        $certificate->tipo_solicitante = $r->Tipo_pessoa;
        $certificate->cpf = $r->CPF;
        $certificate->cnpj = $r->CNPJ;
        $certificate->nome_solicitante = $r->nome;
        $certificate->email = $r->email;
        $certificate->telefone_solicitante = $r->telefone;
        $certificate->forma_entrega = $r->Forma_de_Entrega;
        $certificate->tipo_certidao = $r->Tipo_da_Certidão;
        $certificate->cep = $r->CEP;
        $certificate->logradouro = $r->Logradouro;
        $certificate->numero = $r->Número;
        $certificate->complemento = $r->Complemento;
        $certificate->bairro = $r->Bairro;
        $certificate->cidade = $r->Cidade;
        $certificate->uf = $r->UF;
        $certificate->valor = $r->Valor;
        $certificate->valor_sedex = $r->Taxa_de_entrega;
        if ($valorENotariado != "0")
            $certificate->custo_midia = $certificateSettings->value_e_notariado;

        $certificate->save();

        $this->valorTotal += $certificate->valor;
        $this->valorTotalENotarial += $certificate->custo_midia;
        $this->valorTotalEntrega += $certificate->valor_sedex;

        $data = [
            'nome' => $certificate->nome_solicitante,
            'data' => $certificate->created_at->format('d/m/Y'),
            'data_entrega' => $certificate->created_at->addDays('4')->format('d/m/Y'),
            'protocolo' => $certificate->id,
            'partes' => $certificate->partes,
            'livro' => $certificate->livro,
            'folha' => $certificate->folhas
        ];

        Mail::to($r->email)->send(new CertificateRequestProtocol($data, $r));

        return [
            'certificate_id' => $certificate->id,
            'request' => $r->except(['file', '_token', 'recipients', 'form', 'g-recaptcha-response', 'email_subject', 'email_template', 'service', 'tipo_envio_select', 'accordingly_checkbox', 'want_messages_checkbox', 'page_title'])
        ];
    }


    private function getCertificateSettings()
    {
        $certificateSettings = Sysmeta::where('meta_key', 'certificate_settings')->first();
        return json_decode($certificateSettings->meta_value);
    }

    public function getCorreiosDeliveryFee(Request $r)
    {
        $certificateSettings = $this->getCertificateSettings();

        if ($r->type_correios == 'carta_registrada') {
            return $certificateSettings->value_registered_letter;
        }

        $certificateSettings = $this->getCertificateSettings();
        /**
         * Dimensões da certidão
         * Peso: 0.3kg
         * Comprimento: 36cm
         * Altura: 2cm
         * Largura: 22cm
         */
        $data = [
            'tipo' => $r->type_correios, // opções: `sedex`, `sedex_a_cobrar`, `sedex_10`, `sedex_hoje`, `pac`
            'formato' => 'envelope', // opções: `caixa`, `rolo`, `envelope`
            'cep_destino' => str_replace('-', '', $r->cep), // Obrigatório
            'cep_origem' => '01501000', // Obrigatorio
            'peso' => $certificateSettings->weightKG, // kg
            'comprimento' => $certificateSettings->lengthCM, // cm
            'altura' => $certificateSettings->heightCM, // cm
            'largura' => $certificateSettings->widthCM, // cm
            'diametro' => '0', // Em cm, no caso de rolo
        ];

        $deliveryFeeDetails = \Correios::frete($data);
        $deliveryFee = $deliveryFeeDetails[0]['valor'];

        return $deliveryFee;
    }

    public function generateBoleto(Request $request)
    {
        $certificateSettings = $this->getCertificateSettings();
        $certidao = Certificate::find($request->id);

        $endereco = new Endereco();
        $endereco->setCep($certidao->cep);
        $endereco->setLogradouro($certidao->logradouro);
        $endereco->setNumero($certidao->numero);
        $endereco->setComplemento($certidao->complemento);
        $endereco->setBairro($certidao->bairro);
        $endereco->setCidade($certidao->cidade);
        $endereco->setUf($certidao->uf);

        $comprador = new Comprador($request, $endereco);
        $comprador->setNome($certidao->nome_solicitante);
        if ($certidao->tipo_solicitante === 'pj') {
            $documento = $certidao->cnpj;
        } else {
            $documento = $certidao->cpf;
        }
        $comprador->setDocumento($documento);

        $valorTotal = $certidao->valor + $certidao->valor_sedex + $certificateSettings->boleto_fee;

        if (isset($certidao->custo_midia))
            $valorTotal += $certidao->custo_midia;

        $valor = str_replace('.', '', number_format((float)$valorTotal, 2, '.', ''));

        $dadosDePagamento = Pagamento::generateBoletoRequest($certidao->id, $valor, $comprador, $certidao, ($certidao->forma_entrega === 'correios'), $certidao->custo_midia);

        $return = [
            'status' => 'success',
            'message' => '',
            'url' => $dadosDePagamento->url,
            'linha' => $dadosDePagamento->linha,
            'linha_formatada' => $dadosDePagamento->linha_formatada
        ];

        return response()->json($return, 200);
    }

    public function printCertificateProtocol(Request $r)
    {
        $certificate = Certificate::find($r->id);
        return view('print.certificate-protocol', ['certificate' => $certificate]);
    }

    private function sanitizeFileName($fileName)
    {
        $transliterator = \Transliterator::create('Any-Latin; Latin-ASCII; [\u0100-\u7fff] remove');
        $transliterated = $transliterator->transliterate($fileName);
        $transliterated = str_replace(' ', '-', $transliterated);
        $transliterated = preg_replace('/[^A-Za-z0-9.\-_]/', '', $transliterated);
        return strtolower($transliterated);
    }

    public function retornaDadosFormulario($fields)
    {
        $retorno = "";
        switch ($fields->form) {
            case "mensalistas":
                $retorno = "Mensagem: " . $fields->mensagem . "\r\n";
                break;
            case "ouvidoria":
                $retorno = "Tipo do Assunto: " . $fields->Tipo_do_Assunto . "\r\n";
                $retorno .= "Título: " . ($fields->título ?? '') . "\r\n";
                $retorno .= "Mensagem: " . ($fields->mensagem ?? '') . "\r\n";
                break;
            case "a":
                $retorno = $fields->select_name . ":" . ($fields->{str_replace(' ', '_', $fields->select_name)} ?? '')  . "\r\n";
                $retorno .= "Outros: " . ($fields->others_text ?? '') . "\r\n";
                $retorno .= "Mensagem: " . ($fields->messagem ?? '') . "\r\n";
                break;
            case "b":
            case "c":
            case "e":
                $retorno = "Mensagem: " . ($fields->mensagem ?? '') . "\r\n";
                break;
            case "d":
                $retorno = "Número do pedido: " . ($fields->Número_do_Pedido ?? '') . "\r\n";
                $retorno .= "Tipo do ato: " . ($fields->Tipo ?? '') . "\r\n";
                if (strpos($fields->Tipo, 'Documento arquivado') !== false)
                    $retorno .= "Dados do documento: " . ($fields->Dados_do_documento ?? '') . "\r\n";
                elseif (strpos($fields->Tipo, 'Busca') !== false)
                    $retorno .= "Dados para busca: " . ($fields->Dados_para_busca ?? '') . "\r\n";

                $retorno .= "Finalidade da solicitação: " . ($fields->Finalidade_da_solicitação ?? '') . "\r\n";
                $retorno .= "Partes: " . ($fields->Partes ?? '') . "\r\n";
                $retorno .= "Livro: " . ($fields->Livro ?? '') . "\r\n";
                $retorno .= "Folha: " . ($fields->Folha ?? '') . "\r\n";
                if (!empty($fields->arquivos)) {
                    $contador = 1;
                    foreach ($fields->arquivos as $arquivo) {
                        $nomeCompleto = basename($arquivo);
                        $nomeLimpo = pathinfo($nomeCompleto, PATHINFO_FILENAME);
                        $filename = $arquivo;
                        $extension = pathinfo($filename, PATHINFO_EXTENSION); // Captura a extensão
                        $siteBase = rtrim(config('app.url'), '/');
                        $rota = route('download_anexo_pedido', ['filename' => $nomeLimpo, 'ext' => $extension], false);
                        $linkFinal = $siteBase . $rota;
                        $retorno .= "<a href='" . $linkFinal . "' target='_blank'>Arquivo {$contador}:</a>\r\n";
                        $contador++;
                    }
                }
                break;
            case "f":
                $retorno = "Tem advogado?: " . ($fields->{'Tem_advogado?'} ?? '') . "\r\n";
                $retorno .= $fields->select_name . ":" . ($fields->{str_replace(' ', '_', $fields->select_name)} ?? '') . "\r\n";
                $retorno .= "Mensagem: " . ($fields->mensagem ?? '') . "\r\n";
                break;
            case "g":
                $retorno .= $fields->select_name . ":" . ($fields->{str_replace(' ', '_', $fields->select_name)} ?? '') . "\r\n";
                $retorno .= "Mensagem: " . ($fields->mensagem ?? '') . "\r\n";
                break;
            case "h":
                $retorno .= "Tipo de imóvel: " . $fields->Tipo_de_imóvel . "\r\n";
                if ($fields->Tipo_de_imóvel == "Rural")
                    $retorno .= "CCIR: " . ($fields->CCIR ?? '') . "\r\n";
                else
                    $retorno .= "Nº do Contribuinte (IPTU): " . ($fields->{'Nº_do_Contribuinte_(IPTU)'} ?? '') . "\r\n";
                $retorno .= "Valor do Negócio: " . ($fields->Valor_do_Negócio ?? '') . "\r\n";
                $retorno .= "Mensagem: " . ($fields->mensagem ?? '') . "\r\n";
                break;
            case "i":
                $retorno = "Tem advogado?: " . ($fields->{'Tem_advogado?'} ?? '') . "\r\n";
                $retorno .= "Natureza do conflito: " . ($fields->Natureza_do_conflito ?? '') . "\r\n";
                $retorno .= "Mensagem: " . ($fields->mensagem ?? '') . "\r\n";
                break;
            case "j":
                $retorno = "Tem advogado?: " . ($fields->{'Tem_advogado?'} ?? '') . "\r\n";
                $retorno .= "Tipo de inventário: " . ($fields->Tipo_de_inventário ?? '') . "\r\n";
                if ($fields->Tipo_de_inventário == "Inventário e partilha")
                    $retorno .= "Esboço da partilha (primeiras declarações): " . ($fields->{'Esboço_da_partilha_(primeiras_declarações)'} ?? '')  . "\r\n";

                $retorno .= "Mensagem: " . ($fields->mensagem ?? '')  . "\r\n";
                break;
        }
        return $retorno;
    }
}
