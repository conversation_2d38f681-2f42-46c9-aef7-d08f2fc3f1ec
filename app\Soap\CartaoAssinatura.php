<?php namespace App\Soap;

use Artisaninweb\SoapWrapper\Extension\SoapService;

class CartaoAssinatura extends SoapService {

    /**
     * @var string
     */
    protected $name = 'cartao-assinatura';

    /**
     * @var string
     */
    protected $wsdl = 'http://servico26notas.cartorios.net/CartaoAssinaturaWSImplService?wsdl';

    /**
     * @var boolean
     */
    protected $trace = true;

    /**
     * Get all the available functions
     *
     * @return mixed
     */
    public function functions()
    {
        return $this->getFunctions();
    }
}