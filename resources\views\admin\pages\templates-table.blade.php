<div class="table-responsive text-nowrap">
  <table class="table table-hover">
    <thead>
      <tr>
        <th><PERSON><PERSON><PERSON><PERSON></th>
        <th><PERSON>o</th>
        <th>Ações</th>
      </tr>
    </thead>
    <tbody class="table-border-bottom-0" id="sortable">
      @foreach ($pages as $page)
        <tr>
          <td><strong>{{ $page->title }}</strong></td>
          <td>{{ __('messages.' . $page->template) }}</td>
          <td>
            <div class="dropdown">
              <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown" aria-expanded="false"><i class="bx bx-dots-vertical-rounded"></i></button>
              <div class="dropdown-menu" style="">
                <a class="dropdown-item" href="{{ route('pages.templates.edit', [$page->id]) }}"><i class="bx bx-edit-alt me-1"></i> Edit</a>
                <form method="POST" action="{{ route('pages.templates.destroy', [$page->id]) }}">
                  @csrf
                  @method('delete')
                  <button class="dropdown-item" href="javascript:void(0);" onclick="return confirm('Tem certeza disso? Essa ação deletará o menu e seus submenus e links')"><i class="bx bx-trash me-1"></i> Delete</button>
                </form>
              </div>
            </div>
          </td>
        </tr>
      @endforeach
    </tbody>
  </table>
</div>