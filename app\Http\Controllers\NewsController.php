<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\News;
use App\Models\NewsBanners;

class NewsController extends Controller
{
  public function news()
  {
    $news = News::published()->orderBy('created_at', 'desc')->paginate(10);
    return view('news.news', ['news' => $news]);
  }

  public function showNews($slug)
  {
    $news = News::published()->where('slug', $slug)->first();

    if (empty($news))
      abort(404);

    $related = News::published()->where('category_id', $news->category_id)->whereNotIn('id', [$news->id])->take(2)->get();
    $banners = NewsBanners::all();
    return view('news.show-news', [
      'news' => $news,
      'banner1' => $banners[0],
      'banner2' => $banners[1],
      'related' => $related
    ]);
  }

  public function cadastro_newsletter($email){
    return view('news.cadastro_news', compact('email'));
  }

  public function redirectNews($slug)
  {
    return redirect()->route('news.show', ['slug' => $slug]);
  }
}
