<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PedidoCertidao extends Model
{
    use HasFactory;

    protected $casts = [
        'data_emissao_boleto' => 'datetime',
    ];

    public function descricaoStatus(){
        switch($this->status){
            case "P":
                return "Pendente";
            case "E":
                return "Elegível";
            case "I":
                return "Inelegível";
        }
    }
}
