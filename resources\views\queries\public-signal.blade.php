@extends('layouts.my-account')

@section('content')
<section class="account txtcontent">
  <div class="container-fluid">
	@if (!isset($resposta))
    <div class="px-1">
      <div class="breadcrumb">
        <a href="{{ route('my.services') }}">Meus Serviços</a>  >
        <span>Sinal Público</span>
      </div>
      <div class="d-flex justify-content-between align-items-start">
        <h1>Sinal Público</h1>
        <a href="{{ route('my.services') }}" class="btnDefault" style="background: #708082;">Voltar</a>
      </div>
    </div>
		<div class="row">
			<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">

				<p><strong class="blue">PESQUISA POR NOME DO ESCREVENTE</strong><br>Informe o nome do escrevente para verificar se possui sinal público em nosso cartório.</p>

				<form action="{{ route('queries.show.public.signal') }}" method="POST">
          @csrf
					<div class="formControl">
						<label>Informe o nome do escrevente:</label>
						<input type="text" name="sinal_nome" required>
					</div>
					<div class="d-flex">
						<button class="btnDefault"  >Enviar</button>
					</div>
				</form>

			</div>

		</div>

    @else
    <div class="px-1">
      <div class="breadcrumb">
        <a href="{{ route('my.services') }}">Meus Serviços</a>  >
        <a href="{{ route('queries.public.signal') }}">Sinal Público</a>  >
        <span>Resultado</span>
      </div>
      <div class="d-flex justify-content-between align-items-start">
        <h1>Sinal Público</h1>
        <a href="{{ route('queries.public.signal') }}" class="btnDefault" style="background: #708082;">Voltar</a>
      </div>
    </div>
      <div class="row">
        <div class="resultadoBusca resultadoBuscaAtos26">

          <div class="headBusca">
            <h2>RESULTADO DA PESQUISA POR NOME DO ESCREVENTE</h2>
          </div>

          <table class="registros">
            <tbody>
              <tr class="head">
                <th>Cidade</th>
                <th>Estado</th>
                <th>Denominação</th>
                <th>Escrevente</th>
                <th>Tabelião</th>
              </tr>
              @if ($resposta != "")
                @foreach ($resposta['results'] as $data)
                  <tr>
                    <td>{{ $data->cidade }}</td>
                    <td>{{ $data->estado }}</td>
                    <td>{{ $data->denomicacao }}</td>
                    <td>{{ $data->escrevente }}</td>
                    <td>{{ $data->tabeliao }}</td>
                  </tr>
                @endforeach
              @else
                <tr>
                  <td colspan="5">
                    <p style="text-align: center">Sem Resultado</p>
                  </td>
                </tr>
              @endif
            <tr>
          </tr></tbody></table>
        </div>
      </div>
    @endif

	</div>

</section>
@endsection
