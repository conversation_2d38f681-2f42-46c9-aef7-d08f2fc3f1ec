<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Push26;
use App\Soap\EscrituraProcuracao;
use App\Soap\CertidaoNotas;
use App\Soap\SinalPublico;
use App\Soap\Autenticidade;
use App\Soap\CartaoAssinatura;
use Illuminate\Support\Facades\Gate;

class QueriesController extends Controller
{
  public function notarialAct()
  {
    if (!Gate::allows('use_services'))
      return redirect()->route('my.acc')->withErrors([
        'error_client_cannot_use_services' => 'Queremos conhecer os nossos usuários, informe seus dados e usufrua dos nossos serviços.'
      ]);
    return view('queries.notarial-act');
  }

  public function showNotarialAct(Request $request)
  {
    $statusProtocolo = new \App\APIDasti\ConsultasAPIDasti;
    $resposta = $statusProtocolo->pesquisaSituacaoProtocolo($request->proto, $request->doc);
    $result = [];

    $resposta = json_decode($resposta);

  if ($resposta) {

    if(isset($resposta->Outorgantes) && $resposta->Outorgantes[0] != ""){
        $result['outorgantesProtocolo'] = $resposta->Outorgantes;
        unset($resposta->Outorgantes);
    }
    if(isset($resposta->Outorgados) && $resposta->Outorgados[0] != ""){
        $result['outorgadosProtocolo'] = $resposta->Outorgados;
        unset($resposta->Outorgados);
    }

    if(isset($resposta->Imoveis) && $resposta->Imoveis[0] != ""){
      $result['imoveisProtocolo'] = $resposta->Imoveis;
      unset($resposta->Imoveis);
    }

    if(isset($resposta->dataLavratura)){
        $resposta->dataLavratura = date('d/m/Y', strtotime($resposta->dataLavratura));
        unset($resposta->dataAbertura);
    }

    $result['statusProtocolo'] = $resposta;

    if(isset($resposta->message) && $resposta->message == "Nenhum item encontrado"){
        $result['statusProtocolo']->situacao = 'Incompleto';
    }
  }

    $curl = curl_init();
    curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($curl, CURLOPT_USERPWD, "26TN@pi:puGyZ2Xtm8jF08D");

    if(isset($_SERVER['HTTPS']))
      curl_setopt($curl, CURLOPT_URL, 'https://api.e-tabeliao.com.br/26TN/historicoProtocolo');
    else
      curl_setopt($curl, CURLOPT_URL, 'http://api.e-tabeliao.com.br/26TN/historicoProtocolo');

    curl_setopt($curl, CURLOPT_POST, 1);
    curl_setopt($curl, CURLOPT_HTTPHEADER, array(
      'chave: 42ACA468E34BB7142454BAF73A698',
      'Content-Type: application/json',
    ));

    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode(['protocolo' => [$request->proto]]));
    $resultAndamentos = curl_exec($curl);
    $result['andamentos'] = json_decode($resultAndamentos);
    $ultimoStatusLista = "";
    $historico_invertido = array();

    if($result['andamentos'][0]->sucesso == false){
        $result['andamentos'] = null;
    }else{
      $x = 0;
      foreach($result['andamentos'][0]->envioRI as $andamento) {
        $historico_invertido = array();
        $ultimoStatusLista = "";
        $andamento->ultimoStatus = "";
        for($i = sizeof($andamento->historico) -1; $i >= 0; $i--) {
            if(strtoupper($andamento->historico[$i]->status) == "CADASTRADO PELA CONFERÊNCIA" || $andamento->historico[$i]->status == "PROTOCOLO ABERTO" || strtoupper($andamento->historico[$i]->status) == "EM ABERTO" || $andamento->historico[$i]->status == "ATO LAVRADO E CONCLUÍDO" || $andamento->historico[$i]->status == "PROCESSANDO"  || $andamento->historico[$i]->status == "SUBSCRITO PELO TABELIÃO/SUBSTITUTO")
                continue;

            if($andamento->historico[$i]->status == "PAGAMENTO EFETIVADO")  // retorno sempre em CAIXA ALTA
                $andamento->historico[$i]->status = "PAGAMENTO EFETIVADO";

            if($andamento->historico[$i]->status == "AGUARDANDO PAGAMENTO")
                $andamento->historico[$i]->status = "PROCESSANDO PAGAMENTO";

            if(strtoupper($andamento->historico[$i]->status) == "ENVIADO AO RI")
                $andamento->historico[$i]->status = "ENVIADO AO REGISTRO IMOBILIÁRIO";

            if($andamento->historico[$i]->status == "REABERTO - NÃO CONCLUÍDO")
                $andamento->historico[$i]->status = "PROCESSANDO EXIGÊNCIA REGISTRAL";

            if($andamento->historico[$i]->status == "NOTA DE EXIGÊNCIA")
                $andamento->historico[$i]->status = "NOTA DE EXIGÊNCIA DO REGISTRO IMOBILIÁRIO";

            if($andamento->historico[$i]->status == "DEVOLVIDO")
                $andamento->historico[$i]->status = "REJEITADO PELO REGISTRO IMOBILIÁRIO";

            if($andamento->historico[$i]->status == "FINALIZADO SEM A PRÁTICA DO ATO")
                $andamento->historico[$i]->status = "FINALIZADO SEM A PRÁTICA DO ATO";

            if($andamento->historico[$i]->status == "CANCELAMENTO DA PRENOTAÇÃO")
                $andamento->historico[$i]->status = "PRENOTAÇÃO CANCELADA A PEDIDO DA PARTE";

            if($andamento->historico[$i]->status == "PRAZO SUSPENSO")
                $andamento->historico[$i]->status = "PRAZO SUSPENSO PELO REGISTRO IMOBILIÁRIO";

            if($andamento->historico[$i]->status == "REENVIADO")
                $andamento->historico[$i]->status = "REENVIADO AO REGISTRO IMOBILIÁRIO";

            if(strtoupper($andamento->historico[$i]->status) == "ATO LAVRADO E CONCLUÍDO - EMITIDO TRASLADO")
                $andamento->historico[$i]->status = "TRASLADO EMITIDO";

            if(strtoupper($andamento->historico[$i]->status) == "ATO LAVRADO EM CONFERÊNCIA")
                $andamento->historico[$i]->status = "ATO EM CONFERÊNCIA";

            if(strtoupper($andamento->historico[$i]->status) == "ATO LAVRADO - PENDÊNCIA(S) - COM O ESCREVENTE")
                $andamento->historico[$i]->status = "PENDÊNCIA COM O ESCREVENTE";

            if(strtoupper($andamento->historico[$i]->status) == "ATO LAVRADO - PENDÊNCIA(S) - NA CONFERÊNCIA")
                $andamento->historico[$i]->status = "PENDÊNCIA NA CONFERÊNCIA";

            if($ultimoStatusLista == $andamento->historico[$i]->status)
                continue;

            $ultimoStatusLista = $andamento->historico[$i]->status;

            $data = explode(' ', $andamento->historico[$i]->data)[0];
            $hora = explode(' ', $andamento->historico[$i]->data)[1];

            $andamento->historico[$i]->data = explode('-', $data)[2] . '/' . explode('-', $data)[1] . '/' . explode('-', $data)[0] . ' ' . $hora;

            if($andamento->ultimoStatus == "")
                $andamento->ultimoStatus = $andamento->historico[$i]->status;

            array_push($historico_invertido, $andamento->historico[$i]);
        }
        if($result['andamentos'] != null)
            $result['andamentos'][0]->envioRI[$x]->historico = $historico_invertido;
        $x++;
      }
    }

    $followingAct = Push26::where([
      'email' => Auth::user()->email,
      'doc' => $request->doc
    ])->first();
    $followingAct = !!$followingAct;

    return view('queries.notarial-act', [
      'act' => $result,
      'doc' => $request->doc,
      'doc_type' => $request->type,
      'followingAct' => $followingAct
    ]);
  }

  public function certificate()
  {
    if (!Gate::allows('use_services'))
      return redirect()->route('my.acc')->withErrors([
        'error_client_cannot_use_services' => 'Queremos conhecer os nossos usuários, informe seus dados e usufrua dos nossos serviços.'
      ]);

    return view('queries.certificate');
  }

  public function showCertificate(Request $r)
  {
    $andamentoCertidao = new CertidaoNotas;

    $resposta = false;
    $mensagem = '';

    $sinalPublico = new \App\APIDasti\ConsultasAPIDasti;
    $result = $sinalPublico->ConsultarCertidaoNotasPorCPFCNPJLivroFolha($r->input('doc'), $r->input('livro'), $r->input('folha'));

    $resposta = json_decode($result);

    if(isset($resposta->message)){
        $mensagem = 'Desculpe, não foram encontradas informações para o documento ' . $r->input('doc') . ' - Livro: ' . $r->input('livro') . ' e Folha: ' . $r->input('folha');
    }else{
        if (!$resposta) {
            $mensagem = 'Desculpe, não foram encontradas informações para o documento ' . $r->input('doc') . ' - Livro: ' . $r->input('livro') . ' e Folha: ' . $r->input('folha');
        }
    }

    return view('queries.certificate', [
      'resposta' => $resposta,
      'mensagem' => $mensagem
    ]);
  }

  public function publicSignal()
  {
    if (!Gate::allows('use_services'))
      return redirect()->route('my.acc')->withErrors([
        'error_client_cannot_use_services' => 'Queremos conhecer os nossos usuários, informe seus dados e usufrua dos nossos serviços.'
      ]);
    return view('queries.public-signal');
  }

  public function showPublicSignal(Request $r)
  {

      $sinalPublico = new \App\APIDasti\ConsultasAPIDasti;
      $result = $sinalPublico->consultarSinalPublico($r->input('sinal_nome'));

      $resposta = json_decode($result);

      if(isset($resposta->message) && $resposta->message == "Nenhum item encontrado"){
        $resposta = "";
      }else{
        $resposta = ['results' => $resposta];

        if (isset($resposta['results'])) {
          if (!is_array($resposta['results'])) {
            $resposta['results'] = [
              $resposta['results']
            ];
          }
        }
      }

      return view('queries.public-signal', [
        'resposta' => $resposta
      ]);
  }

  public function authenticityOfRecognition()
  {
    return view('queries.authenticity-of-recognition');
  }

  public function showAuthenticityOfRecognition(Request $request)
  {
    $request->validate([
      'g-recaptcha-response' => 'required',
    ], [
      'g-recaptcha-response' => 'Assinale a caixa "não sou um robô"!',
    ]);

      $dataReconhecimento = str_replace('-', '/', $request->input('carimbo_data'));

      $data = [
          'codigo' => $request->input('carimbo_codigo'),
          'dataReconhecimento' => $dataReconhecimento
      ];

      $autenticidade = new Autenticidade;

      $resposta = false;

      $soapResponse = $autenticidade->call('consultarCarimbo', [$data]);
      if (isset($soapResponse->result->carimbos)) {
          $resposta = $soapResponse->result->carimbos;
          $resposta->dataRec = date('d/m/Y', strtotime($resposta->data));
      }

      return view('queries.authenticity-of-recognition', [
        'resposta' => $resposta
      ]);
  }

  public function subscriptionCard() {
    if (!Gate::allows('use_services'))
      return redirect()->route('my.acc')->withErrors([
        'error_client_cannot_use_services' => 'Queremos conhecer os nossos usuários, informe seus dados e usufrua dos nossos serviços.'
      ]);
    return view('queries.subscription-card');
  }
  public function showSubscriptionCard(Request $request)
    {
        if($request->input('cartao_nome_cpf') == '0'){
            $valor = $request->input('cartao_nome');
            $resultadoText = 'RESULTADO DA PESQUISA POR NOME';
        } else {
            $valor = $request->input('cartao_cpf');
            $resultadoText = 'RESULTADO DA PESQUISA POR CPF';
        }

        $data = ['tipoPesquisa' => $request->input('cartao_nome_cpf'), 'valor' => $valor];

        $resposta = false;

        $andamentoCertidao = new \App\APIDasti\ConsultasAPIDasti;
        if($request->input('cartao_nome_cpf') == '0')
            $result = $andamentoCertidao->consultarCartaoAssinaturaPorNome($request->input('cartao_nome'));
        else
            $result = $andamentoCertidao->consultarCartaoAssinaturaPorCPF($request->input('cartao_cpf'));

        $resposta = json_decode($result);

        if ($resposta) {
            if (is_array($resposta)) {
                foreach ($resposta as $key => $resp) {
                    $resposta[$key] = $this->formatRespostaCartaoAssinatura($resp);
                }
            } else {
                $resposta = $this->formatRespostaCartaoAssinatura($resposta);
            }

            $resposta = ['results' => [$resposta]];
        }

        return view('queries.subscription-card', [
          'resposta' => $resposta,
          'resultadoText' => $resultadoText
        ]);
    }

    private function formatRespostaCartaoAssinatura($resposta)
    {
        if (isset($resposta->rg)) {
            $resposta->documento = substr($resposta->rg, 0, 3) . '...';
        } elseif (isset($resposta->cpf)) {
            $resposta->documento = substr($resposta->cpf, 0, 3) . '...';
        } else {
            $resposta->documento = 'Não informado';
        }

        unset($resposta->rg);
        unset($resposta->cpf);
        unset($resposta->dataValidade);

        return $resposta;
    }

    public function acts26()
    {
      if (!Gate::allows('use_services'))
      return redirect()->route('my.acc')->withErrors([
        'error_client_cannot_use_services' => 'Queremos conhecer os nossos usuários, informe seus dados e usufrua dos nossos serviços.'
      ]);
        return view('queries.acts-26');
    }

    public function showActs26(Request $request)
    {
      $dataObj = new \stdClass;

      if($request->input('atos_cpf_cnpj') == '0'){
        $dataObj->tipoPesquisa = 'NOME';
        $resultadoText = 'RESULTADO DA PESQUISA POR NOME';
        $dataObj->nomeoutorgante = $request->input('atos_nome_outorgante');
        $dataObj->nomeoutorgado = $request->input('atos_nome_outorgado');
      } elseif ($request->input('atos_cpf_cnpj') == '1') {
        $dataObj->tipoPesquisa = 'CPF';
        $resultadoText = 'RESULTADO DA PESQUISA POR CPF';
        $dataObj->cpfoutorgante = $request->input('atos_cpf_outorgante');
        $dataObj->cpfoutorgado = $request->input('atos_cpf_outorgado');
      } else {
        $dataObj->tipoPesquisa = 'CNPJ';
        $resultadoText = 'RESULTADO DA PESQUISA POR CNPJ';
        $dataObj->cnpjoutorgante = $request->input('atos_cnpj_outorgante');
        $dataObj->cnpjoutorgado = $request->input('atos_cnpj_outorgado');
      }

        $resposta = false;

        $livroFolha = new \App\APIDasti\ConsultasAPIDasti;

        if($request->input('atos_cpf_cnpj') == '0')
            $result = $livroFolha->pesquisarOutorganteOutorgadoPorNome($request->input('atos_nome_outorgante'), $request->input('atos_nome_outorgado'));
        elseif($request->input('atos_cpf_cnpj') == '1')
            $result = $livroFolha->pesquisarOutorganteOutorgadoPorCpfOuCnpj($request->input('atos_cpf_outorgante'), $request->input('atos_cpf_outorgado'));
        else
            $result = $livroFolha->pesquisarOutorganteOutorgadoPorCpfOuCnpj($request->input('atos_cnpj_outorgante'), $request->input('atos_cnpj_outorgado'));

        $resposta = json_decode($result);

        $result = [];


        if ($resposta) {
            if(isset($resposta->message)){
                return view('queries.acts-26', [
                    'resposta' => $result,
                    'resultadoText' => $resultadoText
                  ]);
            }

            if(!is_array($resposta) && !is_null($resposta)){
                $resposta = [$resposta];
            }

            foreach ($resposta as $key => $resp) {
               if (strtolower($resp->tipoato) == 'testamento')
                continue;

              if(strtolower($resp->situacaoDescricao) == 'sem efeito' || strtolower($resp->situacaoDescricao) == 'finalizado' || strtolower($resp->situacaoDescricao) == 'em edição' || strtolower($resp->situacaoDescricao) == 'incompleto')
                continue;

                  $stringOutorgados = '';
                  if (isset($resp->Outorgados) && is_array($resp->Outorgados)) {
                      $result[$key]['outorgadosAtos'] = $resp->Outorgados;
                      $stringOutorgados = '';
                      foreach($resp->Outorgados as $outorgado){
                          if($stringOutorgados != '')
                              $stringOutorgados .= ',';

                          $stringOutorgados .= $outorgado->nome;
                      }
                      unset($resp->Outorgados);
                  } else if(isset($resp->Outorgados)){
                      $result[$key]['outorgadosAtos'] = [$resp->Outorgados];
                      foreach($result[$key]['outorgadosAtos'] as $outorgado){
                          if($stringOutorgados != '')
                              $stringOutorgados .= ',';

                          $stringOutorgados .= $outorgado->nome;
                      }
                      unset($resp->Outorgados);
                  }

                  $stringOutorgantes = '';
                  if (isset($resp->Outorgantes) && is_array($resp->Outorgantes)) {
                      $result[$key]['outorgantesAtos'] = $resp->Outorgantes;
                      foreach($resp->Outorgantes as $outorgante){
                          if($stringOutorgantes != '')
                              $stringOutorgantes .= ',';

                          $stringOutorgantes .= $outorgante->nome;
                      }
                      unset($resp->outorgantes);
                  } else if (isset($resp->Outorgantes)) {
                      $result[$key]['outorgantesAtos'] = [$resp->Outorgantes];
                      foreach($result[$key]['outorgantesAtos'] as $outorgante){
                          if($stringOutorgantes != '')
                              $stringOutorgantes .= ',';

                          $stringOutorgantes .= $outorgante->nome;
                      }
                      unset($resp->Outorgantes);
                  }

                  $result[$key]['stringPartes'] = $stringOutorgantes .' | '. $stringOutorgados;

                  $resp->dataAtos = date('d/m/Y', strtotime($resp->dataLavratura));
                  unset($resp->dataLavratura);

                  if(!isset($resp->revogado)){
                    if (strtolower($resp->situacaoDescricao) == 'revogado') {
                        $resp->revogado = 'Sim';
                    } else {
                        $resp->revogado = 'Não';
                    }
                  }

                  $result[$key]['dadosAtos'] = $resp;
              }

        }

        return view('queries.acts-26', [
          'resposta' => $result,
          'resultadoText' => $resultadoText
        ]);
    }

    public function processaAtosLivroFolha(Request $request)
    {
        $livroFolha = new \App\APIDasti\ConsultasAPIDasti;

        $result = $livroFolha->pesquisarLivroFolha($request->input('atos_livro'), $request->input('atos_folha'));

        $resposta = json_decode($result);

        $result = [];

        if ($resposta) {
            if(isset($resposta->message) && $resposta->message == "Nenhum item encontrado"){
                return view('queries.acts-26', [
                    'resposta' => $result,
                    'resultadoText' => 'RESULTADO DA PESQUISA POR LIVRO E FOLHAS'
                  ]);
            }

            if(!is_array($resposta) && !is_null($resposta)){
                $resposta = [$resposta];
            }

            foreach ($resposta as $key => $resp) {

              if (strtolower($resp->tipoato) == 'testamento')
                continue;

              if(strtolower($resp->situacaoDescricao) == 'sem efeito' || strtolower($resp->situacaoDescricao) == 'finalizado' || strtolower($resp->situacaoDescricao) == 'em edição' || strtolower($resp->situacaoDescricao) == 'incompleto')
                continue;

                $stringOutorgados = '';
                if (isset($resp->Outorgados) && is_array($resp->Outorgados)) {
                    $result[$key]['outorgadosAtos'] = $resp->Outorgados;
                    $stringOutorgados = '';
                    foreach($resp->Outorgados as $outorgado){
                        if($stringOutorgados != '')
                            $stringOutorgados .= ',';

                        $stringOutorgados .= $outorgado->nome;
                    }
                    unset($resp->Outorgados);
                } else if(isset($resp->Outorgados)){
                    $result[$key]['outorgadosAtos'] = [$resp->Outorgados];
                    foreach($result[$key]['outorgadosAtos'] as $outorgado){
                        if($stringOutorgados != '')
                            $stringOutorgados .= ',';

                        $stringOutorgados .= $outorgado->nome;
                    }
                    unset($resp->Outorgados);
                }

                $stringOutorgantes = '';
                if (isset($resp->Outorgantes) && is_array($resp->Outorgantes)) {
                    $result[$key]['outorgantesAtos'] = $resp->Outorgantes;
                    foreach($resp->Outorgantes as $outorgante){
                        if($stringOutorgantes != '')
                            $stringOutorgantes .= ',';

                        $stringOutorgantes .= $outorgante->nome;
                    }
                    unset($resp->outorgantes);
                } else if (isset($resp->Outorgantes)) {
                    $result[$key]['outorgantesAtos'] = [$resp->Outorgantes];
                    foreach($result[$key]['outorgantesAtos'] as $outorgante){
                        if($stringOutorgantes != '')
                            $stringOutorgantes .= ',';

                        $stringOutorgantes .= $outorgante->nome;
                    }
                    unset($resp->Outorgantes);
                }

                $result[$key]['stringPartes'] = $stringOutorgantes .' | '. $stringOutorgados;

                $resp->dataAtos = date('d/m/Y', strtotime($resp->dataLavratura));
                unset($resp->dataLavratura);

                if(isset($resp->revogado)){
                    if($resp->revogado != 'N'){
                        $resp->revogado = 'Sim';
                    } else {
                        $resp->revogado = 'Não';
                    }
                } else if (strtolower($resp->situacaoDescricao) == 'revogado') {
                    $resp->revogado = 'Sim';
                } else {
                    $resp->revogado = 'Não';
                }

                $result[$key]['dadosAtos'] = $resp;
            }

        }

        return view('queries.acts-26', [
          'resposta' => $result,
          'resultadoText' => 'RESULTADO DA PESQUISA POR LIVRO E FOLHAS'
        ]);

    }
}
