@extends('layouts.admin')
@section('title', 'Colaboradores')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="fw-bold py-3 m-0">
    Colaboradores
  </h4>
  <a href="{{ route('collaborators.collaborators.create') }}" class="btn btn-primary mt-3">Novo</a>
</div>

 <div class="card">
  <h5 class="card-header">Colaboradores</h5>
  <div class="table-responsive text-nowrap">
    <table class="table table-hover">
      <thead>
        <tr>
          <th>Nome</th>
          <th>Email</th>
          <th>Tipo</th>
          <th>Ações</th>
        </tr>
      </thead>
      <tbody class="table-border-bottom-0" id="sortable">
        @foreach ($collaborators as $collaborator)
          <tr>
            <td><strong>{{ $collaborator->name }}</strong></td>
            <td><strong>{{ $collaborator->email }}</strong></td>
            <td><strong>{{ __('messages.' . $collaborator->type) }}</strong></td>
            <td>
              @if (Auth::guard('admin')->user()->can('manage_this_user', $collaborator))
              <div class="dropdown">
                <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown" aria-expanded="false"><i class="bx bx-dots-vertical-rounded"></i></button>
                <div class="dropdown-menu">
                  <a class="dropdown-item" href="{{ route('collaborators.collaborators.edit', [$collaborator->id]) }}"><i class="bx bx-edit-alt me-1"></i> Edit</a>
                  <form method="POST" action="{{ route('collaborators.collaborators.destroy', [$collaborator->id]) }}">
                    @csrf
                    @method('delete')
                    <button class="dropdown-item" href="javascript:void(0);" onclick="return confirm('Tem certeza disso? Essa ação deletará o colaborador')"><i class="bx bx-trash me-1"></i> Delete</button>
                  </form>
                </div>
              </div>
              @endif
            </td>
          </tr>
        @endforeach
      </tbody>
    </table>
  </div>
</div>
@endsection