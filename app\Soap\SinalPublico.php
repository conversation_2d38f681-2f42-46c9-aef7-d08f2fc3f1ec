<?php namespace App\Soap;

use Artisaninweb\SoapWrapper\Extension\SoapService;

class SinalPublico extends SoapService {

    /**
     * @var string
     */
    protected $name = 'sinal-publico';

    /**
     * @var string
     */
    protected $wsdl = 'http://servico26notas.cartorios.net/SinalPublicoWSImplService?wsdl';

    /**
     * @var boolean
     */
    protected $trace = true;

    /**
     * Get all the available functions
     *
     * @return mixed
     */
    public function functions()
    {
        return $this->getFunctions();
    }
}