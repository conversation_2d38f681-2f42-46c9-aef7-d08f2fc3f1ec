@extends('layouts.admin')
@section('title', 'Dúvidas')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="fw-bold py-3 m-0">
    Dúvidas
  </h4>
  <a href="{{ route('questions.q.create') }}" type="button" class="btn btn-primary">Nova</a>
</div>

 <div class="card">
  <div class="card-header">
    <div class="d-flex justify-content-between">
      <h5 class="mt-2">Dúvidas</h5>
      <div class="d-flex">
          <select class="form-select ms-3" onchange="filterCategory(this)" id="category">
            <option value="" @selected($filteredCategory == '')>Nenhuma categoria</option>
            @foreach ($categories as $category)
              <option value="{{ $category->id }}" @selected($filteredCategory == $category->id)>{{ $category->category }}</option>
            @endforeach
          </select>
      </div>
    </div>
    <div class="d-flex justify-content-between align-items-center mt-4">
      <div class="w-100">
        <label>Pesquisa por título:</label>
        <input type="text" class="form-control" id="title" placeholder="Digite o título" value="{{ request()->titulo }}" onkeyup="disableCategoryFilter()">
        <small>Ao pesquisar por título o filtro de categoria fica indisponível assim como a ordenação dos itens.</small>
      </div>
      
      <button class="btn btn-primary ms-4" onclick="search()">Buscar</button>
    </div>
  </div>
  <div class="table-responsive">
    <table class="table table-hover">
      <thead>
        <tr>
          <th>Título</th>
          <th>Categoria</th>
          <th>Ações</th>
        </tr>
      </thead>
      <tbody
        class="table-border-bottom-0"
        @if (request()->titulo == '' && request()->category != '')
        id="sortable"
        @endif
      >
        @foreach ($questions as $question)
          <tr data-id="{{ $question->id }}">
            <td style="max-width: 581px;">
              @if (request()->titulo == '' && request()->category != '')
              <i class='bx bxs-sort-alt me-3'></i>
              @endif
              <strong>{{ $question->question }}</strong>
            </td>
            <td>
              <strong>{{ $question->category->category }}</strong>
            </td>
            <td>
              <div class="dropdown">
                <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown" aria-expanded="false"><i class="bx bx-dots-vertical-rounded"></i></button>
                <div class="dropdown-menu" style="">
                  <a class="dropdown-item" href="{{ route('questions.q.edit', [$question->id]) }}"><i class="bx bx-edit-alt me-1"></i> Edit</a>
                  <form method="POST" action="{{ route('questions.q.destroy', [$question->id]) }}">
                    @csrf
                    @method('delete')
                    <button class="dropdown-item" href="javascript:void(0);" onclick="return confirm('Tem certeza disso? Essa ação excluirá essa categoria')"><i class="bx bx-trash me-1"></i> Delete</button>
                  </form>
                </div>
              </div>
            </td>
          </tr>
        @endforeach
      </tbody>
    </table>
  </div>
</div>

  <script nonce="{{ csp_nonce() }}">
    $( "#sortable" ).sortable({
      update: function( event, ui ) {
        var ids = [];
        var category = $('#category').val();

        var categories = $('#sortable').children('tr');
        for (let i = 0; i < categories.length; i++) {
          ids.push(categories.eq(i).data('id'));
        }        

        $.get("{{ route('questions.update.order') }}", {
          ids,
          category
        }, function() {
          //$(button).html('Enviado!');
        });
      }
    });

    $( ".accordion-button" ).on('click', function(){
      $(".accordion-collapse").collapse('hide');
    });

    function filterCategory(select) {
      var cat = $(select).val();

      var url = new URL(window.location.href);
      url.searchParams.set('category', cat);
      url.searchParams.delete('titulo');
      window.location = url.href;
    }

    function disableCategoryFilter() {
      if ($('#title').val() != '') {
        if ($('#category').val('') != '') {
          $('#sortable').hide();
        }
        $('#category').val('');
        $('#category').attr('disabled', true);
      } else {
        $('#category').removeAttr('disabled');
      }
    }

    function search()
    {
      var url = new URL(window.location.href);
      url.searchParams.delete('category');
      url.searchParams.set('titulo', $('#title').val());
      window.location = url.href;
    }
  </script>
@endsection