<?php

namespace App\Services\Bradesco;

use Carbon\Carbon;

class Boleto {

    public $beneficiario;
    public $carteira = 26;
    public $nosso_numero;
    public $data_emissao;
    public $data_vencimento;
    public $valor_titulo;
    public $url_logotipo = 'https://www.26notas.com.br/img/logo.png';
    public $mensagem_cabecalho = '26º Tabelionato de Notas';
    public $tipo_renderizacao = 2;
    public $instrucoes;

    public function __construct($valor, $certidao = null, $sedex = false, $custo_midia = false, $protocoloAto = '')
    {
        date_default_timezone_set('America/Sao_Paulo');
		$date = new \DateTime('+2 day');
        $this->data_emissao = date('Y-m-d');
        $this->data_vencimento = $date->format('Y-m-d');
        $this->beneficiario = '26. TABELIAO DE NOTAS DA CAPITAL';
        $this->valor_titulo = $valor;
        $idParaNumero = !empty($certidao) ? $certidao->id : random_int(100,200);
        $this->nosso_numero = $idParaNumero . random_int(1000000,9000000);
        $this->url_logotipo = env('URL_LOGOTIPO_BRADESCO', 'https://www.26notas.com.br/img/logo.png');
        $instrucoes = new \stdClass();

        $propCusto = 'instrucao_linha_7';

        if(!empty($certidao)) {
            $instrucoes->instrucao_linha_1 = 'Data do pedido: ' . date('d/m/Y', strtotime($certidao->created_at));
            $instrucoes->instrucao_linha_2 = 'Data da Entrega: ' . date('d/m/Y', strtotime($certidao->created_at .  ' + 4 days'));
            $instrucoes->instrucao_linha_3 = 'Numero do pedido de certidao: ' . $certidao->id;
            $instrucoes->instrucao_linha_4 = 'Valor da certidao: R$' . number_format($certidao->valor, 2, ',', '.');
            if($sedex) {
                $instrucoes->instrucao_linha_5 = 'Valor do frete: R$' . number_format($certidao->valor_sedex, 2, ',', '.');
            }
            if($custo_midia) {
                $instrucoes->instrucao_linha_6 = 'Opção digital R$' . number_format($certidao->custo_midia, 2, ',', '.');
            }
        }

        if(!empty($protocoloAto) && empty($certidao)) {
            $instrucoes->instrucao_linha_1 = 'Vencimento D + 2 dias da emissão.';
            $instrucoes->instrucao_linha_2 = 'Ref.: Ato notarial do 26º Tabelionato de Notas';
            $instrucoes->instrucao_linha_3 = 'Protocolo ' . $protocoloAto;
            $instrucoes->instrucao_linha_4 = 'A lavratura do ato notarial está condicionada à compensação';
            $instrucoes->instrucao_linha_5 = 'deste boleto.';
            $instrucoes->instrucao_linha_6 = 'No caso de depósito prévio está condicionada ao pagamento';
            $instrucoes->instrucao_linha_7 = 'integral do ato';
            $propCusto = 'instrucao_linha_8';
        }

        $instrucoes->{$propCusto} = 'Custo de emissao do boleto R$' . number_format('2.45', 2, ',', '.');
        $this->instrucoes = $instrucoes;
        $this->registro = null;
    }

}
