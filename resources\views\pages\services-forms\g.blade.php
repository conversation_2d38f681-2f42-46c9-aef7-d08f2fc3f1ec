<div id="form-anchor"></div>
<form id="a" class="includePage" action="{{ route('forms.send') . '#anchor' }}" method="POST" enctype="multipart/form-data" style="position: relative">
  @csrf
    <input type="hidden" name="form" value="g">
    <input type="hidden" value="{{ $page->title }}" name="page_title">
    <input type="hidden" value="{{ $form->settings->select_name }}" name="select_name">
  <input type="hidden" name="título" value="{{ $form->title }}">
  <input type="hidden" name="recipients" value="{{ $form->recipients }}">
  <input type="hidden" name="email_template" value="{{ $form->email_template }}">
  <input type="hidden" name="service" value="{{ $page->title }}">
  <input type="hidden" name="bitrix" value="{{ $form->tipo_bitrix ?? "" }}">

	<div class="formControl">
		<label>Nome <PERSON>mple<PERSON></label>
		<input type="text" name="nome" value="{{ old('nome') }}" required>
	</div>
	<div class="formControl">
		<label>Telefone</label>
		<input type="text" name="telefone" value="{{ old('telefone') }}" required>
	</div>
	<div class="formControl">
		<label>E-mail</label>
		<input type="text" name="email" value="{{ old('email') }}" required>
	</div>
	<div class="formControl">
		<label>Algum escrevente já lhe atende?</label>
		<div class="typeRadio">
			<input type="radio" id="sim" class="radioEsc" name="Algum escrevente já lhe atende?" value="sim" @checked(old('Algum_escrevente_já_lhe_atende?') == 'sim')>
			<label for="sim">Sim</label><br>
		</div>
		<div class="typeRadio">
			<input type="radio" id="nao" class="radioEsc" name="Algum escrevente já lhe atende?" value="não" @checked(old('Algum_escrevente_já_lhe_atende?') == 'não')>
			<label for="nao">Não</label><br>
		</div>
	</div>
  <div class="formControl" id="escrevente"  @if(old('Algum_escrevente_já_lhe_atende?') != 'sim') style="display:none;" @endif>
    <label>Escrevente</label>
    <input type="hidden" name="escrevente_email" id="escrevente_email">
    <select name="Escrevente" id="escreventeSelect" onchange="escreventeSelected(this)">
      <option value="">Selecione...</option>
      @foreach ($collaboratorOccupations as $occupation)
        @if (in_array($occupation->id, $form->settings->areas))
          <optgroup label="{{ $occupation->name }}">
            @foreach ($occupation->collaborators as $collaborator)
              <option value="{{ $collaborator->id }}" data-email="{{ $collaborator->email }}" @selected(old('Escrevente') == $collaborator->id)>{{ $collaborator->name }}</option>
            @endforeach
          </optgroup>

        @endif
      @endforeach
    </select>
  </div>

  <div class="formControl">
    <label>{{ $form->settings->select_name }}</label>
    <select name="{{ $form->settings->select_name }}" required>
      <option value="">Selecione...</option>
      @php
        $options = array_filter(explode(';', $form->settings->select_options));
      @endphp
      @foreach ($options as $option)
        <option value="{{ $option }}" @selected(old($form->settings->select_name) == $option)>{{ $option }}</option>
      @endforeach
    </select>
  </div>

	<div class="formControl file">
		<label>Envio de documento</label>
    <input type="file" name="file[]" style="color:black;" multiple>
	</div>
	<div class="formControl">
		<label>Mensagem</label>
		<textarea name="mensagem" id="" cols="30" rows="10" required>{{ old('mensagem') }}</textarea>
	</div>
	<div class="formControl check mb-1">
    <input type="checkbox" id="accordingly_checkbox" name="accordingly_checkbox" required>
    <label for="accordingly_checkbox">Li, estou de acordo com a <a href="/politica-de-privacidade" target="_blank">política de privacidade</a></label>
  </div>
  <div class="formControl check mt-1">
    <input type="checkbox" id="want_messages_checkbox" name="want_messages_checkbox">
    <label for="want_messages_checkbox">Desejo receber mensagens informativas do 26º Tabelionato de Notas.</label>
  </div>
	<div class="reCaptcha">
		{!! htmlFormSnippet() !!}
	</div>
	<div class="formControl">
		<button class="btnDefault red">Enviar</button>
	</div>

  <div id="anchor"></div>
	@if(session('form_success'))
    <div class="message success">
      <p>Formulário enviado com sucesso!</p>
      <p>Obrigado por usar os serviços do 26.</p>
      <p>Em breve faremos contato para tratar a sua solicitação.</p>
    </div>
  @endif

  @if(session('form_error') || $errors->any())
    <div class="message error">
      <p>Formulário não enviado.</p>
      @if ($errors->any())
        <ul class="mt-2">
          @foreach ($errors->all() as $error)
            <li style="color:#a94442;">{{ $error }}</li>
          @endforeach
        </ul>
      @endif
    </div>
  @endif
</form>

<script nonce="{{ csp_nonce() }}">
  $(document).on('change', '.radioEsc', function() {
    if (this.value == 'sim') {
      $('#escrevente').show();
    } else {
      $('#escrevente').hide();
    }
  });
  function escreventeSelected()
  {
    var yourSelect = document.getElementById( "escreventeSelect" );
    var email = $(yourSelect.options[ yourSelect.selectedIndex ]).data('email');
    $('#escrevente_email').val(email);
  }

</script>
