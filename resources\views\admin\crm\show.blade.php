@extends('layouts.admin')
@section('title', 'CRM 26 Notas / Informações')

@section('content')
<style type="text/css">
    .btnEditar{font-size: 0.8em; cursor:pointer; font-weight: normal; text-decoration: underline;}
    .campo_representante, .campo_dados{display: none;}
</style>
<script type="text/javascript">
    function exibeAlteracaoRepresentante(){
        $(".dados_representante").hide();
        $(".campo_representante").show('fast');
    }

    function cancelaAlteracaoRepresentante(){
        $(".dados_representante").show('fast');
        $(".campo_representante").hide();
    }

    function confirmaAlteracaoRepresentante(){
        if(confirm('Confirma alteração dos dados do representante?')){
            $('#formAlteracaoRepresentante').submit();
        }
    }

    function exibeAlteracaoDados(){
        $(".dados_cliente").hide();
        $(".campo_dados").show('fast');
    }

    function cancelaAlteracaoDados(){
        $(".dados_cliente").show('fast');
        $(".campo_dados").hide();
    }

    function confirmaAlteracaoDados(){
        if(confirm('Confirma alteração dos dados do cliente?')){
            $('#formAlteracaoDados').submit();
        }
    }
</script>
<h4 class="fw-bold py-3 mb-4">
  <span class="text-muted fw-light">Clientes / </span> <b>Informações</b>
</h4>

<div class="card mt-4">
  <div class="card-header">
    <h5><span class="txt_verde">Dados do Cliente</span> <span class="btnEditar dados_cliente" onclick="exibeAlteracaoDados();">editar</span>
        <span class="btnEditar campo_dados" onclick="cancelaAlteracaoDados();">cancelar</span>
        <span class="btnEditar campo_dados" onclick="confirmaAlteracaoDados();">confirmar</span></h5>
  </div>
  <div class="card-body">
    <form action="{{route('admin.crm.alterar_dados', $form->id)}}" method="POST" id="formAlteracaoDados">
        @csrf
        @method("PUT")
        <div class="row">
            <div class="mb-4 col-sm-3">
                <b>NOME</b><br />
                <input value="{{$fields->nome ?? ''}}" name="nome" class="form-control campo_dados" type="text" />
                <span class="dados_cliente">{{$fields->nome}}</span>
            </div>
            <div class="mb-4 col-sm-2">
                <b>CPF/CNPJ</b><br />
                <input value="{{$fields->CPF ?? $fields->CNPJ ?? '-'}}" name="cpf_cnpj" class="form-control campo_dados" type="text" />
                <span class="dados_cliente">{{$fields->CPF ?? $fields->CNPJ ?? '-'}}</span>
            </div>
            <div class="mb-4 col-sm-2">
                <b>NASCIMENTO</b><br />
                <input value="{{$fields->data_nascimento ?? '-'}}" name="data_nascimento" class="form-control campo_dados" type="text" />
                <span class="dados_cliente">{{$fields->data_nascimento ?? '-'}}</span>
            </div>
            <div class="mb-4 col-sm-2">
                <b>SEXO</b><br />
                <select name="sexo" class="form-select campo_dados">
                    <option @selected(($fields->sexo ?? '') == "")>-</option>
                    <option @selected(($fields->sexo ?? '') == "Masculino")>Masculino</option>
                    <option @selected(($fields->sexo ?? '') == "Feminino")>Feminino</option>
                    <option @selected(($fields->sexo ?? '') == "Não quis declarar")>Não quis declarar</option>
                </select>
                <span class="dados_cliente">{{$fields->sexo ?? '-'}}</span>
            </div>
            <div class="mb-4 col-sm-3">
                <b>PROFISSÃO</b><br />
                <input value="{{$fields->profissao ?? '-'}}" name="profissao" class="form-control campo_dados" type="text" />
                <span class="dados_cliente">{{$fields->profissao ?? '-'}}</span>
            </div>
        </div>
        <div class="row">
            <div class="mb-4 col-sm-5">
                <b>E-MAIL</b><br />
                <input value="{{$fields->email ?? '-'}}" name="email" class="form-control campo_dados" type="text" />
                <span class="dados_cliente">{{$fields->email ?? '-'}}</span>
            </div>
            <div class="mb-4 col-sm-2">
                <b>ESTADO CIVIL</b><br />
                <select name="estado_civil" class="form-select campo_dados">
                    <option @selected($fields->estado_civil ?? '' == "")>-</option>
                    <option @selected($fields->estado_civil ?? '' == "Solteiro")>Solteiro</option>
                    <option @selected($fields->estado_civil ?? '' == "Casado")>Casado</option>
                    <option @selected($fields->estado_civil ?? '' == "Separado")>Separado</option>
                    <option @selected($fields->estado_civil ?? '' == "Divorciado")>Divorciado</option>
                    <option @selected($fields->estado_civil ?? '' == "Viúvo")>Viúvo</option>
                    <option @selected($fields->estado_civil ?? '' == "União Estável")>União Estável</option>
                    <option @selected($fields->estado_civil ?? '' == "Desquitada")>Desquitada</option>
                    <option @selected($fields->estado_civil ?? '' == "Outros")>Outros</option>
                </select>
                <span class="dados_cliente">{{$fields->estado_civil ?? '-'}}</span>
            </div>
            <div class="mb-4 col-sm-2">
                <b>TELEFONE</b><br />
                <input value="{{$fields->telefone ?? '-'}}" name="telefone" class="form-control campo_dados" type="text" />
                <span class="dados_cliente">{{$fields->telefone ?? '-'}}</span>
            </div>
            <div class="mb-4 col-sm-3">
                <b>CARGO</b><br />
                <input value="{{$fields->cargo ?? '-'}}" name="cargo" class="form-control campo_dados" type="text" />
                <span class="dados_cliente">{{$fields->cargo ?? '-'}}</span>
            </div>
        </div>
        <div class="row">
            <div class="mb-4 col-sm-5">
                <b>ENDEREÇO</b><br />
                <input value="{{$fields->endereco ?? '-'}}" name="endereco" class="form-control campo_dados" type="text" />
                <span class="dados_cliente">{{$fields->endereco ?? '-'}}</span>
            </div>
            <div class="mb-4 col-sm-2">
                <b>EMPRESA</b><br />
                <input value="{{$fields->nome_empresa_contato ?? '-'}}" name="nome_empresa_contato" class="form-control campo_dados" type="text" />
                <span class="dados_cliente">{{$fields->nome_empresa_contato ?? '-'}}</span>
            </div>
            <div class="mb-4 col-sm-5">
                <b>RAZÃO SOCIAL</b><br />
                <input value="{{$fields->razao_social ?? '-'}}" name="razao_social" class="form-control campo_dados" type="text" />
                <span class="dados_cliente">{{$fields->razao_social ?? '-'}}</span>
            </div>
        </div>
        <hr />
        <div class="row">
            <div class="mb-4 col-sm-4 txt_verde">
                <b>Dados consolidados</b>
            </div>
        </div>
        <div class="row">
            <div class="mb-4 col-sm-3">
                <b>QTD DE ATOS DO CPF/CNPJ</b><br />
                {{$protocolos->count()}}
            </div>
            <div class="mb-4 col-sm-2">
                <b>TICKET MÉDIO</b><br />
                @php
                    $valorTotal = 0;
                    foreach($protocolos as $protocolo)
                        $valorTotal += json_decode($protocolo->fields)->valorTotal;
                @endphp
                @if($protocolos->count() > 0)
                    R$ {{number_format($valorTotal / $protocolos->count(), 2, ',', '.') }}
                @else
                    R$ 0,00
                @endif
            </div>
            <div class="mb-4 col-sm-5">
                <b>CNPJ VINCULADOS</b><br />
                @foreach($cnpjsVinculados as $cnpj)
                    {{$cnpj->nome_empresa_contato}}<br />
                @endforeach
            </div>
            <div class="mb-4 col-sm-2">
                <b>CRM</b><br />
                {{$totalCardsCRM}} cards
            </div>
        </div>
    </form>
    <div class="row">
        <div class="mb-4 col-sm-3 txt_verde">
            <b>Escrevente</b>
        </div>
        <div class="mb-4 col-sm-8">
            <span class="txt_verde"><b>Representante</b></span> <span class="btnEditar dados_representante" onclick="exibeAlteracaoRepresentante();">editar</span>
            <span class="btnEditar campo_representante" onclick="cancelaAlteracaoRepresentante();">cancelar</span>
            <span class="btnEditar campo_representante" onclick="confirmaAlteracaoRepresentante();">confirmar</span>
        </div>
    </div>
        <form action="{{route('admin.crm.alteracao_representante', $form->id)}}" method="POST" id="formAlteracaoRepresentante">
            <div class="row">
                <div class="mb-4 col-sm-3">
                    <b>ESCREVENTE</b><br />
                    @foreach($escreventes as $escrevente)
                        {{$escrevente->escrevente}}<br />
                    @endforeach
                </div>
                    @csrf
                    <div class="mb-4 col-sm-2">
                        <b>REPRESENTANTE</b><br />
                        <input value="{{$fields->nome_representante ?? ''}}" name="nome_representante" class="form-control campo_representante" type="text" />
                        <span class="dados_representante">{{$fields->nome_representante ?? ''}}</span>
                    </div>
                    <div class="mb-4 col-sm-2">
                        <b>TELEFONE</b><br />
                        <input value="{{$fields->telefone_representante ?? ''}}" name="telefone_representante" class="form-control campo_representante" type="text" />
                        <span class="dados_representante">{{$fields->telefone_representante ?? ''}}</span>
                    </div>
                    <div class="mb-4 col-sm-5">
                        <b>E-MAIL</b><br />
                        <input value="{{$fields->email_representante ?? ''}}" name="email_representante" class="form-control campo_representante" type="email" />
                        <span class="dados_representante">{{$fields->email_representante ?? ''}}</span>
                    </div>
            </div>
    </form>
    <br /><br /><br />
    <div class="row">
        <div class="mb-4 col-sm-4 txt_verde">
          <b>Atos Realizados</b>
        </div>
    </div>
    <div class="row">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                <tr>
                    <th>PROTOCOLO</th>
                    <th>ÚLTIMA ATUALIZAÇÃO</th>
                    <th>STATUS</th>
                    <th>VALOR TOTAL</th>
                    <th>VALOR TOTAL DOS EMOLUMENTOS</th>
                </tr>
                </thead>
                <tbody class="table-border-bottom-0">
                    @foreach($protocolos as $protocolo)
                        <tr>
                            <td>{{json_decode($protocolo->fields)->protocolo ?? ''}}</td>
                            <td>{{json_decode($protocolo->fields)->ultimoStatusData ?? ''}} - {{json_decode($protocolo->fields)->ultimoStatus ?? ''}}</td>
                            @isset(json_decode($protocolo->fields)->envioEmailTraslado)
                                @if(json_decode($protocolo->fields)->envioEmailTraslado == "OK")
                                    <td>E-mail enviado</td>
                                @else
                                    <td class="txt_vermelho">Erro envio e-mail<a href="{{route('traslado.envia_emails_traslado_individual', $protocolo->id)}}"><i class="bx bx-envelope"></i></a></td>
                                @endif
                            @else
                                <td>-</td>
                            @endif
                            <td>R$ {{number_format(json_decode($protocolo->fields)->valorTotal, 2, ',', '.') ?? ''}}</td>
                            <td>R$ {{number_format(json_decode($protocolo->fields)->valorTotalEmolumentos, 2, ',', '.') ?? ''}}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
  </div>
</div>

<br><br><br>

@endsection
