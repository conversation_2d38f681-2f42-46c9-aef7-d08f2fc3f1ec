<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Push26;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class Push26Controller extends Controller
{

  /* FUNÇÃO ORIGINAL
  public function followProtocol(Request $r)
  {
    foreach ($r->registry as $reg => $status) {
      $push = new Push26;
      $push->email = Auth::user()->email;
      $push->doc = $r->doc;
      $push->registry = $reg;
      $push->doc_type = $r->doc_type;
      $push->protocol = $r->protocol;
      $push->last_status = $status;
      $push->save();
    }
  }
*/




public function followProtocol(Request $r)
{

  $protocolo = explode('|', $r->protocol);
  $protocol = $protocolo[0];

  $codInterno = array();
  foreach ($r->cod_interno as $key => $value) {
    array_push($codInterno, $value);
  }

  $count = 0;
  foreach ($r->registry as $reg => $status) {
    $push = new Push26;
    $push->email = Auth::user()->email;
    $push->doc = $r->doc;
    $push->registry = preg_replace('/<span[^>]*>.*?<\/span>/', '', $reg);
    $push->doc_type = $r->doc_type;

      $push->protocol = $protocol . '|' . $codInterno[$count];
      $push->last_status = $status;
      $push->save();


      // Adicione logs para rastrear o que está acontecendo
      /*
      Log::info('Novo Push26 criado:', [
        'email' => $push->email,
        'doc' => $push->doc,
        'registry' => $push->registry,
        'doc_type' => $push->doc_type,
        'protocol' => $push->protocol,
        'last_status' => $push->last_status,
      ]);
      */

      $count++;
  }
}


  /* ORIGINAL

  public function unfollowProtocol(Request $r)
  {
    $email = Auth::user()->email;
    Push26::where([
      'email' => $email,
      'protocol' => $r->protocol
    ])->delete();
  }
*/

public function unfollowProtocol(Request $r)
{
    $email = Auth::user()->email;

    // Verifica se há um pipe na string do protocolo
    if (strpos($r->protocol, '|') !== false) {
        // Se sim, extrai o código antes do pipe
        $protocolo_cod_interno = explode('|', $r->protocol)[0];
    } else {
        // Se não, usa a string completa como está
        $protocolo_cod_interno = $r->protocol;
    }

    // Exclui os registros com o email e que começam com o código do protocolo
    Push26::where([
        'email' => $email,
    ])->where('protocol', 'like', $protocolo_cod_interno . '|%')->delete();
}




  public function show()
  {
    $email = Auth::user()->email;
    $registers = Push26::where('email', $email)->get();
    return view('clients.push26', [
      'registers' => $registers
    ]);
  }






  public function runCronJob()
  {

    $itens_push = Push26::where('last_status', '!=', 'REGISTRADO/AVERBADO')->get();
    $protocolos = array();


    foreach($itens_push as $item){

      $protocolo_Unico = $item['protocol'];
      $protocolo_Unico = explode('|', $protocolo_Unico);

      $protocolo_Unico[0] = ltrim($protocolo_Unico[0], '0');

      if (!in_array($protocolo_Unico[0], $protocolos)) {
        // Adicionar o protocolo ao array
        array_push($protocolos, $protocolo_Unico[0]);
      }

    }



    if(count($protocolos) == 0)
      exit("Sem protocolos para verificar!");

    $curl = curl_init();

    curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($curl, CURLOPT_USERPWD, "26TN@pi:puGyZ2Xtm8jF08D");

    if(isset($_SERVER['HTTPS']))
      curl_setopt($curl, CURLOPT_URL, 'https://api.e-tabeliao.com.br/26TN/historicoProtocolo');
    else
      curl_setopt($curl, CURLOPT_URL, 'http://api.e-tabeliao.com.br/26TN/historicoProtocolo');

    curl_setopt($curl, CURLOPT_POST, 1);
    curl_setopt($curl, CURLOPT_HTTPHEADER, array(
      'chave: 42ACA468E34BB7142454BAF73A698',
      'Content-Type: application/json',
    ));

    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode(['protocolo' => $protocolos]));

    $resultAndamentos = curl_exec($curl);
    $andamentos = json_decode($resultAndamentos);


    foreach($itens_push as $item){


      $_protocol = (object)['protocol' => $item->protocol];

        // Usar explode para dividir a string no caractere '|'
        $protocolo_parts = explode('|', $item->protocol);

        // Pegar apenas o primeiro elemento (antes do '|')
        $protocolo_busca = $protocolo_parts[0];

        $protocolo_busca = ltrim($protocolo_busca, "0"); //////// aquiiiii
        $protocolo_cod_interno = $protocolo_parts[1];


      foreach($andamentos as $andamento){

        if($andamento->protocolo != $protocolo_busca)
        // if($andamento->protocolo != $protocolos[0])
          continue;


        foreach($andamento->envioRI as $ri){

          if($ri->codigo_interno != $protocolo_cod_interno)
          // if($ri->cartorio != $item->registry)
            continue;

            $ultimo_status = $ri->historico[sizeof($ri->historico) -1]->status;


           if($ultimo_status == "PROTOCOLO ABERTO" || $ultimo_status == "ATO LAVRADO E CONCLUÍDO - EMITIDO TRASLADO" || $ultimo_status == "ATO LAVRADO EM CONFERÊNCIA" || $ultimo_status == "Em aberto" || $ultimo_status == "ATO LAVRADO E CONCLUÍDO" || $ultimo_status == "Processando" || $ultimo_status == "Nota de Exigência")
              continue;

            if($ultimo_status == "Devolvido")
              $ultimo_status = "Rejeitado pelo Registro Imobiliário";

            if($ultimo_status == "Finalizado Sem A Prática do Ato")
              $ultimo_status = "Finalizado sem a prática do ato";

            if($ultimo_status == "Cancelamento da Prenotação")
              $ultimo_status = "Prenotação cancelada a pedido da parte";

            if($ultimo_status == "Prazo Suspenso")
              $ultimo_status = "Prazo suspenso pelo Registro Imobiliário";

            if($ultimo_status == "Aguardando Pagamento")
              $ultimo_status = "Processando pagamento";

            if($ultimo_status == "Pagamento Efetivado")
              $ultimo_status = "Pagamento efetivado";

            if($ultimo_status != "" && $ultimo_status != $item->last_status){
              $proximo_status = "";
              $ultimo_status_envio = "";

              if($ultimo_status == "ATO LAVRADO EM CONFERÊNCIA"){
                $ultimo_status_envio = "Ato em conferência 😀";
                $proximo_status = "Emissão do traslado";
              }

              if($ultimo_status == "Enviado ao RI"){
                $ultimo_status_envio = "Enviado ao Registro Imobiliário 😀";
                $proximo_status = "Prenotação (" . $item->protocol . ")";
              }

              if($ultimo_status == "PRENOTADO"){
                $ultimo_status_envio = "Prenotado 😀<br />⚠️ <span style='color: red; font-weight: normal;'>O prazo de registro é de 30 dias.</span>";
                $proximo_status = "Registrado/Averbado<br />Obs.: A próxima etapa é o registro. Contudo, em razão da qualificação registral imobiliária, pode haver alguma solicitação de complemento documental ou de valor para o registro. Mas não se preocupe, o escrevente fará as providências necessárias.";
              }

              if($ultimo_status == "REGISTRADO/AVERBADO"){
                $ultimo_status_envio = "Registrado/Averbado ✅<br /><p>😀 <span style='color: red; font-weight: normal;'>O escrevente fará contato para entrega da escritura e do registro.</span></p>";
                $proximo_status = "-";
              }

              $data = Carbon::now()->timezone('America/Sao_Paulo');
              $protocolo = $item->protocol;

              $protocolo = explode('|', $protocolo);
              $protocolo[0] = ltrim($protocolo[0], '0');
              $protocolo = $protocolo[0];


              $email = $item->email;
              $dados = [
                'protocolo' => $protocolo . " [" . $ri->codigo_interno . "] ",
                'ultimo_status' => $ultimo_status_envio,
                'proximo_status' => $proximo_status,
                'cartorio' => $ri->cartorio,
                'data_hora' => $data->format('d/m/Y') . ' às ' . $data->format('H:i:s')
              ];
              try{
                if($proximo_status != ""){
                  $subject = 'Push 26 Notas - Atualização do protocolo ' . $protocolo . " [" . $ri->codigo_interno . "] " . ' - ' . $data->format('d/m/Y');
                  \Mail::send('emails.push26', $dados, function($message) use ($protocolo, $email, $subject) {
                    $message->to($email)->subject($subject);
                  });
                }

                $item->last_status = $ultimo_status;
                $item->save();
                //sleep(1); // TODO: REMOVEEEEE!!!!

                echo "<pre>";
                print_r($dados);
                echo "</pre>";


              }catch(\Exception $e){
                echo 'protocolo ->>>>>>>>>> ' . $item->protocol . PHP_EOL;
                echo "<span style='color:red; font-weight: bold;'>Ocorreu um erro no envio do e-mail para " . $item->email . ' referente ao protocolo ' . $item->protocol . '</span><br />' . $e;
                throw new \Exception($e);
              }
            }



        }



      }


    }
  }
}
