@extends('layouts.admin')
@section('title', 'Categorias')

@section('content')
<h4 class="fw-bold py-3 mb-4">
  <span class="text-muted fw-light">Dúvidas / </span><span class="text-muted fw-light">Categorias /</span> Nova
</h4>

<form method="POST" action="{{ route('questions.categories.update', [$category->id]) }}">
  @csrf

  <div class="card" >
    <h5 class="card-header">Categoria</h5>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-5">
          <label class="form-label">Titulo</label>
          <input type="text" class="form-control @error('category') is-invalid @enderror" placeholder="Digite o título" name="category" value="{{ $category->category }}" required>
        </div>
      </div>
    </div>
  </div>

  <button class="btn btn-primary mt-3"><PERSON>var</button>
</form>



<script nonce="{{ csp_nonce() }}">

</script>
@endsection