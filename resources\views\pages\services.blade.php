@extends('layouts.app')
@section('title', $page->title)

@section('content')
<section class="contentServico txtcontent">
	
	<div class="container-fluid">
		@if($page->restricted_access)
      <div class="px-1 mb-3 d-flex justify-content-between align-items-start">
        <div class="breadcrumb">
          <a href="{{ route('my.services') }}">Meus Serviços</a>  >
          <span>{{ $page->title }}</span>
        </div>
        <a href="{{ route('my.services') }}" class="btnDefault" style="background: #708082;">Voltar</a>
      </div>
    @endif
		<div class="row">
			
			<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12">

				<h1>{{ $page->title }}</h1>

        @foreach (json_decode($page->template_settings) as $session)
          <p>
            @if($session->type == 'content')
              @if($session->title)
                @if($session->red_title && $session->highlighted_title)
                  <h2 class="red" id="{{ $session->title }}">{{ $session->title }}</h2>
                @elseif ($session->red_title)
                  <p class="red" id="{{ $session->title }}">{{ $session->title }}</p>
                @elseif ($session->highlighted_title)
                  <h2 id="{{ $session->title }}">{{ $session->title }}</h2>
                @else
                  <strong id="{{ $session->title }}">{{ $session->title }}</strong><br>
                @endif
              @endif

              @if ($session->content_block)
                <div class="boxBgGray">
                  {!! $session->content !!}
                </div>
              @else
                <div>
                  {!! $session->content !!}
                </div>
              @endif
            @endif

            @if($session->type == 'numered_box')
            <p>
               @if($session->red_title && $session->highlighted_title)
                  <h2 class="red">{{ $session->title }}</h2>
                @elseif ($session->red_title)
                  <p class="red">{{ $session->title }}</p>
                @elseif ($session->highlighted_title)
                  <h2>{{ $session->title }}</h2>
                @else
                  <strong>{{ $session->title }}</strong>
                @endif</p>
              <div class="blocosNumber row">
                @foreach ($session->texts as $text)
                    <div class="col">
                      <div class="blocoNumber">
                        <div class="icone">{{ $loop->iteration }}</div>
                        <p>{{ $text->title }}</p>
                      </div>
                    </div>
                @endforeach
              </div>
            @endif

            @if ($session->type == 'image_box')
              <div class="row listAtalhos">
                @foreach ($session->blocks as $block)
                  <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                    <div class="blocoAtalhoDefault bgGray">
                      <div class="iconeAtalho">
                        <img src="{{ asset('storage/' . $block->file_name) }}" style="max-width: 133px;">
                      </div>
                      <div class="infoAtalho">
                        <p><strong>{{ $block->title }}</strong><br>
                          {{ $block->desc }}</p>
                        <a href="{{ $block->link }}" class="btnLink">{{ $block->text_link }}</a>
                      </div>
                    </div>
                  </div>
                @endforeach
              </div>
            @endif

            @if($session->type == 'faq')
              @if($session->red_title && $session->highlighted_title)
                <h2 class="red">Dúvidas Frequentes</h2>
              @elseif ($session->red_title)
                <p class="red">Dúvidas Frequentes</p>
              @elseif ($session->highlighted_title)
                <h2>Dúvidas Frequentes</h2>
              @else
                <strong>Dúvidas Frequentes</strong><br>
              @endif
              <div class="col">
                  <div class="tabs">
                    @foreach ($session->questions as $question)
                      @php
                        $question = App\Models\Question::find($question->id);
                      @endphp
                      @if (!empty($question))
                        <div class="tab">
                          <input type="checkbox" id="chck{{ $loop->iteration }}">
                          <label class="tab-label" for="chck{{ $loop->iteration }}">{{ $question->question }}</label>
                          {{--  <div class="tab-content">{!! $question->answer !!}</div> --}}
                          <div class="tab-content">{!! strip_tags($question->answer, '<br><strong><em><a><b><i>') !!}</div>
                        </div>
                      @endif
                    @endforeach
                  </div>
                </div>
            @endif

            @if ($session->type == 'files')
              @if($session->title)
                  @if($session->red_title && $session->highlighted_title)
                    <h2 class="red" id="{{ $session->title }}">{{ $session->title }}</h2>
                  @elseif ($session->red_title)
                    <p class="red" id="{{ $session->title }}">{{ $session->title }}</p>
                  @elseif ($session->highlighted_title)
                    <h2 id="{{ $session->title }}">{{ $session->title }}</h2>
                  @else
                    <strong id="{{ $session->title }}">{{ $session->title }}</strong><br>
                  @endif
                @endif
              <div class="downloads">
                @foreach ($session->files as $file)
                  <a href="{{ route('pages.templates.file.download', ['file' => $file]) }}">
                    <i class="download"></i>
                    {{ $file->name }}
                  </a>
                @endforeach
              </div>
            @endif

            
          </p>
        @endforeach

        @if ($form->show)
          <h2 class="red" id="form">{{ $form->title }}</h2>
          @include('pages.services-forms.' . $form->model)
        @endif
        

			</div>

			<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12">
				<div class="boxWidgetSidebar">
					<p class="red"><strong>Contratar</strong></p>
          @foreach (json_decode($page->template_settings) as $session)
            @if (isset($session->add_sidebar) && $session->add_sidebar)
              <a href="#{{$session->title}}">{{ $session->title }}</a><br>
            @endif
          @endforeach
          @if ($form->show)
					  <a href="#form-anchor" class="btnDefault red">Fazer pedido online</a>
          @endif
				</div>
			</div>

		</div>

	</div>

</section>

@endsection