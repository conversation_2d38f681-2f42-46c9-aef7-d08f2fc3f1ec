<?php

namespace App\Http\Controllers;

use App\Models\Collaborator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\Document;
use App\Models\Client;
use Illuminate\Support\Facades\Mail;
use App\Mail\SendDocumentWarning;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Gate;

class DocumentController extends Controller
{
    public function index(Request $r)
    {
        if (!Gate::allows('use_services'))
            return redirect()->route('my.acc')->withErrors([
                'error_client_cannot_use_services' => 'Queremos conhecer os nossos usuários, informe seus dados e usufrua dos nossos serviços.'
            ]);

        $client = Auth::user();

        $documents = Document::where('client_id', $client->id);

        if (!empty($r->status)) {
            $status = $r->status;

            $documents->where(function ($query) use ($status) {
                if ($status == 'closed') {
                    $query->where('status', '=', $status)
                        ->orWhere('status', '=', 'dropped');
                } else {
                    $query->where('status', '=', $status);
                }

            });

        }

        $documents = $documents->orderBy('id')->get();

        return view('documents.index', [
            'client' => $client,
            'documents' => $documents
        ]);
    }

    public function create()
    {
        $client = Auth::user();
        $clerks = Collaborator::whereIn('type', ['clerk', 'notary', 'substitute_notary'])->orderBy('name')->get();

        return view('documents.create', [
            'client' => $client,
            'clerks' => $clerks
        ]);
    }

    public function store(Request $r)
    {

        $validator = Validator::make($r->all(), [
            'name' => 'required',
            'phone' => 'required',
            'email' => 'required',
            'cpf' => 'required',
            'doc_type' => 'required',

            'is_part' => 'required',
            'part_name' => 'required_if:is_part,0',
            'part_cpf' => 'required_if:is_part,0',
            'clerk' => 'required',
            'more_files' => 'required',

            'documents' => 'required|max:15', // TODO: remove post_max_size and upload_max_filesize limits from server
        ]);

        $totalMB = 0;
        $documents = $r->file('documents');
        foreach ($documents as $file) {
            $sizeMB = $file->getSize() / 1024 / 1024;
            $totalMB += number_format($sizeMB, 2);
        }

        if ($totalMB > 64) {
            $validator->errors()->add('total_files_size', 'Os documentos enviados não podem exceder 64MB');
        }

        if ($validator->errors()->any()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }


        $client = Client::where('email', Auth::user()->email)->first();
        $client->name = $r->name;
        $client->phone = $r->phone;
        $client->cpf = $r->cpf;
        $client->save();


        $documents = $this->documentsStorageSave($documents);
        $status = ($r->more_files) ? 'opened' : 'closed';

        $document = new Document;
        $document->client_id = $client->id;
        $document->document_type = $r->doc_type;
        $document->documents = json_encode($documents);
        $document->is_part = $r->is_part;
        $document->part_name = $r->part_name;
        $document->part_cpf = $r->part_cpf;
        $document->clerk_id = $r->clerk;
        $document->message = $r->message;
        $document->status = $status;
        $document->log = NULL;
        $document->save();

        //$subject = 'Um novo envio de documentos de Thomaz recebido';
        //$subjectClient = 'Thomaz documentos enviados com sucesso';
        $this->sendWarningTo('clerk', $document, 'new');
        $this->sendWarningTo('client', $document, 'new');

        return redirect()->route('documents.index');
    }

    public function documentsStorageSave($documents)
    {
        $files = [];
        foreach ($documents as $file) {

            $fileName = $file->getClientOriginalName();
            $fileNameWithoutExtension = pathinfo($fileName, PATHINFO_FILENAME);

            $fileName = $this->sanitizeFileName($fileNameWithoutExtension);
            $timestamp = Carbon::now()->format('d-m-Y_H\h_i\m');
            $extension = $file->getClientOriginalExtension();

            $hashName = $fileName . "_" . $timestamp . "_" . "." . $extension;

            $path = $file->storeAs('documents', $hashName);

            $data = [
                'file_name' => $hashName,
                'mime_type' => $file->getClientMimeType(),
                'hash_name' => $hashName,
                'path' => $path,
                'size' => $file->getSize(),
                'sent_at' => now()->format('Y-m-d H:i:s')
            ];

            $files[] = $data;
        }

        return $files;
    }

    private function sanitizeFileName($fileName)
    {
        $transliterator = \Transliterator::create('Any-Latin; Latin-ASCII; [\u0100-\u7fff] remove');
        $transliterated = $transliterator->transliterate($fileName);
        $transliterated = str_replace(' ', '-', $transliterated);
        $transliterated = preg_replace('/[^A-Za-z0-9.\-_]/', '', $transliterated);

        return strtolower($transliterated);
    }

    public function documentDownload(Request $r)
    {
        $path = storage_path('app/' . $r->path);
        $name = $r->file_name;
        return response()->download($path, $name);
    }

    public function edit($id)
    {
        $doc = Document::find($id);
        $remainingMB = $this->getRemainingMB($doc);


        return view('documents.edit', [
            'doc' => $doc,
            'remainingMB' => $remainingMB
        ]);
    }

    private function getRemainingMB(Document $doc)
    {
        $usedMB = 0;
        $documents = json_decode($doc->documents);
        foreach ($documents as $file) {
            $sizeMB = $file->size / 1024 / 1024;
            $usedMB += number_format($sizeMB, 2);
        }
        $remainingMB = floor(64 - $usedMB);
        return $remainingMB;
    }

    public function update(Request $r, $id)
    {
        $doc = Document::find($id);
        $documentsQtt = 15 - count(json_decode($doc->documents));
        $validator = Validator::make($r->all(), [
            'more_files' => 'required',
            'documents' => 'max:' . $documentsQtt
        ]);

        $totalMB = 0;
        $documents = $r->file('documents') ?? [];
        foreach ($documents as $file) {
            $sizeMB = $file->getSize() / 1024 / 1024;
            $totalMB += number_format($sizeMB, 2);
        }

        $remainingMB = $this->getRemainingMB($doc);
        if ($totalMB > $remainingMB) {
            $validator->errors()->add('total_files_size', 'Os documentos enviados não podem exceder ' . $remainingMB . 'MB');
        }

        if ($validator->errors()->any()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        if (!empty($documents)) {
            $documents = $this->documentsStorageSave($documents);
            $documents = array_merge(json_decode($doc->documents), $documents);
            $doc->documents = $documents;
            $doc->log = $this->addToLog($doc, 'O responsável enviou novos arquivos.');
        }

        if ($r->more_files) {
            $status = 'opened';
        } else {
            $status = 'closed';
            $doc->log = $this->addToLog($doc, 'O responsável fechou o protocolo.');
        }
        $doc->status = $status;
        $doc->save();

        if ($doc->status == 'closed' || !empty($documents)) {
            // $subject = 'Envio de documentos de Thomaz atualizado';
            // $subjectClient = 'Thomaz documentos enviados com sucesso';

            $this->sendWarningTo('clerk', $doc, 'update');
            $this->sendWarningTo('client', $doc, 'update');
        }

        return redirect()->route('documents.index');
    }

    private function sendWarningTo($to, Document $doc, $subjectType)
    {
        if ($to == 'client') {
            $email = $doc->client->email;
        } else {
            $email = $doc->clerk->email;
        }
        Mail::to($email)->send(new SendDocumentWarning($doc, $to, $subjectType));
    }

    public function cancel($id)
    {
        $doc = Document::find($id);
        $doc->log = $this->addToLog($doc, 'O responsável cancelou o protocolo.');
        $doc->status = 'canceled';
        $doc->save();
        return redirect()->route('documents.index');
    }

    private function addToLog($doc, $msg)
    {
        $log = json_decode($doc->log);
        $log[] = [
            'date' => Carbon::now()->format('Y-m-d H:i:s'),
            'message' => $msg,
            'by' => 'client'
        ];
        return json_encode($log);
    }
}
