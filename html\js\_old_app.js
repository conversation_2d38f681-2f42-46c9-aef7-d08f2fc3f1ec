$(function(){
	var header = $("header"); 

	if ($(window).width() >= 940) {
		$(window).scroll(function() {
			var scroll = $(window).scrollTop();
			if (scroll > 1) {
  	    header.addClass("header-fixed");
			} else {
		    header.removeClass("header-fixed");
			}
		});
	}
  
  // Verificar se a página está no topo
	// para caso necessário adicionar a classe no menu
	if (window.pageYOffset === 0) {
		// página no topo, não executa nada
	 } else {
		header.addClass("header-fixed");
	 }

	if($(window).width() <= 1040){
		$('.hasSubMenu > a').attr('href', '#');
		$('.hasSubMenu a').click(function(){
			$(this).addClass('active');
		});
		$('.closeSubMenu').click(function(){
			$('.hasSubMenu a.active').removeClass('active');
		});
		$('.open-menu').click(function(){
			$('.hasSubMenu a.active').removeClass('active');
		});
	}

	

  		/* cookie lgpd */
		// Verifica se o cookie ja foi aceito
		function checkCookie() {
			var cookieAccepted = getCookie('cookie-modal-lgpd', { secure: true, sameSite: 'strict' });
			if (cookieAccepted !== 'true') {
			showBanner();
			}
		}
		
		// Exibe o banner
		function showBanner() {
			var banner = document.createElement('div');
			banner.classList.add('cookie-info-banner');
			banner.innerHTML = '<div>Este site utiliza cookies para proporcionar uma melhor experiência de utilização. Você pode aceitar os cookies ou saber mais sobre o seu uso acessando a nossa <a href="/politica-de-privacidade" class="btnLink">política de privacidade</a>.</div>';
			
			var acceptButton = document.createElement('button');
			acceptButton.innerHTML = 'X';
			acceptButton.addEventListener('click', acceptCookies);
		
			banner.appendChild(acceptButton);
			document.body.appendChild(banner);
		}
		
		// Aceita os cookies
		function acceptCookies() {
			setCookie('cookie-modal-lgpd', 'true', 365);
			var banner = document.querySelector('.cookie-info-banner');
			banner.remove();
		}
		
		
		// Define um cookie com uma data de expiracao na sessao
		function setCookie(name, value) {
			document.cookie = name + '=' + value + '; path=/';
		}
		
		// Obtem o valor de um cookie
		function getCookie(name) {
			var nameEQ = name + '=';
			var ca = document.cookie.split(';');
			for (var i = 0; i < ca.length; i++) {
			var c = ca[i];
			while (c.charAt(0) === ' ') c = c.substring(1, c.length);
			if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
			}
			return null;
		}
		
		// Verifica se os cookies foram aceitos ao carregar a pagina
		checkCookie();

});


$(document).ready(function () { 
  backgrounds = $('[data-background]');
  for (let index = 0; index < backgrounds.length; index++) {
    const element = backgrounds.eq(index);
    element.css('background-image', 'url('+element.data('background')+')');
  }

$('#cpfcnpj').mask('00.000.000/0000-00', {
  onKeyPress : function(cpfcnpj, e, field, options) {
    const masks = ['000.000.000-000', '00.000.000/0000-00'];
    const mask = (cpfcnpj.length > 14) ? masks[1] : masks[0];
    $('#cpfcnpj').mask(mask, options);
  }
});

  $("#cpf").mask('000.000.000-00', {reverse: false});
  $(".cpf").mask('000.000.000-00', {reverse: false});
  $(".phone-mask").mask('(00) 0000-0000');
  $('.cel-mask').mask('(00) 00000-0000');
  $('.cep').mask('00000-000');
  $('.money').mask('000.000.000.000.000,00', {reverse: true});

  /**
   * Coloca loading ao enviar qualquer form
   */
  var forms = $("form");
  for (var i = 0; i < forms.length; i++) {
    forms.eq(i).on("submit", function(event){
      var loading = '<div class="spinner"></div>';
      $(this).find('button').html(loading);
    });
  }

  /**
   * Set jQuery validation defaults
   */
  jQuery.extend(jQuery.validator.messages, {
    required: "Este campo &eacute; obrigatório.",
    remote: "Por favor, corrija este campo.",
    email: "Por favor, forne&ccedil;a um endere&ccedil;o eletr&ocirc;nico v&aacute;lido.",
    url: "Por favor, forne&ccedil;a uma URL v&aacute;lida.",
    date: "Por favor, forne&ccedil;a uma data v&aacute;lida.",
    dateISO: "Por favor, forne&ccedil;a uma data v&aacute;lida (ISO).",
    number: "Por favor, forne&ccedil;a um n&uacute;mero v&aacute;lido.",
    digits: "Por favor, forne&ccedil;a somente d&iacute;gitos.",
    creditcard: "Por favor, forne&ccedil;a um cart&atilde;o de cr&eacute;dito v&aacute;lido.",
    equalTo: "Por favor, forne&ccedil;a o mesmo valor novamente.",
    accept: "Por favor, forne&ccedil;a um valor com uma extens&atilde;o v&aacute;lida.",
    maxlength: jQuery.validator.format("Por favor, forne&ccedil;a n&atilde;o mais que {0} caracteres."),
    minlength: jQuery.validator.format("Por favor, forne&ccedil;a ao menos {0} caracteres."),
    rangelength: jQuery.validator.format("Por favor, forne&ccedil;a um valor entre {0} e {1} caracteres de comprimento."),
    range: jQuery.validator.format("Por favor, forne&ccedil;a um valor entre {0} e {1}."),
    max: jQuery.validator.format("Por favor, forne&ccedil;a um valor menor ou igual a {0}."),
    min: jQuery.validator.format("Por favor, forne&ccedil;a um valor maior ou igual a {0}.")
  });
  jQuery.validator.setDefaults({
    errorClass: "validation-error"
  });
  
});

function jumpTo(to)
{
  var top = $(to)[0].offsetTop; //Getting Y of target element
  window.scrollTo(0, top); 
}
