@extends('layouts.admin')
@section('title', 'Men<PERSON>')

@section('content')
<h4 class="fw-bold py-3 mb-4">
  <span class="text-muted fw-light">Menus / </span><span class="text-muted fw-light">Cabeçalho /</span> Novo
</h4>

<form method="POST" action="{{ route('menus.header.store') }}">
  @csrf

  <div class="card" x-data="menuComponent(@js(old('type', 'slug')), @js(old('slug')), @js(old('link')))">
    <h5 class="card-header">Menu</h5>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-2">
          <label class="form-label">Tipo</label>
          <select class="form-select" name="type" x-model="type" required>
            <option value="slug">slug</option>
            <option value="link">link</option>
          </select>
        </div>
        <div class="mb-3 col-sm-5">
          <label class="form-label">Titulo</label>
          <input type="text" class="form-control @error('title') is-invalid @enderror" placeholder="Digite o título" name="title" value="{{ old('title') }}" required>
        </div>
        <template x-if="type == 'slug'">
          <div class="mb-3 col-sm-5">
            <label class="form-label">Slug</label>
            <input type="text" class="form-control @error('slug') is-invalid @enderror" placeholder="agende-seu-servico" name="slug" x-model="slug"  x-on:keyup="filterSlug" x-on:blur="removeLastHyphen()" required>
          </div>
        </template>

        <template x-if="type == 'link'">
          <div class="mb-3 col-sm-5">
            <label class="form-label">Link</label>
            <input type="text" class="form-control @error('link') is-invalid @enderror" placeholder="https://26notas.com.br/" name="link" x-model="link" x-on:keyup="filterLink" required>
            <template x-if="!$store.isValidLink && link.length > 0">
              <div class="invalid-feedback d-block">Link inválido</div>
            </template>
            <template x-if="$store.isValidLink">
              <div class="valid-feedback d-block">Link válido</div>
            </template>
          </div>
        </template>
      </div>
    </div>
  </div>

  <div id="sortable" x-data="submenuComponent(@js(old('submenus', [])), @js($errors->messages()))">
    <template x-for="(submenu, index) in submenus">
      <div class="card my-4">
        <div class="card-body">
          <div class="d-flex justify-content-between mb-1">
            <div class="d-flex">
              <i class='bx bxs-sort-alt cursor-move me-2'></i>
              <h5>Submenu</h5>
            </div>
            <i class='bx bx-trash text-danger cursor-pointer' x-on:click="remove(index)"></i>
          </div>
          <div class="row">
            <div class="mb-3 col-sm-6">
              <label class="form-label">Titulo</label>
              <input type="text" class="form-control" :class="{'is-invalid': hasErrors(index, 'title')}" placeholder="Digite o título" x-model="submenu.title" :name="getInputName(index, 'title')" required>
            </div>
            <div class="mb-3 col-sm-6">
              <label class="form-label">Slug</label>
              <input type="text" class="form-control" :class="{'is-invalid': hasErrors(index, 'slug')}" placeholder="agende-seu-servico" x-model="submenu.slug" :name="getInputName(index, 'slug')" x-on:keyup="filterSlug(index)" x-on:blur="removeLastHyphen(index)" required>
            </div>
          </div>
        </div>
      </div>
    </template>
    <div class="d-flex justify-content-center mt-4">
      <a x-on:click="add" class="cursor-pointer">Adicionar Submenu</a>
    </div>
  </div>

  <button class="btn btn-primary mt-3">Salvar</button>
</form>



<script nonce="{{ csp_nonce() }}">
  $( "#sortable" ).sortable();

  var regexSlug = /^[a-z0-9]+(\-{1}[a-z0-9]+)*(\-)?$/;
  
  document.addEventListener('alpine:init', () => {
    Alpine.store('isValidLink', false);
  });
  
  function menuComponent(initialType, initialSlug = '', initialLink = '') {
    return {
      type: initialType,
      slug: initialSlug,
      link: initialLink,
      filterLink() {
        var link = this.link;
        var reg = /^(?:http(s)?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\(\)\*\+,;=.]+$/;

        if (!reg.test(link)) {
          this.$store.isValidLink = false;
        } else {
          this.$store.isValidLink = true;
        }
      },
      filterSlug() {
        var slug = this.slug;

        if (!regexSlug.test(slug)) {
          this.slug = slug.slice(0, -1);
        }
      },
      removeLastHyphen() {
        var slug = this.slug;

        if (slug.substr(slug.length - 1) == '-') {
          this.slug = slug.slice(0, -1);
        }
      }
    }
  };

  function submenuComponent(initialSubmenus = [], validationErrors = []) {
    return {
      submenus: initialSubmenus,
      errors: validationErrors,
      add() {
        this.submenus.push({
          title: '',
          slug: ''
        });
      },
      remove(index) {
        this.submenus.splice(index, 1);
      },
      getInputName(index, input) {
        return 'submenus[' + index + '][' + input + ']';
      },
      hasErrors(index, input) {
        if (this.errors['submenus.' + index + '.' + input ]) {
          return true;
        }
        return false;
      },
      filterSlug(index) {
        var slug = this.submenus[index].slug;

        if (!regexSlug.test(slug)) {
          this.submenus[index].slug = slug.slice(0, -1);
        }
      },
      removeLastHyphen(index) {
        var slug = this.submenus[index].slug;

        if (slug.substr(slug.length - 1) == '-') {
          this.submenus[index].slug = slug.slice(0, -1);
        }
      }
    }
  }
</script>
@endsection