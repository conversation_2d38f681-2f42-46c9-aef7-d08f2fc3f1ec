<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class Collaborator extends Authenticatable
{
  use HasFactory, Notifiable;

  public function occupations()
  {
    return $this->belongsToMany(CollaboratorOccupation::class, 'collaborator_has_occupation', 'collaborator_id', 'occupation_id');
  }
}
