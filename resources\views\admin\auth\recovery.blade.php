@extends('components.admin.header')
@section('title', 'Recuperar Senha')

@section('head')
  <link rel="stylesheet" href="{{ asset('/css/page-auth.css') }}" />
@endsection

<body>

  <div class="container-xxl">

    <div class="authentication-wrapper authentication-basic container-p-y">

      <div class="authentication-inner">

        <div class="card">

          <div class="card-body">

            <div class="app-brand justify-content-center">

              <a href="index.html" class="app-brand-link gap-2">

                <img src="{{ asset('img/logo.png') }}" alt="26Notas">

              </a>

            </div>

            <h4 class="mb-4 text-center">Recuperar Senha</h4>

            <form id="formAuthentication" class="mb-3" action="{{ route('admin.recovery.email') }}" method="POST">
              @csrf

              

              <div class="mb-3">

                <label for="email" class="form-label">E-mail</label>

                <input type="email" class="form-control" id="email" name="email" placeholder="Digite seu e-mail" autofocus required />

              </div>

              @error('email')
                <div class="mb-4 mt-1">
                  <span style="color: red;">{{ $message }}</span>
                </div>
              @enderror

              @if(session('success'))
                <div class="alert alert-primary" role="alert">
                  {{ session('success') }}
                </div>
              @endif

              <div class="mb-3">

                <button class="btn btn-primary d-grid w-100" type="submit">Recuperar Senha</button>

              </div>
              <a href="{{ route('admin.login') }}">
                <small>Fazer Login</small>
              </a>

            </form>

          </div>

        </div>

      </div>

    </div>

  </div>

  <x-admin.footer />
