@extends('layouts.my-account')
@section('title', 'Atos Feitos no 26')

@section('content')
<section class="account txtcontent">
  <div class="container-fluid">
    @if (!isset($resposta))
    <div class="px-1">
      <div class="breadcrumb">
        <a href="{{ route('my.services') }}">Meus Serviços</a>  >
        <span>Atos Feitos no 26</span>
      </div>
      <div class="d-flex justify-content-between align-items-start">
        <h1>Atos Feitos no 26</h1>
        <a href="{{ route('my.services') }}" class="btnDefault" style="background: #708082;">Voltar</a>
      </div>
    </div>
		<div class="row">
			<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">

				<p><strong class="blue">CONSULTE OS ATOS FEITOS NO 26</strong><br>
          Faça uma pesquisa de um ato notarial. Escolha se deseja realizar a pesquisa com as informações do outorgante/outorgado ou por livro/folha.<br>
        <strong>Atenção:</strong> o ato pode conter anotação à sua margem. A certidão é a forma jurídica de conhecer a validade e/ou eficacia do ato. </p>
        <hr>
        <p><strong>Pesquisa por outorgante e/ou outorgado</strong><br>
          Localize atos lavrados no 26 por nome completo do outorgante ou do outorgado. Por CPF ou CNPJ do outorgante e do outorgado.</p>
				<form action="{{ route('queries.show.acts.26') }}" method="POST">
          @csrf
          <div class="formControl">
            <label for="">Pesquisar por:</label>
						<select name="atos_cpf_cnpj" id="atos_cpf_cnpj" class="form-control" onchange="toggleFields()">
              <option value="0" selected="">Nome</option>
              <option value="1">CPF</option>
              <option value="2">CNPJ</option>
            </select>
					</div>
					<div class="formControl" id="nome">
						<label>Informe o nome do Outorgante:</label>
						<input type="text" name="atos_nome_outorgante" placeholder="Digite o nome exato" id="atos_nome_outorgante" value="{{ old('atos_nome_outorgante') }}">

            <label>Informe o nome do Outorgado:</label>
						<input type="text" name="atos_nome_outorgado" placeholder="Digite o nome exato" id="atos_nome_outorgado" value="{{ old('atos_nome_outorgado') }}">
					</div>
          <div class="formControl" id="cpf_fields" style="display: none;">
						<label>Informe o CPF do Outorgante:</label>
						<input type="text" name="atos_cpf_outorgante" id="atos_cpf_outorgante" value="{{ old('atos_cpf_outorgante') }}">
            <label>Informe o CPF do Outorgado:</label>
						<input type="text" name="atos_cpf_outorgado" id="atos_cpf_outorgado" value="{{ old('atos_cpf_outorgado') }}">
					</div>
          <div class="formControl" id="cnpj" style="display: none;">
						<label>Informe o CNPJ do Outorgante:</label>
						<input type="text" name="atos_cnpj_outorgante" id="atos_cnpj_outorgante" value="{{ old('atos_cnpj_outorgante') }}">
            <label>Informe o CNPJ do Outorgado:</label>
						<input type="text" name="atos_cnpj_outorgado" id="atos_cnpj_outorgado" value="{{ old('atos_cnpj_outorgado') }}">
					</div>

          @if(session('form_error') || $errors->any())
            <div class="message error">
              <p>Formulário não enviado.</p>
              @if ($errors->any())
                <ul class="mt-2" style="font-size: 15px;">
                  @foreach ($errors->all() as $error)
                    <li style="color:#a94442;">{{ $error }}</li>
                  @endforeach
                </ul>
              @endif
            </div>
          @endif

					<div class="d-flex">
						<button class="btnDefault">Enviar</button>
					</div>
				</form>
        <hr style="margin-top: 32px;">
        <p><strong>Pesquisa por Livro/Folha</strong><br>
          Digite o número do livro e da folha inicial impresso no ato notarial.</p>
        <form action="{{ route('queries.show.acts.26.lf') }}" method="POST">
          @csrf
          <div class="formControl">
						<label>Livro:</label>
						<input type="text" name="atos_livro" required>
            <label>Folha:</label>
						<input type="text" name="atos_folha" required>
					</div>
          <div class="d-flex">
						<button class="btnDefault">Enviar</button>
					</div>
        </form>
			</div>

		</div>

    @else
    <div class="px-1">
      <div class="breadcrumb">
        <a href="{{ route('my.services') }}">Meus Serviços</a>  >
        <a href="{{ route('queries.acts.26') }}">Atos Feitos no 26</a>  >
        <span>Resultado</span>
      </div>
      <div class="d-flex justify-content-between align-items-start">
        <h1>Atos Feitos no 26</h1>
        <a href="{{ route('queries.acts.26') }}" class="btnDefault" style="background: #708082;">Voltar</a>
      </div>
    </div>

      <div class="row">
        <div class="resultadoBusca resultadoBuscaAtos26">

          <div class="headBusca">
            <h2>{{ $resultadoText ?? '' }}</h2>
          </div>

          <table class="registros">
            <tbody>
              <tr class="head">
                <th>Selecione</th>
                <th>Tipo</th>
                <th>Protocolo</th>
                <th>Outorgante(s)</th>
                <th>Outorgado(s)</th>
                <th>Lavratura</th>
                <th>Livro</th>
                <th>Folha</th>
                <th>Revogado</th>
                <th>Situação do ato</th>
              </tr>
              @if (!empty($resposta))
                @foreach ($resposta as $respost)
                  <tr>
                    <td>
                      <input
                        type="radio"
                        name="selectActRadio"
                       {{-- @if($loop->first) checked @endif --}}
                        data-partes="{{$respost['stringPartes']}}"
                        data-tipo="{{$respost['dadosAtos']->tipoato}}"
                        data-livro="{{$respost['dadosAtos']->livro}}"
                        data-folha="{{$respost['dadosAtos']->folha}}"
                        onchange="actRadioChanged(this)"
                      >
                    </td>
                    <td>{{ $respost['dadosAtos']->tipoato }}</td>
                    <td>{{ $respost['dadosAtos']->protocolo }}</td>
                    <td>
                      <ul>
                        @foreach ($respost['outorgantesAtos'] as $outorgante)
                        <li>{{ $outorgante->nome ?? 'Nenhum' }}</li>
                        @endforeach
                      </ul>
                    </td>
                    <td>
                      <ul>
                        @foreach ($respost['outorgadosAtos'] ?? ['Nenhum'] as $outorgado)
                        <li>{{ $outorgado->nome ?? 'Nenhum' }}</li>
                        @endforeach
                      </ul>
                    </td>
                    <td>{{ $respost['dadosAtos']->dataAtos }}</td>
                    <td>{{ $respost['dadosAtos']->livro }}</td>
                    <td>{{ $respost['dadosAtos']->folha }}</td>
                    <td>{{ $respost['dadosAtos']->revogado }}</td>
                    <td>{{ $respost['dadosAtos']->situacaoDescricao }}</td>
                  </tr>
                @endforeach
                <tr>
                  <td colspan="9" class="d-flex">
                    <a class="btnDefault" target="_blank" id="requestCertificate" href="">Solicitar Certidão</a>
                  </td>
                </tr>
              @else
                <tr>
                  <td colspan="9" style="text-align: left">
                    <div class="message error">
                      <p>Atos não disponíveis</p>
                      <p>Mesmo que não tenha havido resposta para esta consulta, o ato buscado pode existir no 26º tabelionato. Estão disponíveis no sistema apenas os atos lavrados a partir de <strong>02/01/2001</strong>. Os atos lavrados de <strong>01/06/1964</strong> a <strong>31/12/2000</strong> devem ser solicitados através de formulário específico, informando os nomes das partes, livro e folha. <a href="../servicos/pedido-de-certidao-notarial" title="Clique aqui para solicitar" style="color:#000">Clique aqui para solicitar</a>.</p>
                    </div>
                  </td>
                </tr>
              @endif
            <tr>
          </tr></tbody></table>
        </div>
      </div>
    @endif

	</div>

</section>

<script nonce="{{ csp_nonce() }}">
  actRadioChanged($('input[name="selectActRadio"]').eq(0));


  function toggleFields() {
    var type = $('#atos_cpf_cnpj').val();
    if (type == '0') {
      $('#nome').show();

      $('#nome').children('input').attr('required', true);


      $('#cpf_fields').hide();
      $('#cpf_fields').children('input').attr('required', false);

      $('#cnpj').hide();
      $('#cnpj').children('input').attr('required', false);
    } else if (type == '1') {
      $('#nome').hide();

      $('#nome').children('input').attr('required', false);

      $('#cnpj').hide();
      $('#cnpj').children('input').attr('required', false);

      $('#cpf_fields').show();
      $('#cpf_fields').children('input').attr('required', true);
    } else {
      $('#cnpj').show();

      $('#nome').children('input').attr('required', false);

      $('#cnpj').children('input').attr('required', true);

      $('#cpf_fields').hide();
      $('#cpf_fields').children('input').attr('required', false);

      $('#nome').hide();
    }

    $('#cpf_fields').children('input').change(function(){
        $('#cpf_fields').children('input').attr('required', $("#atos_cpf_outorgante").val().trim() == '' && $("#atos_cpf_outorgado").val().trim() == '');
    })

    $('#cnpj').children('input').change(function(){
        $('#cnpj').children('input').attr('required', $("#atos_cnpj_outorgante").val().trim() == '' && $("#atos_cnpj_outorgado").val().trim() == '');
    })

    $('#nome').children('input').change(function(){
        $('#nome').children('input').attr('required', $("#atos_nome_outorgante").val().trim() == '' && $("#atos_nome_outorgado").val().trim() == '');
    })
  };

  function actRadioChanged(radio)
  {
    radio = $(radio);
    var partes = radio.data('partes');
    var tipo = radio.data('tipo');
    var livro = radio.data('livro');
    var folha = radio.data('folha');
    var url = '/servicos/pedido-de-certidao-notarial?partes='+partes+'&tipo='+tipo+'&livro='+livro+'&folha='+folha+'#form'
    $('#requestCertificate').attr('href', url);
  }

  $( document ).ready(function() {
    setTimeout(() => {
      toggleFields();
    }, 1);
  })

</script>
@endsection
