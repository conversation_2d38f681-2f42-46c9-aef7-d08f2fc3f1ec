@extends('components.admin.header')
@section('title', 'Admin Login')

@section('head')
  <link rel="stylesheet" href="{{ asset('/css/page-auth.css') }}" />
@endsection

<body>

  <div class="container-xxl">

    <div class="authentication-wrapper authentication-basic container-p-y">

      <div class="authentication-inner">

        <div class="card">

          <div class="card-body">

            <div class="app-brand justify-content-center">

              <a href="index.html" class="app-brand-link gap-2">

                <img src="{{ asset('img/logo.png') }}" alt="26Notas">

              </a>

            </div>

            <h4 class="mb-4 text-center">Acessar</h4>

            <form id="formAuthentication" class="mb-3" action="{{ route('admin.auth') }}" method="POST">
              @csrf

              

              <div class="mb-3">

                <label for="email" class="form-label">E-mail</label>

                <input type="email" class="form-control" id="email" name="email" placeholder="digite seu e-mail" autofocus required />

              </div>

              <div class="mb-3 form-password-toggle">

                <div class="d-flex justify-content-between">

                  <label class="form-label" for="password">Senha</label>

                  <a href="{{ route('admin.recovery') }}">
                    <small>Esqueceu a senha?</small>
                  </a>

                </div>

                <div class="input-group input-group-merge">

                  <input type="password" id="password" class="form-control" name="password" placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;" aria-describedby="password" required/>

                </div>

              </div>

              <div class="mb-3">

                <div class="form-check">

                  <input class="form-check-input" type="checkbox" id="remember-me" />

                  <label class="form-check-label" for="remember-me"> Lembrar acesso </label>

                </div>

              </div>

              @error('email')
                <div class="mb-4 mt-1">
                  <span style="color: red;">{{ $message }}</span>
                </div>
              @enderror

              @if(session('success'))
                <div class="alert alert-primary" role="alert">
                  {{ session('success') }}
                </div>
              @endif

              <div class="mb-3">

                <button class="btn btn-primary d-grid w-100" type="submit">Entrar</button>

              </div>

            </form>

          </div>

        </div>

      </div>

    </div>

  </div>

  <x-admin.footer />
