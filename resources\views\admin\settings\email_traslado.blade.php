@extends('layouts.admin')
@section('title', 'E-mail Traslado')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="fw-bold py-3 m-0">
    <span class="text-muted fw-light">Configurações / </span> E-mail Traslado
  </h4>
</div>

<form class="card my-4" method="POST" action="{{ route('settings.email_traslado.update') }}">
  @csrf
  <h5 class="card-header">Configurações</h5>
  <div class="card-body">
    <div class="row">
      <div class="col-12">
        <label class="form-label"><b>E-MAIL TRASLADO</b></label><br />
        O conteúdo do e-mail deve ser cadastrado abaixo utilizando tags para personalização.<br/>
        Obs. O cabeçalho e rodapé do e-mail será adicionado de forma automática.
        <br>
        <br>
        <label class="form-label"><b>TAGS EXISTENTES</b></label>
        <br>
        [NOME] [CPF] [ESCREVENTE] [TRASLADO] [ATUALIZACAO] (<i>última atualização</i>) [NEWSLETTER]
        <br />
        <br />
        <label class="form-label"><b>CONTEÚDO DO E-MAIL TRASLADO</b></label>
        <textarea rows="10" class="form-control measures @error('conteudo_traslado') is-invalid @enderror" name="conteudo_traslado" required>{{ $email_trasladoSettings->conteudo_traslado }}</textarea>

        <br />
        <label class="form-label"><b>CONTEÚDO DA NEWSLETTER</b></label><br>
        O conteúdo abaixo é inserido no e-mail acima na tag [NEWSLETTER] apenas se o cliente não assinou a newsletter.
        <textarea rows="4" class="form-control measures @error('conteudo_newsletter') is-invalid @enderror" name="conteudo_newsletter" required>{{ $email_trasladoSettings->conteudo_newsletter }}</textarea>

      </div>
    </div>
    <button class="btn btn-primary mt-3">Salvar</button>
  </div>
</form>

  <script nonce="{{ csp_nonce() }}">
    $( "#sortable" ).sortable();

    $( ".accordion-button" ).on('click', function(){
      $(".accordion-collapse").collapse('hide');
    });
  </script>
@endsection
