<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Page;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class ClienteController extends Controller
{
    public function templateEdit()
    {
        $page = Page::where([
            'type' => 'clientes_servicos'
        ])->first();

        $t = $page->dynamic_page_settings ?? [];

        $pages = Page::all();

        return view('admin.clientes.servicos', [
            'page' => $page,
            'dynamic_page_settings' => $t,
            'pages' => $pages
        ]);
    }

    public function update(Request $r)
    {
        $home = Page::where([
            'type' => 'clientes_servicos',
        ])->first();

        $settings = [];
        if (!$home) {
            $home = new Page;
        } else {
            $settings = json_decode($home->dynamic_page_settings, true);
        }

        $settings['block1'] = $r->block1;

        for ($i = 0; $i < 12; $i++) {
            $settings['color1' . $i] = $r->{"color1" . $i};
            $settings['bold1' . $i] = $r->{"bold1" . $i};
            $settings['blank1' . $i] = $r->{"blank1" . $i};

            $settings['color2' . $i] = $r->{"color2" . $i};
            $settings['bold2' . $i] = $r->{"bold2" . $i};
            $settings['blank2' . $i] = $r->{"blank2" . $i};
        }

        $settings = json_encode($settings);
        $home->dynamic_page_settings = $settings;
        $home->restricted_access = 1;
        $home->type = 'clientes_servicos';
        $home->slug = 'meus_servicos_admin';
        $home->published = 1;
        $home->save();

        return redirect()->back()->with('success', 'Links editados com sucesso!');
    }
}
