@extends('layouts.app')
@section('title', 'Novidades')

@section('content')
<section class="pageNovidades txtcontent">
	
	<div class="container-fluid">

		<h1>Novidades</h1>
		
		<div class="row listNovidades">
      @foreach ($news as $theNews)
        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
          <div class="blocoBlog">
            @if(!empty($theNews->photo))
              <div class="imageBlog">
                <img src="{{ asset('storage/' . $theNews->photo) }}" alt="PreviewBlog" style="max-width: 100%;">
              </div>
            @endif
            <div class="infoBlog">
              <p class="mb-0">{{ $theNews->title }}</p>
              <a href="{{ route('news.show', [$theNews->slug]) }}" class="btnLink"><PERSON><PERSON> mais +</a>
            </div>
          </div>
        </div>
      @endforeach
		</div>


    {{ $news->links('news.paginator') }}

	</div>

</section>
@endsection