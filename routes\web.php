<?php

use App\Bitrix\IntegracaoBitrix;
use App\Traslado\Traslado;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Response;

use App\Http\Controllers\Admin\MenuController as AdminMenuController;
use App\Http\Controllers\Admin\PageController as AdminPageController;
use App\Http\Controllers\Admin\QuestionController as AdminQuestionController;
use App\Http\Controllers\Admin\NewsController as AdminNewsController;
use App\Http\Controllers\Admin\CollaboratorController;
use App\Http\Controllers\Admin\AuthController as AdminAuthController;
use App\Http\Controllers\Admin\BoletoController;
use App\Http\Controllers\Admin\PedidosCertidaoController;
use App\Http\Controllers\Admin\ClienteController;
use App\Http\Controllers\Admin\DocumentController as AdminDocumentController;
use App\Http\Controllers\Admin\NewsletterController as AdminNewsletterController;
use App\Http\Controllers\Admin\FormulariosController as AdminFormulariosController;
use App\Http\Controllers\Admin\CRMController as AdminCRMController;
use App\Http\Controllers\Admin\SysmetaController;

use App\Http\Controllers\HomeController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\FormController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\MenuController;
use App\Http\Controllers\QuestionController;
use App\Http\Controllers\QueriesController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\RegisterController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\Push26Controller;
use App\Http\Controllers\FormFillingController;
use App\Http\Controllers\DocumentController;
use App\Http\Controllers\NewsletterController;
use App\Models\FormFilling;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/


Route::get('/newsletter', [NewsletterController::class, 'index'])->name('newsletter.index');
Route::get('/', [HomeController::class, 'index'])->name('index');
Route::get('/pesquisa', [HomeController::class, 'search'])->name('search');
Route::get('/home/<USER>', [HomeController::class, 'services'])->name('home.services');
Route::get('/institucional', [PageController::class, 'institutional'])->name('institutional');

Route::post('/formularios/enviar', [FormController::class, 'send'])->name('forms.send');
Route::get('/formularios/correios/taxa-de-entrega', [FormController::class, 'getCorreiosDeliveryFee'])->name('forms.correios.delivery.fee');
Route::get('/formularios/gerar-boleto', [FormController::class, 'generateBoleto'])->name('forms.generate.boleto');
Route::get('/formularios/imprimir-protocolo-certidao', [FormController::class, 'printCertificateProtocol'])->name('forms.print.certificate.protocol');

Route::get('/novidades', [NewsController::class, 'news'])->name('news');
Route::get('/novidades/{slug}', [NewsController::class, 'showNews'])->name('news.show');

Route::get('/noticias', function () {
    return redirect()->route('news');
});
Route::get('/noticias/{slug}', [NewsController::class, 'redirectNews']);

Route::get('/menu/{slug}', [MenuController::class, 'menu'])->name('menu');
Route::get('/submenu/{slug}', [MenuController::class, 'submenu'])->name('submenu');

Route::get('/duvidas', [QuestionController::class, 'questions'])->name('questions');

Route::get('/contratos', [PageController::class, 'listContracts'])->name('list.contracts');
Route::get('/ouvidoria', [PageController::class, 'ouvidoria'])->name('ouvidoria');
Route::get('/fale-conosco', function () {
    return redirect()->route('ouvidoria');
});
Route::get('/sac', function () {
    return redirect()->route('ouvidoria');
});

Route::get('/mensalistas', [PageController::class, 'subscribers'])->name('subscribers');
Route::get('/tabelionato-com-voce', [PageController::class, 'WithYou26'])->name('with.you.26');
Route::get('/facilidades/pagamento-online', [PageController::class, 'onlinePayment'])->name('online.payment');


Route::get('/consultas/autenticidade-do-reconhecimento', [QueriesController::class, 'authenticityOfRecognition'])->name('queries.auth.recognition');
Route::post('/consultas/autenticidade-do-reconhecimento', [QueriesController::class, 'showAuthenticityOfRecognition'])->name('queries.show.auth.recognition');

Route::get('/custas-notariais', [PageController::class, 'priceList'])->name('price.list');
Route::post('/custas-notariais/send-mail', [PageController::class, 'priceListSendMail'])->name('price.list.sendmail');

Route::get('/login', [AuthController::class, 'login'])->name('login');
Route::post('/login', [AuthController::class, 'sendToken'])->name('login.token');
Route::get('/auth', [AuthController::class, 'auth'])->name('auth');
Route::get('/cadastro', [RegisterController::class, 'register'])->name('register');
Route::post('/cadastro', [RegisterController::class, 'registerClient'])->name('register.client');
Route::get('/verificar-token', [AuthController::class])->name('auth.verify_token');
Route::post('/verificar-token', [AuthController::class, 'verifyToken'])->name('verify_token');
Route::get('/recuperar-senha', [AuthController::class, 'reset'])->name('auth.reset_password');
Route::post('/recuperar-senha', [AuthController::class, 'resetPassword'])->name('auth.reset_password');


Route::middleware(['auth'])->group(function () {
    Route::get('/logout', [AuthController::class, 'logout'])->name('logout');
    Route::get('/minha-conta', [ClientController::class, 'myAcc'])->name('my.acc');
    Route::post('/minha-conta', [ClientController::class, 'myAccUpdate'])->name('my.acc.update');
    Route::get('/meus-servicos', [ClientController::class, 'myServices'])->name('my.services');
    Route::post('/adicionar-escrevente', [ClientController::class, 'addClerk'])->name('my.clerks');
    Route::delete('/remover-escrevente/{id}', [ClientController::class, 'removeClerk'])->name('my.clerks.remove');

    Route::get('/consultas/andamento-ato-notarial', [QueriesController::class, 'notarialAct'])->name('queries.notarial.act');
    Route::post('/consultas/andamento-ato-notarial', [QueriesController::class, 'showNotarialAct'])->name('queries.show.notarial.act');

    Route::get('/consultas/sinal-publico', [QueriesController::class, 'publicSignal'])->name('queries.public.signal');
    Route::post('/consultas/sinal-publico', [QueriesController::class, 'showPublicSignal'])->name('queries.show.public.signal');
    Route::get('/consultas/cartao-de-assinatura', [QueriesController::class, 'subscriptionCard'])->name('queries.subscription.card');
    Route::post('/consultas/cartao-de-assinatura', [QueriesController::class, 'showSubscriptionCard'])->name('queries.show.subscription.card');
    Route::get('/consultas/andamento-certidao', [QueriesController::class, 'certificate'])->name('queries.certificate');
    Route::post('/consultas/andamento-certidao', [QueriesController::class, 'showCertificate'])->name('queries.show.certificate');
    Route::get('/consultas/atos-no-26', [QueriesController::class, 'acts26'])->name('queries.acts.26');
    Route::post('/consultas/atos-no-26', [QueriesController::class, 'showActs26'])->name('queries.show.acts.26');
    Route::post('/consultas/atos-no-26-lf', [QueriesController::class, 'processaAtosLivroFolha'])->name('queries.show.acts.26.lf');


    Route::get('/documentos', [DocumentController::class, 'index'])->name('documents.index');
    Route::get('/documentos/novo', [DocumentController::class, 'create'])->name('documents.create');
    Route::post('/documentos/novo', [DocumentController::class, 'store'])->name('documents.store');
    Route::get('/documentos/download', [DocumentController::class, 'documentDownload'])->name('documents.download');
    Route::get('/documentos/{id}/cancel', [DocumentController::class, 'cancel'])->name('documents.cancel');
    Route::get('/documentos/{id}', [DocumentController::class, 'edit'])->name('documents.edit');
    Route::post('/documentos/{id}', [DocumentController::class, 'update'])->name('documents.update');

    Route::get('push26/registros', [Push26Controller::class, 'show'])->name('push26.show');
    Route::post('push26/acompanhar-protocolo', [Push26Controller::class, 'followProtocol'])->name('push26.follow.protocol');
    Route::post('push26/deixar-de-acompanhar-protocolo', [Push26Controller::class, 'unfollowProtocol'])->name('push26.unfollow.protocol');
});

// push26 cronjob
Route::get('push26/run', [Push26Controller::class, 'runCronJob'])->name('push26.run');


Route::post('salvar-preenchimento-de-formulario', [FormFillingController::class, 'save'])->name('form.filling.save');


Route::get('admin/login', [AdminAuthController::class, 'login'])->name('admin.login');
Route::get('admin/recuperar-senha', [AdminAuthController::class, 'recovery'])->name('admin.recovery');
Route::post('admin/recuperar-senha', [AdminAuthController::class, 'recoveryEmail'])->name('admin.recovery.email');
Route::get('admin/nova-senha', [AdminAuthController::class, 'newPassword'])->name('admin.new.password');
Route::post('admin/nova-senha', [AdminAuthController::class, 'updatePassword'])->name('admin.update.password');
Route::post('admin/auth', [AdminAuthController::class, 'auth'])->name('admin.auth');
Route::get('admin/logout', [AdminAuthController::class, 'logout'])->name('admin.logout');

Route::get('/modelos/file-download', [AdminPageController::class, 'templatesFileDownload'])->name('pages.templates.file.download');
Route::prefix('admin')->middleware(['auth.admin'])->group(function () {
    Route::get('/', function () {
        return view('admin.admin');
    })->name('admin');

    Route::get('/clientes/servicos', [ClienteController::class, 'templateEdit'])->name('clientes.servicos');
    Route::post('/clientes/servicos', [ClienteController::class, 'update'])->name('clientes.servicos.update');


    Route::prefix('pedidos_certidao')->name('admin.pedidos_certidao.')->group(function () {
        Route::get('/', [PedidosCertidaoController::class, 'index'])->name('index');
        Route::get('/novo', [PedidosCertidaoController::class, 'create'])->name('create');
        Route::post('/novo', [PedidosCertidaoController::class, 'store'])->name('store');
        Route::get('/{id}', [PedidosCertidaoController::class, 'show'])->name('show');
        Route::get('/{id}/editar', [PedidosCertidaoController::class, 'edit'])->name('edit');
        Route::post('/{id}', [PedidosCertidaoController::class, 'update'])->name('update');
        Route::delete('/{id}', [PedidosCertidaoController::class, 'destroy'])->name('destroy');
    });

    Route::prefix('menus')->name('menus.')->group(function () {
        Route::get('/cabecalho', [AdminMenuController::class, 'header'])->name('header');
        Route::get('/cabecalho/novo', [AdminMenuController::class, 'createHeader'])->name('header.create');
        Route::post('/cabecalho/novo', [AdminMenuController::class, 'storeHeader'])->name('header.store');
        Route::get('/cabecalho/{id}', [AdminMenuController::class, 'showHeader'])->name('header.show');
        Route::post('/cabecalho/{id}', [AdminMenuController::class, 'updateHeader'])->name('header.update');
        Route::delete('/cabecalho/{id}', [AdminMenuController::class, 'destroyHeader'])->name('header.destroy');

        Route::get('/rodape', [AdminMenuController::class, 'footer'])->name('footer');
        Route::get('/rodape/novo', [AdminMenuController::class, 'createFooter'])->name('footer.create');
        Route::post('/rodape/novo', [AdminMenuController::class, 'storeFooter'])->name('footer.store');
        Route::get('/rodape/{id}', [AdminMenuController::class, 'editFooter'])->name('footer.edit');
        Route::post('/rodape/{id}', [AdminMenuController::class, 'updateFooter'])->name('footer.update');
        Route::delete('/rodape/{id}', [AdminMenuController::class, 'destroyFooter'])->name('footer.destroy');


        Route::get('/links', [AdminMenuController::class, 'links'])->name('links');
        Route::post('/links', [AdminMenuController::class, 'linksStore'])->name('links.store');

        Route::get('/menus/update-order', [AdminMenuController::class, 'updateMenuOrder'])->name('update.order');
    });

    Route::prefix('paginas')->name('pages.')->group(function () {
        Route::get('/modelos', [AdminPageController::class, 'templates'])->name('templates');
        Route::get('/modelos/filtrar', [AdminPageController::class, 'templatesFilter'])->name('templates.filter');
        Route::get('/modelos/nova', [AdminPageController::class, 'templatesCreate'])->name('templates.create');
        Route::post('/modelos/nova', [AdminPageController::class, 'templatesStore'])->name('templates.store');
        Route::get('/modelos/{id}/editar', [AdminPageController::class, 'templatesEdit'])->name('templates.edit');
        Route::post('/modelos/{id}/editar', [AdminPageController::class, 'templatesUpdate'])->name('templates.update');
        Route::delete('/modelos/{id}', [AdminPageController::class, 'templatesDestroy'])->name('templates.destroy');
        Route::post('/modelos/file-upload', [AdminPageController::class, 'templatesFileUpload'])->name('templates.file.upload');


        Route::get('/dinamicas', [AdminPageController::class, 'dynamics'])->name('dynamics');
        Route::get('/dinamicas/{page}', [AdminPageController::class, 'dynamicsEdit'])->name('dynamics.edit');
        Route::post('/dinamicas/{page}', [AdminPageController::class, 'dynamicsUpdate'])->name('dynamics.update');

        Route::get('/codificadas', [AdminPageController::class, 'coded'])->name('coded');
        Route::get('/codificadas/nova', [AdminPageController::class, 'codedCreate'])->name('coded.create');
        Route::post('/codificadas/nova', [AdminPageController::class, 'codedStore'])->name('coded.store');
        Route::get('/codificadas/{page}', [AdminPageController::class, 'codedEdit'])->name('coded.edit');
        Route::post('/codificadas/{page}', [AdminPageController::class, 'codedUpdate'])->name('coded.update');
        Route::delete('/codificadas/{page}', [AdminPageController::class, 'codedDestroy'])->name('coded.destroy');
    });

    Route::prefix('duvidas')->name('questions.')->group(function () {
        Route::get('/categorias', [AdminQuestionController::class, 'categories'])->name('categories');
        Route::get('/categorias/nova', [AdminQuestionController::class, 'categoriesCreate'])->name('categories.create');
        Route::post('/categorias/nova', [AdminQuestionController::class, 'categoriesStore'])->name('categories.store');
        Route::get('/categorias/update-order', [AdminQuestionController::class, 'updateCategoriesOrder'])->name('categories.update.order');
        Route::get('/categorias/{id}', [AdminQuestionController::class, 'categoriesEdit'])->name('categories.edit');
        Route::post('/categorias/{id}', [AdminQuestionController::class, 'categoriesUpdate'])->name('categories.update');
        Route::delete('/categorias/{id}', [AdminQuestionController::class, 'categoriesDestroy'])->name('categories.destroy');

        Route::get('/', [AdminQuestionController::class, 'questions'])->name('q.index');
        Route::get('/nova', [AdminQuestionController::class, 'questionsCreate'])->name('q.create');
        Route::post('/nova', [AdminQuestionController::class, 'questionsStore'])->name('q.store');
        Route::get('/update-order', [AdminQuestionController::class, 'updateQuestionsOrder'])->name('update.order');
        Route::get('/{id}', [AdminQuestionController::class, 'questionsEdit'])->name('q.edit');
        Route::post('/{id}', [AdminQuestionController::class, 'questionsUpdate'])->name('q.update');
        Route::delete('/{id}', [AdminQuestionController::class, 'questionsDestroy'])->name('q.destroy');
    });


    Route::prefix('novidades')->name('news.')->group(function () {
        Route::get('/categorias', [AdminNewsController::class, 'categories'])->name('categories');
        Route::post('/categorias', [AdminNewsController::class, 'categoriesStore'])->name('categories.store');
        Route::get('/categorias/{id}', [AdminNewsController::class, 'categoriesEdit'])->name('categories.edit');
        Route::post('/categorias/{id}', [AdminNewsController::class, 'categoriesUpdate'])->name('categories.update');
        Route::delete('/categorias/{id}', [AdminNewsController::class, 'categoriesDestroy'])->name('categories.destroy');

        Route::get('/autores', [AdminNewsController::class, 'authors'])->name('authors');
        Route::post('/autores', [AdminNewsController::class, 'authorsStore'])->name('authors.store');
        Route::get('/autores/{id}', [AdminNewsController::class, 'authorsEdit'])->name('authors.edit');
        Route::post('/autores/{id}', [AdminNewsController::class, 'authorsUpdate'])->name('authors.update');
        Route::delete('/autores/{id}', [AdminNewsController::class, 'authorsDestroy'])->name('authors.destroy');

        Route::get('/banners', [AdminNewsController::class, 'banners'])->name('banners');
        Route::post('/banners/{id}', [AdminNewsController::class, 'bannersUpdate'])->name('banners.update');

        Route::get('/', [AdminNewsController::class, 'news'])->name('news');
        Route::get('/nova', [AdminNewsController::class, 'newsCreate'])->name('news.create');
        Route::post('/nova', [AdminNewsController::class, 'newsStore'])->name('news.store');
        Route::get('/{id}', [AdminNewsController::class, 'newsEdit'])->name('news.edit');
        Route::post('/{id}', [AdminNewsController::class, 'newsUpdate'])->name('news.update');
        Route::delete('/{id}', [AdminNewsController::class, 'newsDestroy'])->name('news.destroy');
    });

    Route::get('/minha-conta', [CollaboratorController::class, 'collaboratorsProfile'])->name('collaborator.profile');
    Route::post('/minha-conta', [CollaboratorController::class, 'collaboratorsProfileUpdate'])->name('collaborator.profile.update');
    Route::prefix('colaboradores')->name('collaborators.')->group(function () {
        Route::get('/areas-de-atuacao', [CollaboratorController::class, 'occupations'])->name('occupations');
        Route::post('areas-de-atuacao', [CollaboratorController::class, 'occupationsStore'])->name('occupations.store');
        Route::get('areas-de-atuacao/{id}', [CollaboratorController::class, 'occupationsEdit'])->name('occupations.edit');
        Route::post('areas-de-atuacao/{id}', [CollaboratorController::class, 'occupationsUpdate'])->name('occupations.update');
        Route::delete('areas-de-atuacao/{id}', [CollaboratorController::class, 'occupationsDestroy'])->name('occupations.destroy');

        Route::get('/', [CollaboratorController::class, 'collaborators'])->name('collaborators');
        Route::get('/novo', [CollaboratorController::class, 'collaboratorsCreate'])->name('collaborators.create');
        Route::post('/novo', [CollaboratorController::class, 'collaboratorsStore'])->name('collaborators.store');
        Route::get('/{id}', [CollaboratorController::class, 'collaboratorsEdit'])->name('collaborators.edit');
        Route::post('/{id}', [CollaboratorController::class, 'collaboratorsUpdate'])->name('collaborators.update');
        Route::delete('/{id}', [CollaboratorController::class, 'collaboratorsDestroy'])->name('collaborators.destroy');
    });

    Route::prefix('boletos')->name('boletos.')->group(function () {
        Route::get('/', [BoletoController::class, 'index'])->name('index');
        Route::get('/novo', [BoletoController::class, 'create'])->name('create');
        Route::post('/gerar', [BoletoController::class, 'generate'])->name('generate');
        Route::get('/{id}', [BoletoController::class, 'show'])->name('show');
    });

    Route::prefix('documentos')->name('admin.documents.')->group(function () {
        Route::get('/documentos/download', [DocumentController::class, 'documentDownload'])->name('download');
        Route::get('/', [AdminDocumentController::class, 'index'])->name('index');
        Route::get('/{id}/download/all', [AdminDocumentController::class, 'downloadAll'])->name('download.all');
        Route::get('/{id}', [AdminDocumentController::class, 'show'])->name('show');
        Route::get('/{id}/reopen', [AdminDocumentController::class, 'reopen'])->name('reopen');
        Route::get('/{id}/drop', [AdminDocumentController::class, 'drop'])->name('drop');
    });

    Route::prefix('newsletter')->name('admin.newsletter.')->group(function () {
        Route::get('/', [AdminNewsletterController::class, 'index'])->name('index');
        Route::post('/', [AdminNewsletterController::class, 'index'])->name('index');
        Route::get('/export', [AdminNewsletterController::class, 'export'])->name('export');
        Route::post('/{id}', [AdminNewsletterController::class, 'delete'])->name('delete');
    });

    Route::prefix('formularios')->name('admin.formularios.')->group(function () {
        Route::get('/', [AdminFormulariosController::class, 'index'])->name('index');
        Route::post('/', [AdminFormulariosController::class, 'index'])->name('index');
        Route::get('/export', [AdminFormulariosController::class, 'export'])->name('export');
        Route::get('/{id}', [AdminFormulariosController::class, 'details'])->name('details');
        Route::post('/{id}', [AdminFormulariosController::class, 'delete'])->name('delete');
        Route::post('/reenviar_form_bitrix/{id}', [AdminFormulariosController::class, 'reenviar_form_bitrix'])->name('reenviar_form_bitrix');
    });

    Route::prefix('crm')->name('admin.crm.')->group(function () {
        Route::get('/', [AdminCRMController::class, 'index'])->name('index');
        Route::post('/', [AdminCRMController::class, 'index'])->name('index');
        Route::get('/export', [AdminCRMController::class, 'export'])->name('export');
        Route::get('/{id}', [AdminCRMController::class, 'details'])->name('details');
        Route::put('/{id}', [AdminCRMController::class, 'alterar_dados'])->name('alterar_dados');
        Route::post('/{id}', [AdminCRMController::class, 'alteracao_representante'])->name('alteracao_representante');
    });

    Route::prefix('configuracoes')->name('settings.')->group(function () {
        Route::get('/certidao', function () {
            return view('admin.settings.certificate');
        })->name('certificate');

        Route::post('/certidao', [SysmetaController::class, 'updateCertificateSettings'])->name('certificate.update');

        Route::get('/email_traslado', function () {
            return view('admin.settings.email_traslado');
        })->name('email_traslado');

        Route::post('/email_traslado', [SysmetaController::class, 'updateEmailTrasladoSettings'])->name('email_traslado.update');
    });
});

Route::get('/enviar-por-email', [BoletoController::class, 'sendByMail'])->name('boletos.send_mail');
Route::post('/retorno_bitrix', [IntegracaoBitrix::class, 'retornoBitrix'])->name('bitrix.retorno_bitrix');
Route::get('/importa_dados_api_data', [FormFillingController::class, 'importa_dados_api_data'])->name('api.importa_dados_api_data');
Route::get('/importa_dados_api_data/{data}', [FormFillingController::class, 'importa_dados_api_data'])->name('api.importa_dados_api_data');
Route::get('/envia_emails_traslado', [Traslado::class, 'envia_emails_traslado'])->name('traslado.envia_emails_traslado');
Route::get('/envia_emails_traslado/{crm_id}', [Traslado::class, 'envia_emails_traslado_individual'])->name('traslado.envia_emails_traslado_individual');
Route::get('/cadastro_newsletter/{email}', [NewsController::class, 'cadastro_newsletter'])->name('cadastro_newsletter');

//Route::get('/blog26', [PageController::class, 'blog26'])->name('blog26');


Route::get('/contratos/{slug}', [PageController::class, 'contracts'])->name('contracts');
Route::get('/servicos/{slug}', [PageController::class, 'services'])->name('services');
Route::get('/{slug}', [PageController::class, 'default'])->name('default');
Route::get('/download_anexo_pedido/{filename}/{ext}', function ($filename, $ext) {
    $filename = $filename . '.' . $ext;
    $path = storage_path('app\\pedidos_certidao\\' . $filename);

    if (!file_exists($path)) {
        echo "Arquivo não encontrado.";
        exit;
    }
    return Response::download($path, $filename);
})->middleware('auth.admin')->name('download_anexo_pedido');
