@extends('layouts.app')
@section('title', 'Autenticidade do Reconhecimento')

@section('content')
<section class="account txtcontent">
  <div class="container-fluid">
	@if (!isset($resposta))
		<div class="row">
			<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">

				<h1>Autenticidade do Reconhecimento da Firma</h1>

				<p><strong class="blue">PESQUISA POR DATA E CÓDIGO</strong><br>Informe a data do reconhecimento de firma e o código criptografado impressos na etiqueta do ato.</p>

				<form action="{{ route('queries.show.auth.recognition') }}" method="POST">
          @csrf
          <div class="formControl">
						<label>Data de reconhecimento:</label>
						<input type="date" class="form-control" name="carimbo_data" value="{{ old('carimbo_data') }}" required>
					</div>
					<div class="formControl">
						<label>Código criptografado:</label>
						<input type="text" name="carimbo_codigo" class="mb-0" {{ old('carimbo_codigo') }} required>
					</div>

          <div class="formControl check">
            <input type="checkbox" required>
            <label>Li, estou de acordo com a <a href="/politica-de-privacidade" target="_blank">política de privacidade</a> e desejo receber informativos do 26º Tabelionato de Notas.</label>
          </div>

					<div class="reCaptcha">
            {!! htmlFormSnippet() !!}
          </div>

          @if(session('form_error') || $errors->any())
            <div class="message error">
              <p>Formulário não enviado.</p>
              @if ($errors->any())
                <ul class="mt-2" style="font-size: 15px;">
                  @foreach ($errors->all() as $error)
                    <li style="color:#a94442;">{{ $error }}</li>
                  @endforeach
                </ul>
              @endif
            </div>
          @endif

					<div class="d-flex">
						<button class="btnDefault">Enviar</button>
					</div>
				</form>

			</div>

		</div>
  
    @else

        <div class="d-flex justify-content-between align-items-start">
          <h1>Autenticidade do Reconhecimento</h1>
          <a href="https://www.26notas.com.br/consultas/autenticidade-do-reconhecimento" class="btnDefault" style="background: #708082;">Voltar</a>
        </div>

      <div class="row">
        <div class="resultadoBusca mt-1">
          
          <div class="headBusca">
            <h2>RESULTADO DA VERIFICAÇÃO DE AUTENTICIDADE</h2>
          </div>

          <table class="registros">
            <tbody>
              <tr class="head">
                <th>Data Reconhecimento</th>
                <th>Escrevente que assinou</th>
                <th>Nome do Cliente</th>
                <th>Tipo do Conhecimento</th>
              </tr>
              @if (!empty($resposta))
                <tr>
                  <td>{{ $resposta->dataRec }}</td>
                  <td>{{ $resposta->escrevente }}</td>
                  <td>{{ $resposta->nomes }}</td>
                  <td>{{ $resposta->descricao }}</td>
                </tr>
              @else
                <tr>
                  <td colspan="5">
                    <p style="text-align: left">Sem Resultado</p>
                  </td>
                </tr>
              @endif
            <tr>
          </tr></tbody></table>
        </div>
      </div>
    @endif

	</div>

</section>
@endsection