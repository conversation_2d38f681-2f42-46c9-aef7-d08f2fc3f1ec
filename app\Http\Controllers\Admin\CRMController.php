<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\APIDasti\ConsultasAPIDasti;
use Illuminate\Http\Request;
use App\Models\FormFilling;
use App\Models\Collaborator;
use App\Models\Page;
use App\Exports\CRMExport;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;

class CRMController extends Controller
{
  public function index(Request $r)
  {
    $busca = mb_strtolower($r->busca, 'UTF-8');
    $forms = FormFilling::whereRaw('(LOWER(JSON_EXTRACT(fields, "$.nome")) like ? OR LOWER(JSON_EXTRACT(fields, "$.CPF")) like ? OR LOWER(JSON_EXTRACT(fields, "$.CNPJ")) like ? OR LOWER(JSON_EXTRACT(fields, "$.email")) like ?)', ['"%' . $busca . '%"', '"%' . $busca . '%"', '"%' . $busca . '%"', '"%' . $busca . '%"']);
    $clerks = Collaborator::where('type', 'clerk')->orderBy('name')->get()->toArray();

    $clerks = \DB::table('form_filling')
              ->selectRaw('CAST(REPLACE(JSON_UNQUOTE(JSON_EXTRACT(fields, "$.escrevente_nome")), \'"\', \'\') AS CHAR) as escrevente')
              ->whereRaw('JSON_EXTRACT(fields, "$.escrevente_nome") is not null AND REPLACE(JSON_EXTRACT(fields, "$.escrevente_nome"), \'"\', \'\') <> \'null\'')
              ->distinct()
              ->get();

    $clerks = $this->array_sort($clerks, 'escrevente', SORT_ASC);

    if($r->origem != ''){
        $forms = $forms->where('origem', $r->origem);
    }

    if($r->bitrix != ''){
        $forms = $forms->whereRaw('JSON_EXTRACT(fields, "$.ganho_ou_perda") = ?', $r->bitrix);
    }

    if($r->clerk != ''){
        $forms = $forms->whereRaw('JSON_EXTRACT(fields, "$.escrevente_nome") = ?', $r->clerk);
    }

    $forms = $forms->orderByDesc('id')->paginate(30);

    return view('admin.crm.index', ['forms' => $forms, 'clerks' => $clerks]);
  }

  function array_sort($array, $on, $order=SORT_ASC)
    {
        $new_array = array();
        $sortable_array = array();

        if (count($array) > 0) {
            foreach ($array as $k => $v) {
                if (is_array($v)) {
                    foreach ($v as $k2 => $v2) {
                        if ($k2 == $on) {
                            $sortable_array[$k] = $v2;
                        }
                    }
                } else {
                    $sortable_array[$k] = $v;
                }
            }

            switch ($order) {
                case SORT_ASC:
                    asort($sortable_array);
                break;
                case SORT_DESC:
                    arsort($sortable_array);
                break;
            }

            foreach ($sortable_array as $k => $v) {
                array_push($new_array, $array[$k]);
            }
        }

        return $new_array;
    }

  public function details($id){
    $form = FormFilling::find($id);

    if($form == null)
        exit;

    $fields = json_decode($form->fields);

    $escreventes = \DB::table('form_filling')
                    ->selectRaw('REPLACE(JSON_EXTRACT(fields, "$.escrevente_nome"), \'"\', \'\') as escrevente')
                    ->whereRaw('JSON_EXTRACT(fields, "$.escrevente_nome") is not null AND REPLACE(JSON_EXTRACT(fields, "$.escrevente_nome"), \'"\', \'\') <> \'null\' AND (REPLACE(JSON_EXTRACT(fields, "$.CPF"), \'"\', \'\') = ? OR REPLACE(JSON_EXTRACT(fields, "$.CNPJ"), \'"\', \'\') = ?)', [$fields->CPF ?? null, $fields->CNPJ ?? null])
                    ->distinct()
                    ->get();

    $cnpjsVinculados = \DB::table('form_filling')
                       ->selectRaw('REPLACE(JSON_EXTRACT(fields, "$.nome_empresa_contato"), \'"\', \'\') as nome_empresa_contato')
                       ->whereRaw('JSON_EXTRACT(fields, "$.nome_empresa_contato") is not null AND REPLACE(JSON_EXTRACT(fields, "$.nome_empresa_contato"), \'"\', \'\') <> \'null\' AND (REPLACE(JSON_EXTRACT(fields, "$.CPF"), \'"\', \'\') = ? OR REPLACE(JSON_EXTRACT(fields, "$.CNPJ"), \'"\', \'\') = ?)', [$fields->CPF ?? null, $fields->CNPJ ?? null])
                       ->distinct()
                       ->get();

    $totalCardsCRM = FormFilling::whereRaw('(REPLACE(JSON_EXTRACT(fields, "$.CPF"), \'"\', \'\') = ? OR REPLACE(JSON_EXTRACT(fields, "$.CNPJ"), \'"\', \'\') = ?) AND bitrix_status = \'OK\'', [$fields->CPF ?? null, $fields->CNPJ ?? null])->count();

    $cpfCnpj = $fields->CPF ?? $fields->CNPJ ?? "";
    $protocolos = FormFilling::whereRaw('origem = \'API\' AND (REPLACE(JSON_EXTRACT(fields, "$.CPF"), \'"\', \'\') = ? OR REPLACE(JSON_EXTRACT(fields, "$.CNPJ"), \'"\', \'\') = ?)', [$fields->CPF ?? null, $fields->CNPJ ?? null])->get();

    if($cpfCnpj != ""){
        // $apiDasti = new ConsultasAPIDasti;
        // $protocolos = json_decode($apiDasti->pesquisarProtocolosPorDocumento($cpfCnpj));
        // foreach($protocolos as $protocolo){
        //     $atualizacaoProtocolo = json_decode($apiDasti->pesquisaSituacaoProtocolo($protocolo->protocolo, $cpfCnpj));
        // }
    }

    return view('admin.crm.show', ['fields' => $fields, 'form' => $form, 'escreventes' => $escreventes, 'totalCardsCRM' => $totalCardsCRM, 'cnpjsVinculados' => $cnpjsVinculados, 'protocolos' => $protocolos]);
  }

    function efetuaAlteracao($id, Request $r){
        $form = FormFilling::find($id);
        $fields = json_decode($form->fields);
        $fields->nome = $r['nome'];
        $fields->CNPJ = null;
        $fields->CPF = null;
        if(strlen($r['cpf_cnpj']) == 14){
            $fields->CNPJ = $r['cpf_cnpj'];
        }else{
            $fields->CPF = $r['cpf_cnpj'];
        }
        $fields->data_nascimento = $r['data_nascimento'];
        $fields->sexo = $r['sexo'];
        $fields->profissao = $r['profissao'];
        $fields->email = $r['email'];
        $fields->estado_civil = $r['estado_civil'];
        $fields->telefone = $r['telefone'];
        $fields->cargo = $r['cargo'];
        $fields->endereco = $r['endereco'];
        $fields->nome_empresa_contato = $r['nome_empresa_contato'];
        $fields->razao_social = $r['razao_social'];
        $form->fields = json_encode($fields);
        $form->save();
    }

  function alterar_dados($id, Request $r){
    $this->efetuaAlteracao($id, $r);
    if($r['cpf_cnpj'] != ""){
        $buscaFormulariosComMesmoCpfCnpj = $cnpjsVinculados = FormFilling::whereRaw('REPLACE(JSON_EXTRACT(fields, "$.CPF"), \'"\', \'\') = ? OR REPLACE(JSON_EXTRACT(fields, "$.CNPJ"), \'"\', \'\') = ? AND id <> ?', [$r['cpf_cnpj'] ?? null, $r['cpf_cnpj'] ?? null, $id])->get();
        foreach($buscaFormulariosComMesmoCpfCnpj as $form){
            $this->efetuaAlteracao($form->id, $r);
        }
    }

    return redirect('admin/crm/' . $id)->with('success', 'Dados gravados com sucesso!');
  }

  function alteracao_representante($id, Request $r){
    $form = FormFilling::find($id);
    $fields = json_decode($form->fields);
    $fields->nome_representante = $r['nome_representante'];
    $fields->telefone_representante = $r['telefone_representante'];
    $fields->email_representante = $r['email_representante'];
    $form->fields = json_encode($fields);
    $form->save();
    return redirect('admin/crm/' . $id)->with('success', 'Dados gravados com sucesso!');
  }


  public function export(Request $r){
    if(\Auth::guard('admin')->user()->type == 'admin'){
        $busca = mb_strtolower($r->busca, 'UTF-8');
        $forms = FormFilling::whereRaw('(LOWER(JSON_EXTRACT(fields, "$.nome")) like ? OR LOWER(JSON_EXTRACT(fields, "$.CPF")) like ? OR LOWER(JSON_EXTRACT(fields, "$.CNPJ")) like ? OR LOWER(JSON_EXTRACT(fields, "$.email")) like ?)', ['"%' . $busca . '%"', '"%' . $busca . '%"', '"%' . $busca . '%"', '"%' . $busca . '%"']);

        if($r->origem != ''){
            $forms = $forms->where('origem', $r->origem);
        }

        if($r->bitrix != ''){
            $forms = $forms->whereRaw('JSON_EXTRACT(fields, "$.ganho_ou_perda") = ?', $r->bitrix);
        }

        if($r->clerk != ''){
            $forms = $forms->whereRaw('JSON_EXTRACT(fields, "$.escrevente_nome") = ?', $r->clerk);
        }

        $forms = $forms->orderByDesc('id');

        $now = now()->format('Y-m-d_H-i:s');

        return Excel::download(new CRMExport($forms->get()), "crm_$now.xls");
    }
  }
}
