<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Illuminate\Support\Collection;

class CRMExport implements FromCollection, ShouldAutoSize{
    protected $formularios;

    public function __construct(\Illuminate\Database\Eloquent\Collection $formularios)
    {
        $this->formularios = $formularios;
    }

    public function collection(){
        $collection = new Collection();
        $collection->push(['Nome', 'CPF da Parte', 'Origem', 'Escrevente', 'Traslado', 'Bitrix']);

        foreach($this->formularios as $form){
            $fields = json_decode($form->fields);

            $status_bitrix = '-';

            if(isset($fields->ganho_ou_perda)){
                if($fields->ganho_ou_perda == "LOSE")
                    $status_bitrix = "Perdido";
                elseif($fields->ganho_ou_perda == "WON")
                    $status_bitrix = "Ganho";
            }
            $collection->push([$fields->nome ?? '',
                               $fields->CPF ?? $fields->CNPJ ?? '-',
                               $form->origem,
                               $fields->escrevente_nome ?? '-',
                               $fields->traslado ?? '-',
                               $status_bitrix
                            ]);
        }

        return $collection;
    }
}
