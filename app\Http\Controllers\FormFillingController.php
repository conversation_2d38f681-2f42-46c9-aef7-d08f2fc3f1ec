<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\FormFilling;
use App\APIDasti\ConsultasAPIDasti;
use Illuminate\Support\Facades\Mail;
use App\Mail\ThankYouEmailToUser;
use App\Http\Controllers\NewsletterController;
use Illuminate\Support\Facades\Validator;

class FormFillingController extends Controller
{
  public function save(Request $r)
  {
    $fields = $r->except(['_token','page_title', 'g-recaptcha-response']);

    $validator = Validator::make(request()->all(), [
      'g-recaptcha-response' => 'recaptcha',
    ]);

    if($validator->fails()) {
      return redirect()->to(url()->previous() . '#hash')->withErrors($validator)->withInput();
    }

    $form = new FormFilling;
    $form->page_title = $r->page_title;
    $form->fields = json_encode($fields);
    $form->save();

    if (!empty($r->email)) {
      $name = $r->nome ?? '';
      $name = explode(" ", $name)[0];
      $subject = 'Agradecemos o contato ' . $name;
      $template = 'thank-you-email-to-user';
      //Mail::to($r->email)->send(new ThankYouEmailToUser($subject, $name, $template));

      if (boolval($r->want_messages_checkbox)) {
        $newsletter = new NewsletterController;
        $newsletter = $newsletter->index($r);
      }
    }

    return redirect()->back()->with('success', 'teste');
  }

  public function importa_dados_api_data($data = ""){
    try{
        if($data == "")
        $data = date('Y-m-d');

    $api = new ConsultasAPIDasti;
    $atos = json_decode($api->pesquisaAtosData($data));

    foreach($atos->data as $ato){
        $buscaFormulario = FormFilling::whereRaw('ORIGEM = "API" AND JSON_EXTRACT(fields, "$.protocolo") = ? AND (REPLACE(JSON_EXTRACT(fields, "$.CPF"), \'"\', \'\') = ? OR REPLACE(JSON_EXTRACT(fields, "$.CNPJ"), \'"\', \'\') = ?)', [$ato->Protocolo, $ato->Documento, $ato->Documento]);
        $form = new FormFilling;

        if($buscaFormulario->count() > 0)
            $form = $buscaFormulario->first();

        $fields = json_decode(json_encode(array('nome' => '')));
        $fields->nome = $ato->Nome;
        $fields->protocolo = $ato->Protocolo;

        if($ato->{'Tipo Pessoa'} == "F")
            $fields->CPF = $ato->Documento;
        else
            $fields->CNPJ = $ato->Documento;

        switch($ato->{"Estado Civil"}){
            case 0:
                $fields->estado_civil = "Solteiro";
                break;
            case 1:
                $fields->estado_civil = "Casado";
                break;
            case 2:
                $fields->estado_civil = "Viúvo";
                break;
            case 3:
                $fields->estado_civil = "Divorciado";
                break;
            case 4: case 7:
                $fields->estado_civil = "Separado";
                break;
            case 5:
                $fields->estado_civil = "Outros";
                break;
            case 6:
                $fields->estado_civil = "Desquitada";
                break;
        }


        $fields->data_nascimento = $ato->Nascimento;
        $fields->profissao = $ato->Profissao;
        $fields->email = $ato->Email;
        $fields->cargo = $ato->Cargo;
        $fields->telefone = $ato->Telefone;
        $fields->valorTotal = $ato->{"Valor Total"};
        $fields->valorTotalEmolumentos = $ato->{"Valor Total Emolumentos"};

        $fields->escrevente_nome = $ato->Escrevente;

        $fields->razao_social = $ato->{'Razao Social'};

        if($ato->Sexo == "M")
            $fields->sexo = "Masculino";
        elseif($ato->Sexo == "F")
            $fields->sexo = "Feminino";
        else
            $fields->sexo = "Não quis declarar";

        $form->bitrix_status = null;
        $form->page_title = "API";
        $form->origem = "API";
        $form->fields = json_encode($fields);
        $form->save();
    }
    }catch(\Exception $e){
    }
  }

  public function onlySave(Request $r, $bitrix_status, $idBitrixDeal, $origem)
  {
    $fields = $r->except(['_token','page_title', 'g-recaptcha-response']);

    $form = new FormFilling;
    $form->bitrix_status = $bitrix_status;
    $form->page_title = $r->page_title;
    $form->bitrix_id = $idBitrixDeal;
    $form->origem = $origem;
    $form->fields = json_encode($fields);
    $form->save();
    return $form->id;
  }
}
