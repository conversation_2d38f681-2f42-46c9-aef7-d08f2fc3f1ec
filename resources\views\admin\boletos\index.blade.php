@extends('layouts.admin')
@section('title', 'Boletos')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="fw-bold py-3 m-0">
    Boletos
  </h4>
  <a href="{{ route('boletos.create') }}" type="button" class="btn btn-primary">Novo</a>
</div>

 <div class="card">
  <div class="card-header d-flex justify-content-between">
    <h5 class="">Boletos</h5>
    <div class="d-flex align-items-center">
      <input type="text" class="form-control ms-2" id="protocol" placeholder="Digite o protocolo" value="{{ request()->get('protocolo') }}">
      <button class="btn btn-primary ms-3" onclick="filterProtocol()">Pesquisar</button>
    </div>
  </div>
  <div class="table-responsive">
    <table class="table table-hover">
      <thead>
        <tr>
          <th>#</th>
          <th>Protocolo</th>
          <th><PERSON><PERSON></th>
          <th><PERSON><PERSON><PERSON></th>
          <th>Ven<PERSON>mento</th>
          <th>Parte</th>
          <th>Documento</th>
          <th>Escrevente</th>
          <th>Ações</th>
        </tr>
      </thead>
      <tbody class="table-border-bottom-0">
        @foreach ($boletos as $boleto)
          <tr>
            <td>{{ $boleto->id }}</td>
            <td>{{ $boleto->protocol }}</td>
            <td>{{ $boleto->access_password }}</td>
            <td>{{ $boleto->created_at->format('d/m/Y') }}</td>
            <td>{{ $boleto->created_at->addDays(2)->format('d/m/Y') }}</td>
            <td>{{ $boleto->name }}</td>
            <td>{{ $boleto->document }}</td>
            <td>{{ $boleto->clerk->name ?? "" }}</td>
            <td>
              <div class="dropdown">
                <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown" aria-expanded="false"><i class="bx bx-dots-vertical-rounded"></i></button>
                <div class="dropdown-menu">
                  <a class="dropdown-item" href="{{ route('boletos.show', ['id' => $boleto->id]) }}"><i class="bx bx-show me-1"></i> Ver</a>
                </div>
              </div>
            </td>
          </tr>
        @endforeach
      </tbody>
    </table>
  </div>
</div>

  <script nonce="{{ csp_nonce() }}">
    function filterProtocol() {
      var protocol = $('#protocol').val();

      var url = new URL(window.location.href);
      url.searchParams.set('protocolo', protocol);
      window.location = url.href;
    }
  </script>
@endsection
