<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pages', function (Blueprint $table) {
          $table->id();
          $table->string('title')->unique();
          $table->text('description')->nullable();
          $table->boolean('restricted_access')->default(false);
          $table->string('type');
          $table->string('slug')->unique();
          $table->boolean('published')->default(0);

          $table->string('dynamic_page')->unique()->nullable();
          $table->json('dynamic_page_settings')->nullable();

          $table->string('template')->nullable();
          $table->json('template_settings')->nullable();
          $table->json('template_form_settings')->nullable();

          $table->string('coded_endpoint')->unique()->nullable();

          $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pages');
    }
};
