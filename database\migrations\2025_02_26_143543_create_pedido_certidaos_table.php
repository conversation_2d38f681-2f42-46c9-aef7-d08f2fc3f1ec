<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pedido_certidaos', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('form_id');
            $table->integer('pedido_id');
            $table->string('name');
            $table->string('document');

            $table->text('barcode')->nullable();
            $table->string('access_password')->unique()->nullable();
            $table->text('link')->nullable();
            $table->string('status', 1);

            $table->dateTime('data_emissao_boleto')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pedido_certidaos');
    }
};
