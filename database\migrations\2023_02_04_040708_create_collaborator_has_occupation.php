<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('collaborator_has_occupation', function (Blueprint $table) {
          $table->id();
          $table->unsignedBigInteger('collaborator_id');
          $table->foreign('collaborator_id')->references('id')->on('collaborators');
          $table->unsignedBigInteger('occupation_id');
          $table->foreign('occupation_id')->references('id')->on('collaborator_occupations');
          $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('collaborator_has_occupation');
    }
};
