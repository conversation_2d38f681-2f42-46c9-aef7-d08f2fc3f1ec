<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Illuminate\Support\Collection;

class FormulariosExport implements FromCollection, ShouldAutoSize{
    protected $formularios;

    public function __construct(\Illuminate\Database\Eloquent\Collection $formularios)
    {
        $this->formularios = $formularios;
    }

    public function collection(){
        $collection = new Collection();
        $collection->push(['Nome', 'CPF/CNPJ', 'Escrevente', 'Data', 'Serviço', 'Bitrix']);

        foreach($this->formularios as $form){
            $fields = json_decode($form->fields);
            $escreventeNome = '-';
            $bitrixStatus = '-';

            if($form->bitrix_status == "OK"){
                $bitrixStatus = "Enviado com sucesso";
            }elseif($form->bitrix_status != ""){
                $bitrixStatus = "Erro no envio";
            }

            if(isset($fields->Escrevente)){
                        $escrevente = \App\Models\Collaborator::find($fields->Escrevente);
                        if($escrevente)
                            $escreventeNome = $escrevente->name;
            }

            $collection->push([$fields->nome ?? $fields->Nome ?? '',
                               $fields->CPF ?? $fields->CNPJ ?? '',
                               $escreventeNome,
                               $form->created_at->format('d/m/Y H:i'),
                               $form->page_title,
                               $bitrixStatus
                            ]);
        }

        return $collection;
    }
}
