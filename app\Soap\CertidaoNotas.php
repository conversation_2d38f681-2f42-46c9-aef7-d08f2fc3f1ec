<?php namespace App\Soap;

use Artisaninweb\SoapWrapper\Extension\SoapService;

class CertidaoNotas extends SoapService {

  protected $name = 'certidao-notas';
  protected $wsdl = 'http://servico26notas.cartorios.net/CertidaoNotasWSImplService?wsdl';
  protected $trace = true;

  /**
   * Get all the available functions
   *
   * @return mixed
   */
  public function functions()
  {
    return $this->getFunctions();
  }
}
