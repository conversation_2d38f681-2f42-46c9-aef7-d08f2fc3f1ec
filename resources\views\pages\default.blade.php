@extends('layouts.app')
@section('title', $page->title)

@section('content')
<section class="contentServico txtcontent">
	
	<div class="container-fluid">
		<div class="row">
      @if (!empty($banners))
        <div class="col-lg-9 col-md-12 col-sm-12 col-xs-12">
      @else
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
      @endif
        <h1>{{ $page->title }}</h1>

        @foreach (json_decode($page->template_settings) as $session)
        @php
          $content = str_replace('<script type="text/javascript">', '<script nonce="'.csp_nonce().'" type="text/javascript">',$session->content) 
        @endphp
          <p>
            @if($session->type == 'content')
              @if($session->title)
                @if($session->red_title && $session->highlighted_title)
                  <h2 class="red" id="{{ $session->title }}">{{ $session->title }}</h2>
                @elseif ($session->red_title)
                  <p class="red" id="{{ $session->title }}">{{ $session->title }}</p>
                @elseif ($session->highlighted_title)
                  <h2 id="{{ $session->title }}">{{ $session->title }}</h2>
                @else
                  <strong id="{{ $session->title }}">{{ $session->title }}</strong><br>
                @endif
              @endif

              @if ($session->content_block)
                <div class="boxBgGray" style="font-family: 'Open Sans';
                font-weight: 400;
                font-size: 16px;
                line-height: 26px;
                color: #808080;
                padding:20px;">
                  {!! $content !!}
                </div>
              @else
                <div style="font-family: 'Open Sans';
                font-weight: 400;
                font-size: 16px;
                line-height: 26px;
                color: #808080;">
                  {!! $content !!}
                </div>
              @endif
            @endif

            @if ($session->type == 'image_box')
              <div class="row listAtalhos">
                @foreach ($session->blocks as $block)
                  <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                    <div class="blocoAtalhoDefault bgGray">
                      <div class="iconeAtalho">
                        <img src="{{ asset('storage/' . $block->file_name) }}" style="max-width: 133px;">
                      </div>
                      <div class="infoAtalho">
                        <p><strong>{{ $block->title }}</strong><br>
                          {{ $block->desc }}</p>
                        <a href="{{ $block->link }}" class="btnLink">{{ $block->text_link }}</a>
                      </div>
                    </div>
                  </div>
                @endforeach
              </div>
            @endif
          </p>
        @endforeach
			</div>

      @if (!empty($banners))
      <div class="col-lg-3 col-md-12 col-sm-12 col-xs-12">
        <div class="stickyNot">
          <div class="blocoAtalhoBg" @isset($banners[0]->photo) style="background-image: url('{{ asset('storage/' . $banners[0]->photo) }}');" @endisset>
            <p>{{ $banners[0]->title }}</p>
            @if ($banners[0]->button_text && $banners[0]->button_link)
              <a href="{{ $banners[0]->button_link }}" class="btnDefault red">{{ $banners[0]->button_text }}</a>
            @endif
          </div>
          <div class="blocoAtalhoBg" @isset($banners[1]->photo) style="background-image: url('{{ asset('storage/' . $banners[1]->photo) }}');" @endisset>
            <p>{{ $banners[1]->title }}</p>
            @if ($banners[1]->button_text && $banners[1]->button_link)
              <a href="{{ $banners[1]->button_link }}" class="btnDefault blue">{{ $banners[1]->button_text }}</a>
            @endif
          </div>
        </div>
      </div>
    @endif
		</div>
	</div>
</section>


@endsection