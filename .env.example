APP_NAME=26Notas
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://homologacao-26notas.lampejos.work/

URL_API_DASTI=https://websystems.26notas.com.br/api
TOKEN_API_DASTI=

SESSION_SECURE_COOKIE=false

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=26notas
DB_USERNAME=root
DB_PASSWORD=123456

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379


MAIL_MAILER=smtp
MAIL_HOST=smtp.sendgrid.net
MAIL_PORT=587
MAIL_USERNAME=apikey
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=""
MAIL_FROM_NAME="26º Tabelionato"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

#em Services/Bradesco/Pagamento.php essas credenciais não são puxadas daqui
BRADESCO_MERCHANT_ID=
BRADESCO_COD_MEIO_PAGAMENTO_TRANSFERENCIA=800
BRADESCO_COD_MEIO_PAGAMENTO_BOLETO=300
BRADESCO_TRANSFER_URI=https://meiosdepagamentobradesco.com.br/transf/transacao
BRADESCO_BOLETO_URI=https://meiosdepagamentobradesco.com.br/apiboleto/transacao
BRADESCO_KEY=

RECAPTCHA_SITE_KEY=
RECAPTCHA_SECRET_KEY=
RECAPTCHA_SKIP_IP=127.0.0.1:8000
