@extends('layouts.app')
@section('title', $news->title)

@section('content')
<section class="contentServico singleNovidade txtcontent">
	
	<div class="container-fluid">
		
		<div class="row">
			
			<div class="col-lg-9 col-md-12 col-sm-12 col-xs-12">

				<h1>{{ $news->title }}</h1>

        @if (!empty($news->author->occupation))
          <p>{{ $news->category->name }} &nbsp;&nbsp; {{ $news->created_at->format('d/m/Y') }} &nbsp;&nbsp; {{ $news->author->name }}, {{ $news->author->occupation }}</p>
        @else
          <p>{{ $news->category->name }} &nbsp;&nbsp; {{ $news->created_at->format('d/m/Y') }} &nbsp;&nbsp; {{ $news->author->name ?? '' }}</p>
        @endif

        @if(!empty($news->photo))
          <picture>
            <img src="{{ asset('storage/' . $news->photo) }}" alt="{{ $news->title }}">
          </picture>
        @endif
        
				<div class="news_single_content">
          {!! $news->content !!}
        </div>
			</div>

			<div class="col-lg-3 col-md-12 col-sm-12 col-xs-12">
				<div class="sticky">
					<div class="blocoAtalhoBg" @isset($banner1->photo) style="background-image: url('{{ asset('storage/' . $banner1->photo) }}');" @endisset>
						<p>{{ $banner1->title }}</p>
            @if ($banner1->button_text && $banner1->button_link)
						  <a href='{{ url("{$banner1->button_link}") }}' class="btnDefault red">{{ $banner1->button_text }}</a>
            @endif
					</div>
					<div class="blocoAtalhoBg" @isset($banner2->photo) style="background-image: url('{{ asset('storage/' . $banner2->photo) }}');" @endisset>
						<p>{{ $banner2->title }}</p>
						@if ($banner2->button_text && $banner2->button_link)
						  <a href='{{ url("{$banner2->button_link}") }}' class="btnDefault blue">{{ $banner2->button_text }}</a>
            @endif
					</div>
				</div>
			</div>

		</div>

	</div>

</section>

<section class="previewBlog bgWhite">
		
	<div class="container-fluid">

		<h2>Novidades Relacionadas</h2>
		
		<div class="row">
			
			@foreach ($related as $relatedNews)
			<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
				
				<div class="blocoBlog">
					
          @if(!empty($relatedNews->photo))
            <div class="imageBlog">
              <a href="/novidades/{{ $relatedNews->slug }}" title="{{ $relatedNews->title }}">
              <img src="{{ asset('storage/' . $relatedNews->photo) }}" alt="{{ $relatedNews->title }}" style="max-width: 100%;">
              </a>
            </div>
          @endif
					<div class="infoBlog">
						<p>{{ $relatedNews->title }}</p>
						<a href="/novidades/{{ $relatedNews->slug }}" class="btnLink">Leia mais +</a>
					</div>

				</div>

			</div>
			@endforeach

		</div>

	</div>

</section>
@endsection