@extends('layouts.admin')
@section('title', 'Modelos')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="fw-bold py-3 m-0">
    <span class="text-muted fw-light">Páginas / </span> Modelos
  </h4>
  <a href="{{ route('pages.templates.create') }}" type="button" class="btn btn-primary">Nova Página</a>
</div>

 <div class="card">
  <h5 class="card-header">Modelos</h5>
  <div class="d-flex align-items-center p-4">
    <div class="d-flex col form-group align-items-end ">
      <div class="me-3 flex-grow-1">
        <span>Pesquisa:</span>
        <input class="form-control" id="search" onkeyup="search()">
      </div>
    </div>
    <div class="col-3">
      <span>Modelos:</span>
      <select class="form-select" id="filter-template" onchange="search()">
        <option value="">Todos</option>
        <option value="services">Serviços</option>
        <option value="contracts">Contratos</option>
        <option value="default">Padrão</option>
      </select>
    </div>
  </div>
  <div class="table-container">
    @include('admin.pages.templates-table', ['pages' => $pages])
  </div>
</div>

  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  <script nonce="{{ csp_nonce() }}">
    $( "#sortable" ).sortable();

    $( ".accordion-button" ).on('click', function(){
      $(".accordion-collapse").collapse('hide');
    });

    function search() {
      var template = $('#filter-template').val();
      var search = $('#search').val();
      var params = {}

      if (template != '')
        params['template'] = template;

      if (search != '')
        params['search'] = search;

      axios.get("{{ route('pages.templates.filter') }}", {
        params: params
      })
        .then((r) => {
          $('.table-container').html(r.data)
        });
    }
  </script>
@endsection