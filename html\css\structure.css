/* reset */
html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, blockquote, pre, abbr, acronym, address, big, cite, code, del, dfn, em, font, img, ins, kbd, q, s, samp, small, strike, sub, sup, tt, var, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td {
  margin: 0px;
  padding: 0px;
  border: 0;
  font-style: normal;
  outline: none;
  list-style: none; 
}

body {
  margin: 0;
  font-family: sans-serif; 
}

input,
textarea{
  width: 100%;
}

* {
  outline: none; 
}

a, a:active, a:visited, a:hover {
  cursor: pointer;
}

header, nav, section, article, main, aside, footer {
  display: block; 
}

*, *:after, *:before {
  box-sizing: border-box; 
}

html {
  font-size: 62.5%; 
}

article {
  -webkit-font-smoothing: antialiased;
  font-smoothing: antialiased;
  text-rendering: optimizeLegibility; 
}

.clear:before, .clear:after {
  content: " ";
  display: table; 
}

.clear:after {
  clear: both; 
}

.clear {
  *zoom: 1; 
}

.clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden; 
}

.clearfix {
  display: block;
  height: 1%; 
}

.clearfix:before, .clearfix:after {
  content: " ";
  display: table; 
}

.clearfix:after {
  clear: both; 
}

.clearfix {
  *zoom: 1; 
}

img {

  vertical-align: bottom;
  height: auto; 
}

.alignnone {
  margin: 5px 20px 20px 5px; 
}

.aligncenter {
  margin: 5px auto 5px auto; 
}

.alignright {
  margin: 5px 0px 20px 20px;
  float: right; 
}

.alignleft {
  margin: 5px 20px 5px 0px;
  float: left; 
}

.wp-caption .size-full {
  width: 100%;
  height: auto; 
}

.wp-caption.alignnone {
  margin: 5px 0px; 
}

.wp-caption.alignleft {
  margin: 5px 20px 5px 0px; 
}

.wp-caption.alignright {
  margin: 5px 0px 5px 20px; 
}

.gallery-caption, .wp-caption p.wp-caption-text {
  font-size: 1.3em;
  margin: 0;
  color: #67b4b9;
  font-weight: bold;
  font-style: italic; 
}

p {
  /*font-size: 1.4em;*/
  margin: 20px 0; 
}

b, strong {
  font-weight: bold; 
}

i, em {
  font-style: italic; 
}

ul li {
  list-style: inside disc; 
}

ol li {
  list-style: inside decimal; 
}