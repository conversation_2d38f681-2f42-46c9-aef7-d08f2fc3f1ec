@extends('layouts.admin')
@section('title', 'Boletos')

@section('content')
<h4 class="fw-bold py-3 mb-4">
  <span class="text-muted fw-light">Boletos / </span> Novo
</h4>

<form method="POST" action="{{ route('boletos.generate') }}">
  @csrf

  <div class="card">
    <div class="card-header">
      <h5>Dados do Ato</h5>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-6">
          <label class="form-label">Protocolo</label>
          <input type="text" class="form-control @error('protocol') is-invalid @enderror numeric" placeholder="Digite o número do protocolo" name="protocol" value="{{ old('protocol') }}" required>
        </div>
        <div class="mb-3 col-sm-6">
          <label class="form-label">Natureza</label>
          <input type="text" class="form-control @error('nature') is-invalid @enderror" placeholder="Digite a natureza do ato" name="nature" value="{{ old('nature') }}" required>
        </div>
      </div>

      <div class="row">
        <div class="mb-3 col-sm-6">
          <label class="form-label">Escrevente</label>
          @if (Auth::guard('admin')->user()->can('manage_boleto'))
            <select class="form-select" id="clerk" name="clerk" required>
              <option value="">Selecionar...</option>
              @foreach ($clerks as $clerk)
                <option value="{{ $clerk->id }}" @selected(old('clerk') == $clerk->id)>{{ $clerk->name }}</option>
              @endforeach
            </select>
          @elseif (Auth::guard('admin')->user()->can('use_boleto'))
            <input type="text" class="form-control" required readonly value="{{ Auth::guard('admin')->user()->name }}">
            <input type="hidden" id="clerk" name="clerk" value="{{ Auth::guard('admin')->user()->id }}">
          @endif
        </div>
      </div>
    </div>
  </div>

  <div class="card mt-4">
    <div class="card-header">
      <h5>Dados da Parte</h5>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-6">
          <label class="form-label">Nome / Razão social</label>
          <input type="text" class="form-control @error('name') is-invalid @enderror" placeholder="Digite nome/razão" name="name" value="{{ old('name') }}" required>
        </div>
        <div class="mb-3 col-sm-6">
          <label class="form-label">CPF / CNPJ</label>
          <input type="text" class="form-control @error('cpf_cnpj') is-invalid @enderror" placeholder="Digite CPF/CNPJ" name="cpf_cnpj" value="{{ old('cpf_cnpj') }}" required>
        </div>
      </div>

      <div class="row">
        <div class="mb-3 col-sm-4">
          <label class="form-label">CEP</label>
          <div class="input-group align-items-center">
            <input type="text" class="form-control @error('cep') is-invalid @enderror" placeholder="Digite o CEP" name="cep" id="cep" value="{{ old('cep') }}" onkeyup="searchAddressByCEP()" required data-mask="00000-000" pattern=".{8,}">
            <button class="btn btn-primary" type="button" onclick="searchAddressByCEP()">Buscar</button>
          </div>
        </div>
        <div class="mb-3 col-sm-4">
          <label class="form-label">Logradouro (rua, avenida, travessa)</label>
          <input type="text" id="place_type" class="form-control @error('place_type') is-invalid @enderror" placeholder="Digite o logradouro" name="place_type" value="{{ old('place_type') }}" required>
        </div>
        <div class="mb-3 col-sm-4">
          <label class="form-label">Número</label>
          <input type="text" class="form-control @error('number') is-invalid @enderror" placeholder="Digite o número" name="number" value="{{ old('number') }}" required>
        </div>
      </div>

      <div class="row">
        <div class="mb-3 col-sm-4">
          <label class="form-label">Cidade</label>
          <input type="text" class="form-control @error('city') is-invalid @enderror" placeholder="Digite a cidade" name="city" id="city" value="{{ old('city') }}" required>
        </div>
        <div class="mb-3 col-sm-4">
          <label class="form-label">Bairro</label>
          <input type="text" id="neighborhood" class="form-control @error('neighborhood') is-invalid @enderror" placeholder="Digite o logradouro" name="neighborhood" value="{{ old('neighborhood') }}" required>
        </div>
        <div class="mb-3 col-sm-4">
          <label class="form-label">UF</label>
          <input type="text" id="uf" class="form-control @error('uf') is-invalid @enderror" placeholder="Digite o uf" name="uf" value="{{ old('uf') }}" required>
        </div>
      </div>

      <div class="row">
        <div class="mb-3 col-sm-12">
          <label class="form-label">Complemento</label>
          <input type="text" class="form-control @error('complement') is-invalid @enderror" placeholder="Digite o complemento" name="complement" value="{{ old('complement') }}">
        </div>
      </div>
    </div>
  </div>

  <div class="card mt-4">
    <div class="card-header">
      <h5>Valor do Ato</h5>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="mb-3 col-sm-4">
          <div id="previous-deposit-container" class="mb-3" @if(old('show_previous_deposit') != 'on') style="display: none;" @endif>
            <label class="form-label">Depósito prévio </label>
            <input type="text" class="form-control money @error('previous_deposit') is-invalid @enderror" placeholder="Digite o valor" name="previous_deposit" id="previous_deposit" value="{{ old('previous_deposit') }}" onkeyup="updateTotal()">
          </div>
          <input type="checkbox" class="form-check-input" name="show_previous_deposit" id="show_previous_deposit" @checked(old('show_previous_deposit') == 'on') onchange="togglePreviousDeposit()">
          <label class="form-label" for="show_previous_deposit">Cobrar depósito prévio?</label>
        </div>
      </div>

      <div class="row">
        <div class="mb-3 col-sm-4">
          <label class="form-label">Custas Integrais</label>
          <input type="text" id="total_costs" class="form-control money @error('total_costs') is-invalid @enderror" placeholder="Digite o valor" name="total_costs" value="{{ old('total_costs') }}" required onkeyup="updateTotal()">
        </div>
      </div>

      <div class="row">
        <div class="mb-3 col-sm-4">
          <label class="form-label">Outros (registros, tributos, certidões, diligências etc.)</label>
          <input type="text" id="others_costs" class="form-control money @error('others_costs') is-invalid @enderror" placeholder="Digite o valor" name="others_costs" value="{{ old('others_costs') }}" required onkeyup="updateTotal()">
        </div>
      </div>

      <div class="row">
        <div class="mb-3 col-sm-4">
          <label class="form-label">Taxa de emissão</label>
          <input type="text" class="form-control" value="R$ 2,45" readonly>
        </div>
      </div>

      <div class="row">
        <div class="mb-3 col-sm-4">
          <label class="form-label">Total</label>
          <input type="text" id="total" class="form-control money @error('total') is-invalid @enderror" placeholder="R$ 0,00" name="total" required readonly>
        </div>
      </div>
    </div>
  </div>

  <p class="mt-4 mb-1"><i><strong style="color: red;">ATENÇÃO:</strong> Não será possível editar o boleto gerado nem os dados informados nesse formulário!</i></p>


  <button class="btn btn-primary mt-3">Gerar Boleto</button>


</form>

<br><br><br>

@endsection

<script nonce="{{ csp_nonce() }}">
  setTimeout(() => {
    updateTotal();
  }, 600);

  function togglePreviousDeposit() {
    if ($('#show_previous_deposit').is(":checked")) {
      $('#previous-deposit-container').show();
    } else {
      $('#previous-deposit-container').hide();
    }

    $('#previous_deposit').val('');
    updateTotal();
  }

  function updateTotal()
  {
    var pd = removeMask($('#previous_deposit').val());
    var tc = removeMask($('#total_costs').val());
    var oc = removeMask($('#others_costs').val());
    var t = +oc + +tc + +pd + 2.45;
    t = t.toLocaleString("pt-BR", {style:"currency", currency:"BRL"});
    $('#total').val(t);
  }

  function removeMask(val)
  {
    val = val.replaceAll('.', '');
    return val.replaceAll(',', '.');
  }

  function searchAddressByCEP()
  {
    if ($('#cep').val().length < 9) return;

    $.get('https://viacep.com.br/ws/' + $('#cep').val() + '/json', function(data) {
      $('.dados-endereco').val('');
      $('#place_type').val(data.logradouro)
      $('#neighborhood').val(data.bairro)
      $('#city').val(data.localidade)
      $('#uf').val(data.uf);
      console.log(data);
    }).fail(function(){
      alert('Não foi possível encontrar dados de endereço para o CEP fornecido. Por favor, preencha-os manualmente.')
    });
  }
</script>
