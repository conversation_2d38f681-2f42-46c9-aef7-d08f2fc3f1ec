<?php

namespace App\Services\Bradesco;

use App\Models\Certificate;
use App\Services\Bradesco\Exception\PagamentoException;
use GuzzleHttp\Client;
use Sentry\SentryLaravel\SentryFacade;

class Pagamento {

    public static function generateTransferRequest($codigo, $valor, Comprador $comprador) {
        date_default_timezone_set('America/Sao_Paulo');
        $transferRequest = new \stdClass();
        $pedido = new \stdClass();

        $transferRequest->merchant_id = 100005163; // BRADESCO_MERCHANT_ID .env
        $transferRequest->meio_pagamento = 800; // BRADESCO_COD_MEIO_PAGAMENTO_TRANSFERENCIA .env

        $pedido->numero = 'ped_'.$codigo.'_'.random_int(1000000,9000000);
        $pedido->valor = $valor;
        $pedido->descricao = 'Certidão de Ato Notarial Arquivado';

        $transferRequest->pedido = $pedido;
        $transferRequest->comprador = $comprador;

        return self::doTransferRequest($transferRequest);
    }

    public static function generateBoletoRequest($codigo, $valor, Comprador $comprador, Certificate $certidao = null, $sedex = null, $customidia = null, $protocoloAto = '') {
        $boletoRequest = new \stdClass();
        $pedido = new \stdClass();
        $boleto = new Boleto($valor, $certidao, $sedex, $customidia, $protocoloAto);

        $boletoRequest->merchant_id = 100005163; // BRADESCO_MERCHANT_ID .env
        $boletoRequest->meio_pagamento = 300; // BRADESCO_COD_MEIO_PAGAMENTO_BOLETO .env

        $pedido->numero = (string) random_int(1000000,9000000);
        $pedido->valor = $valor;

        $pedido->descricao = 'Pagamento de Ato Notarial';

        if(!empty($certidao)) {
            $pedido->descricao = 'Certidão de Ato Notarial Arquivado';
        }

        $boletoRequest->pedido = $pedido;
        $boletoRequest->comprador = $comprador;
        $boletoRequest->boleto = $boleto;

        try {
            return self::doBoletoRequest($boletoRequest);
        } catch (PagamentoException $e) {
            //app()->captureException($e);
            $paymentData = new \stdClass();
            $paymentData->MessageError = $e->getMessage();
            $paymentData->error = true;
            return $paymentData;
        }
    }


    private static function doTransferRequest($transferRequest) {
        $uri = "https://meiosdepagamentobradesco.com.br/transf/transacao"; // BRADESCO_TRANSFER_URI .env
        $headers = [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'Authorization' => 'Basic ' . base64_encode(100005163 . ':' . 'mfISZvO1q7R4jnT9aEtN5wj-0RlTODnEbuwv_zn4PxY') // BRADESCO_MERCHANT_ID:BRADESCO_KEY .env
        ];
        $body = json_encode($transferRequest);

        $client = new Client();
        $response = $client->post($uri, ['headers' => $headers, 'body' => $body]);

        $responseBody = $response->getBody();
        $response = json_decode($responseBody);
        $transferUrl = $response->transferencia->url_acesso;

        return $transferUrl;
    }

    private static function doBoletoRequest($transferRequest) {
        $uri = "https://meiosdepagamentobradesco.com.br/apiboleto/transacao"; // BRADESCO_BOLETO_URI .env
        $headers = [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'Authorization' => 'Basic ' . base64_encode(100005163 . ':' . 'mfISZvO1q7R4jnT9aEtN5wj-0RlTODnEbuwv_zn4PxY') // BRADESCO_MERCHANT_ID:BRADESCO_KEY .env
        ];
        $body = json_encode($transferRequest);


        $client = new Client();
        $response = $client->post($uri, ['headers' => $headers, 'body' => $body]);

        $responseBody = $response->getBody();
        $response = json_decode($responseBody);

        $paymentData = new \stdClass();

        if(empty($response->boleto)) {
          throw new PagamentoException($response->status->mensagem, $response->status->detalhes);
        }
        $paymentData->url = $response->boleto->url_acesso;
        $paymentData->linha = $response->boleto->linha_digitavel;
        $paymentData->linha_formatada = $response->boleto->linha_digitavel_formatada;
        $paymentData->data_emissao = $response->boleto->data_geracao;
        $paymentData->error = false;

        return $paymentData;
    }
}
