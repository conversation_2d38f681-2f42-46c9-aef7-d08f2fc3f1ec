@extends('layouts.app')
@section('title', $page->title)

@section('content')
<section class="contentServico contentDownload txtcontent">
	
	<div class="container-fluid">
		
		<div class="row">
			
			<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12">

				<h1>{{ $page->title }}</h1>

        @foreach (json_decode($page->template_settings) as $session)
          <p>
            @if($session->type == 'content')
              @if($session->title)
                @if($session->red_title && $session->highlighted_title)
                  <h2 class="red" id="{{ $session->title }}">{{ $session->title }}</h2>
                @elseif ($session->red_title)
                  <p class="red" id="{{ $session->title }}">{{ $session->title }}</p>
                @elseif ($session->highlighted_title)
                  <h2 id="{{ $session->title }}">{{ $session->title }}</h2>
                @else
                  <strong id="{{ $session->title }}">{{ $session->title }}</strong><br>
                @endif
              @endif

              {{-- quando enviar o formulário, oculta todo o texto da página --}}
              @if(!session('success'))
                  @if ($session->content_block)
                    <div class="boxBgGray">
                      {!! $session->content !!}
                    </div>
                  @else
                    <div class="contentBlock">
                      {!! $session->content !!}
                    </div>
                  @endif
              @endif
            @endif 
          </p>
        @endforeach

        <form id="a" class="includePage" action="{{ route('form.filling.save') . '#anchor' }}" method="POST" enctype="multipart/form-data" style="position: relative">
          @if(!session('success'))
            <h2 class="red mb-3" id="form">Preencha o formulário</h2>

            <div id="anchor" alt="form anchor" style="top: -200px; position: absolute;z-index: -1000;"></div>
            @csrf
            <input type="hidden" name="page_title" value="{{ $page->title }}">
          
            <div class="formControl">
              <label>Nome Completo</label>
              <input type="text" name="nome" value="{{ old('nome') }}" required>
            </div>

            <div class="formControl">
              <label>E-mail</label>
              <input type="text" name="email" value="{{ old('email') }}" required>
            </div>

            <div class="formControl check mb-1">
              <input type="checkbox" id="accordingly_checkbox" name="accordingly_checkbox" required>
              <label for="accordingly_checkbox">Li, estou de acordo com a <a href="/politica-de-privacidade" target="_blank">política de privacidade</a></label>
            </div>
            <div class="formControl check mt-1">
              <input type="checkbox" id="want_messages_checkbox" name="want_messages_checkbox">
              <label for="want_messages_checkbox">Desejo receber mensagens informativas do 26º Tabelionato de Notas.</label>
            </div>
            <div class="reCaptcha">
              {!! htmlFormSnippet() !!}
            </div>
            <div class="formControl">
              <button class="btnDefault red">Enviar</button>
            </div>
          @else
            @foreach (json_decode($page->template_settings) as $session)
              <p>
                @if ($session->type == 'files')
                  <h2 class="red mb-3" id="{{ $session->title }}">{{ $session->title }}</h2>
                  <div class="downloads">
                    @foreach ($session->files as $file)
                      <a href="{{ route('pages.templates.file.download', ['file' => $file]) }}">
                        <i class="download"></i>
                        {{ $file->name }}
                      </a>
                    @endforeach
                  </div>
                @endif
              </p>
            @endforeach
          @endif
          
          @if(session('form_error') || $errors->any())
            <div class="message error">
              <p>Formulário não enviado.</p>
              @if ($errors->any())
                <ul class="mt-2">
                  @foreach ($errors->all() as $error)
                    <li style="color:#a94442;">{{ $error }}</li>
                  @endforeach
                </ul>
              @endif
            </div>
          @endif
        </form>
			</div>

			<div class="col-lg-3 col-md-3 col-sm-12 col-xs-12">
				<div class="boxWidgetSidebar">
					<p class="red"><strong>O que deseja?</strong></p>
          <a href="/servicos/procuracoes">Solicitar por escritura pública</a><br>
          <a href="#anchor">Download do documento</a><br>
				</div>
			</div>

		</div>

	</div>

</section>

@endsection