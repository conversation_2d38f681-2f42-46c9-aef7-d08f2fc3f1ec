.padlock::before{
	content: url('/img/padlock.svg');
	display: block;
	margin-right: 10px;
}

.subHeader{
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10px 0;
	border-bottom: 2px solid white;
}

.subHeader .menuAtalhos:first-child li{
	margin-right: 20px;
}

.subHeader .menuAtalhos:last-child li{
	margin-left: 20px;
}

.menuAtalhos{
	display: flex;
	align-items: center;
	margin: 0;
    padding: 0;
}

.menuAtalhos li{
	list-style: none;
}

.menuAtalhos li a{
	font-size: 12px;
	line-height: 14px;
	color: var(--cinza);
	text-transform: uppercase;
	text-decoration: none;
}

.contentHeader{
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-top: 50px;
	padding-bottom: 20px;
	position: relative;
}

.menuHeader{
	display: flex;
	align-items: center;
}

.menuHeader li{
	list-style: none;
	/*margin-left: 50px;*/
	margin-left: 25px;
}

.menuHeader li:first-child{
	margin-left: 0;
}

.menuHeader li a{
	font-size: 16px;
	line-height: 22px;
	text-decoration: none;
	text-transform: uppercase;
	color: var(--azul);
	display: flex;
	align-items: center;
}

footer{
	background-color: var(--azul);
}

footer .container-fluid{
	/*height: 100%;*/
	height: auto;
	padding-top: 60px;
}

footer .container-fluid::before{
	content: url('/img/linguetaBase.svg');
	width: 100px;
	height: 49px;
	position: absolute;
	top: -9px;
	left: 10px;
	transform: rotate(180deg);
}

footer .logo img{
	filter: brightness(1000);
}

.contentFooter{
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.menuFooter{
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.menuFooter li{
	list-style: none;
	margin-left: 30px;
}

.menuFooter li a{
	font-size: 16px;
	line-height: 22px;
	color: white;
	text-transform: uppercase;
	text-decoration: none;
}

footer .subTitle{
	font-size: 16px;
	line-height: 22px;
	color: white;
	text-transform: uppercase;
	margin-bottom: 10px;
}

footer .row{
	margin: 50px 0;
}

footer img{
	width: auto;
}

.textAndress,
.textAndress a{
	font-family: 'Open Sans', sans-serif;
	font-size: 14px;
	line-height: 24px;
	color: white;
	text-decoration: none;
}

.btnsFooter{
	display: flex;
	align-items: center;
}

.btnsFooter a{
	font-size: 14px;
	line-height: 18px;
	color: white;
	font-family: 'Open Sans', sans-serif;
	text-decoration: underline;
	margin-right: 10px;
	display: flex;
	align-items: center;
}

.btnsFooter a::after{
	content: '';
	display: flex;
	width: 1px;
	height: 12px;
	background-color: white;
	margin-left: 10px;
}

.btnsFooter a:last-child::after{
	display: none;
}

.redesSociais{
	display: flex;
	align-items: center;
	margin-top: 30px;
}

.redesSociais li{
	list-style: none;
	margin-right: 20px;
}

.redesSociais li svg{
	width: auto;
}

.copyright{
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.infoCopyright{
	display: flex;
	align-items: center;
}

.infoCopyright *{
	font-size: 12px;
	line-height: 16px;
	color: white;
	margin: 10px 0;
	font-family: 'Open Sans', sans-serif;
}

.infoCopyright a{
	text-transform: uppercase;
	margin-left: 10px;
	text-decoration: none;
}

.menuHeader > li{
	padding-bottom: 30px;
	margin-bottom: -30px;
}

.hasSubMenu:hover .subMenu{
	visibility: visible;
}

.subMenu{
	position: absolute;
	width: 100%;
	top: 111px;
	left: 0;	
	background-color: white;
    display: flex;
    flex: 0 1 auto;
    flex-direction: row;
    flex-wrap: wrap;
    z-index: 9;
    /*padding-top: 50px;*/
    /*padding-bottom: 50px;*/
    visibility: hidden;
}

header .subMenu{
	/*
	-webkit-box-shadow: 0px 5px 5px 0px rgba(0,0,0,0.1);
	-moz-box-shadow: 0px 5px 5px 0px rgba(0,0,0,0.1);
	box-shadow: 0px 5px 5px 0px rgba(0,0,0,0.1);
	*/
	-webkit-box-shadow: 0px 3px 1px 0px rgba(0,0,0,0.09); 
	-moz-box-shadow: 0px 3px 1px 0px rgba(0,0,0,0.09);
	box-shadow: 0px 3px 1px 0px rgba(0,0,0,0.09);
}

.subMenu:before,
.subMenu:after{
	content: '';
	display: block;
	width: 100%;
	height: 100%;
	background-color: white;
    position: absolute;
	top: 0;
}

header{
	position: fixed;
	width: 100%;
	top: 0;
	transition: 0.3s all;
	z-index: 9;
}

.debug{
	display: block;
	width: 100%;
	height: 147px;
}

header.header-fixed{
  z-index: 20000;
	transition: 0.3s all;
	box-shadow: 0px 5px 5px 0px rgba(0,0,0,0.1);
	transform: translateY(-66px);
	background-color: white;
}

header .subMenu:before,
header .subMenu:after{
	/*
	-webkit-box-shadow: 0px 5px 5px 0px rgba(0,0,0,0.1);
	-moz-box-shadow: 0px 5px 5px 0px rgba(0,0,0,0.1);
	box-shadow: 0px 5px 5px 0px rgba(0,0,0,0.1);
	*/
	z-index: -1;
	-webkit-box-shadow: 0px 3px 1px 0px rgba(0,0,0,0.09); 
	-moz-box-shadow: 0px 3px 1px 0px rgba(0,0,0,0.09);
	box-shadow: 0px 3px 1px 0px rgba(0,0,0,0.09);
}

.subMenu:before{
    left: -100%;
}

.subMenu:after{
    right: -100%;
}

.subMenu > li{
	width: 20%;
	margin: 0;
	padding-bottom: 100px;
	padding-right: 20px;
	position: relative;
	padding-top: 50px;
	background-color: white;
}

.subMenu li ul li a{
	font-size: 14px;
	line-height: 24px;
	color: var(--cinza);
	text-transform: unset;
}

.subMenu li ul li:first-child{
	margin-bottom: 30px;
}

.subMenu li ul li:first-child a{
	font-size: 16px;
	line-height: 22px;
	text-transform: uppercase;
	color: #E83946;
}

.subMenu .menuLink{
	margin-top: 30px;
	position: absolute;
	bottom: 50px;
}

.subMenu .menuLink a{
	text-decoration: underline !important;
}

.subMenu li{
	margin-left: 0 !important;
}

.open-menu{
	width: 64px;
	border: none;
	background: transparent;
	display: none;
}

.open-menu svg{
	width: 54px;
	margin: 0;
	height: auto;
}

.line {
	fill: none;
	stroke: var(--azul);
	stroke-width: 6;
	transition: stroke-dasharray 600ms cubic-bezier(0.4, 0, 0.2, 1),
	stroke-dashoffset 600ms cubic-bezier(0.4, 0, 0.2, 1);
}

.line1 {
	stroke-dasharray: 60 207;
	stroke-width: 6;
}

.line2 {
	stroke-dasharray: 60 60;
	stroke-width: 6;
}

.line3 {
	stroke-dasharray: 60 207;
	stroke-width: 6;
}

.opened .line1 {
	stroke-dasharray: 90 207;
	stroke-dashoffset: -134;
	stroke-width: 6;
}

.opened .line2 {
	stroke-dasharray: 1 60;
	stroke-dashoffset: -30;
	stroke-width: 6;
}

.opened .line3 {
	stroke-dasharray: 90 207;
	stroke-dashoffset: -134;
	stroke-width: 6;
}

.closeSubMenu{
	display: none !important;
}

.menu-mobile {
  display: none;
}

.subMenu li {
	/*background: lawngreen;*/
	max-height: 70vh;
}

.subMenuColItem {
	overflow-y: auto;
	max-height: 60vh;
	padding-bottom: 5px;
	scrollbar-color: rgb(134, 134, 134) rgb(233, 233, 233);
  	scrollbar-width: thin;
	/*background: yellow;*/
}

@media(max-width: 1024px){
  .menu-mobile {
    display: block;
  }

	header .closeSubMenu{
		display: block !important;
	}
	.closeSubMenu span{
		font-size: 16px;
		font-weight: bold;
		color: var(--azul);
	}
	.open-menu{
		display: block;
	}
	header{
		background-color: white;
	}
	.contentHeader{
		padding-top: 20px;
	}
	header nav{
		position: fixed;
	    top: 99px;
	    width: 100%;
	    min-height: 100vh;
	    background-color: white;
	    z-index: 1;
	    left: 0;
	    transform: translateX(-150%);
	    transition: 0.3s all;
	}
	header.active nav{
		transform: translateX(0);
	    transition: 0.3s all;
	}
	.searchForm{
		position: fixed;
		top: 139px;
		z-index: 2;
		max-width: calc(100% - 20px);
		transform: translateX(-150%);
	    transition: 0.3s all;
	}
	header.active .searchForm{
		transform: translateX(0);
	    transition: 0.3s all;
	}
	.menuHeader{
		flex-direction: column;
		align-items: flex-start;
		padding: 50px 0px;
		padding-top: 100px;
	}
	.menuHeader li{
		margin-bottom: 10px;
		padding-bottom: 0;
		margin-left: 15px;
		width: 100%;
		padding-right: 0;
	}
	.contentIncludeMenu .subMenu > li{
		width: 100%;
		padding: 25px 0;
	}
	.subMenu li ul li:first-child{
		margin-bottom: 10px;
	}
	header .hasSubMenu .active ~ .subMenu{
		transform: translateX(0);
		transition: 0.3s all;
	}
	header .hasSubMenu .subMenu{
		visibility: visible !important;
		transform: translateX(-100%);
		transition: 0.3s all;
		overflow-y: scroll;
    	height: 100%;
	}
	header .subMenu{
		padding: 10px 15px;
    	top: 100px;
	}
	header .subMenu ul{
		padding: 25px 0;
	}
	header .subMenu > li{
		padding: 0;
	}
	header .menuHeader li:first-child{
		margin-top: 0 !important;
	}
	.menuHeader li:first-child{
		margin-left: 15px;
	}
	.subMenu:before, .subMenu:after{
		display: none !important;
	}
	.menuFooter{
		flex-direction: column;
		align-items: flex-start;
		margin-top: 50px;
	}
	.menuFooter li{
		margin-left: 10px !important;
		margin-bottom: 10px !important;
	}
	.subHeader{
    display: none;
		position: fixed;
	    bottom: -2px;
	    z-index: 2;
	    background-color: var(--cinza);
	    width: 100%;
	    left: 0;
	    padding: 20px 10px;
	    padding-bottom: 10px;
	    transform: translateX(-150%);
	    transition: 0.3s all;
	}
	header.active .subHeader{
		transform: translateX(0);
	    transition: 0.3s all;
	}
	.subHeader li{
		margin-bottom: 10px;
	}
	.subHeader li a{
		color: white;
	}
	.menuAtalhos, .menuAtalhos{
		width: 100%;
		flex-direction: column;
		align-items: flex-start;
	}
	.subHeader .menuAtalhos:last-child li{
		margin-left: 0;
	}
	.subHeader .menuAtalhos:first-child li{
		margin-right: 0;
	}
	.debug{
		height: 99px;
	}
	.contentFooter{
		flex-direction: column;
		align-items: flex-start;
	}
	footer .row{
		margin-top: 10px;
		margin-bottom: 40px !important;
	}
	footer .clCenter{
		margin: 30px 0;
	}
	.copyright{
		flex-direction: column;
		padding-bottom: 20px;
		margin-top: 20px;
	}
	.infoCopyright {
		flex-direction: column;
		margin-bottom: 20px;
	}
	.infoCopyright *{
		text-align: center;
	}
	.subMenu li {
		max-height: unset;
	}
	.subMenuColItem {
		overflow-y: unset;
		max-height: unset;
  }
}