<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Queue\SerializesModels;

class CertificateRequestProtocol extends Mailable
{
    use Queueable, SerializesModels;

    public $data;
    public $cpf;
    public $email;
    public $firstName;
    public $subject;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($data, $r)
    {
      $this->data = $data;

      $this->cpf = (!empty($r->CPF)) ? $r->CPF : $r->CNPJ;
      $this->email = $r->email;

      $this->firstName = explode(" ", $data['nome'])[0];
      $this->subject = $this->firstName . ' recebemos seu pedido de certidão de ato notarial - Protocolo ' . $data['protocolo'];
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            from: new Address(env('MAIL_FROM_ADDRESS'), env('MAIL_FROM_NAME')),
            subject: $this->subject,
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(
            view: 'emails.certificate-request-protocol',
        );
    }
}
