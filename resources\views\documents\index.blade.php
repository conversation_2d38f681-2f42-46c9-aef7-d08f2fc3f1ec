@extends('layouts.my-account')

@section('content')

<section class="account txtcontent">

	<div class="container-fluid">
    <div>
      <div class="breadcrumb">
        <a href="{{ route('my.services') }}">Meus Serviços</a>  >
        <span>Envio de Documentos</span>
      </div>
      <div class="d-flex justify-content-between align-items-start">
        <h1>Envio de Documentos</h1>
        <a href="{{ route('my.services') }}" class="btnDefault" style="background: #708082;">Voltar</a>
      </div>
    </div>
    <div class="row">
      <form>
        <div class="d-flex justify-content-between" style="min-width: 100%;">
          <div class="formControl" style="max-width: 200px; margin-bottom:0;">
            <select name="type" onchange="filterStatus(this)">
              <option value="">Mostrar todos documentos</option>
              <option value="opened" @selected(request()->status == 'opened')>Abertos</option>
              <option value="closed" @selected(request()->status == 'closed')>Fechados</option>
              <option value="canceled" @selected(request()->status == 'canceled')>Cancelados</option>
            </select>
          </div>
          <div>
            <a href="{{ route('documents.create') }}" class="btnDefault">Novo</a>
          </div>
        </div>
      </form>
    </div>
		<div class="row">
			<div style="min-width: 100%" class="txtcontenttable">
        <table class="registros">
          <tbody>
            <tr class="head">
            <th>Ticket</th>
            <th>Data</th>
            <th>Tipo de Doc</th>
            <th>Parte</th>
            <th>CPF da Parte</th>
            <th>Escrevente</th>
            <th>Status</th>
            <th>Ação</th>
          </tr>
          @foreach ($documents as $doc)
            <tr>
              <td>{{ $doc->id }}</td>
              <td>{{ $doc->created_at->format('d/m/Y') }}</td>
              <td>{{ $doc->document_type }}</td>
              @if($doc->is_part)
                <td>{{ $doc->client->name }}</td>
                <td>{{ $doc->client->cpf }}</td>
              @else
                <td>{{ $doc->part_name }}</td>
                <td>{{ $doc->part_cpf }}</td>
              @endif
              <td>{{ $doc->clerk->name ?? '-' }}</td>
              @if ($doc->status != 'dropped')
                <td>{{ ucfirst(__('messages.' . $doc->status)) }}</td>
              @else 
                <td>{{ ucfirst(__('messages.closed')) }}</td>
              @endif
              <td><a class="btnTable" href="{{ route('documents.edit', [$doc->id]) }}"><span class="fa-solid"></span>VER</a></td>
            </tr>
          @endforeach
          <tr>
        </tr></tbody></table>
      </div>

		</div>

	</div>

</section>
@endsection

<script nonce="{{ csp_nonce() }}">
  function filterStatus(select) {
      var status = $(select).val();
      var url = new URL(window.location.href);
      url.searchParams.set('status', status);
      window.location = url.href;
    }
</script>