@extends('layouts.app')
@section('title', 'Pagamento Online')

@section('content')
<section class="account pagamento-online txtcontent">

	<div class="container-fluid">

		<div class="row">
			
			<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">

				<h1 style="margin-bottom: 32px;">Pagamento Online</h1>

			</div>

		</div>

		<div class="row">
			<div class="resultadoBusca m-hidde">
				
				<div class="headBusca invertColor">
					<h2>Dados do Ato Notarial</h2>
				</div>

				<table>
				  <tbody><tr>
				    <td>Protocolo</td>
				    <td>{{ $boleto->protocol }}</td>
				  </tr>
				  <tr>
				    <td>Natureza</td>
				    <td>{{ $boleto->nature }}</td>
				  </tr>
				  <tr>
				    <td>Escrevente responsável</td>
				    <td>{{ $boleto->clerk->name }}</td>
				  </tr>
				</tbody></table>

			</div>

			<div class="resultadoBusca m-hidde">
				
				<div class="headBusca invertColor">
					<h2>Dados da Parte Pagadora</h2>
				</div>

				<table>
				  <tbody><tr>
				    <td>Nome Completo</td>
				    <td>{{ $boleto->name }}</td>
				  </tr>
				  <tr>
				    <td>CPF/CNPJ</td>
				    <td>{{ $boleto->document }}</td>
				  </tr>
				</tbody></table>

			</div>

			<div class="resultadoBusca m-hidde">
				
				<div class="headBusca invertColor">
					<h2>Valor do Ato Notarial</h2>
				</div>

				<table>
				  <tbody><tr>
				    <td>Depósito Prévio</td>
				    <td>R$ {{ number_format($boleto->previous_deposit, 2, ',', '.') }}</td>
				  </tr>
				  <tr>
				    <td>Custas Integrais</td>
				    <td>R$ {{ number_format($boleto->total_costs, 2, ',', '.') }}</td>
				  </tr>
				  <tr>
				    <td>Outros* (registros, tributos, certidões, diligências, etc.)</td>
				    <td>R$ {{ number_format($boleto->others_costs, 2, ',', '.') }}</td>
				  </tr>
          <tr>
				    <td>Taxa de Emissão</td>
				    <td>R$ 2,45</td>
				  </tr>
				  <tr>
				    <td><strong>Saldo a pagar</strong></td>
            @php
              $total = $boleto->previous_deposit + $boleto->total_costs + $boleto->others_costs + 2.45;
            @endphp
				    <td>R$ {{ number_format($total, 2, ',', '.') }}</td>
				  </tr>
				  <tr>
				  	<td colspan="2"><span>*Os valores estarão discriminados no recibo do ato.</span></td>
				  </tr>
				</tbody></table>

			</div>

			<div class="resultadoBusca m-hidde">
				
				<div class="headBusca invertColor">
					<h2>Boleto</h2>
				</div>

				<form id="send-mail-form">
					<div class="formControl">
						<label>Linha digitável</label>
						<div class="d-flex">
              <input type="text" value="{{ $boleto->barcode }}" id="barcode" readonly style="background: #eceef1">
							<button type="button" class="btnDefault" onclick="copyField(this)" data-clipboard-target="#barcode">Copiar</button>
						</div>
					</div>
					<hr>
					<div class="formControl">
						<label>Enviar por e-mail</label>
					    <div class="d-flex" style="align-items: flex-start">
							<div style="width: 100%; margin-right:10px;">
                <input type="text" id="email" placeholder="Digite o e-mail" class="mb-3" required>
              </div>
							<button type="button" class="btnDefault" onclick="sendMail(this)">Enviar</button>
						</div>
					</div>
					<hr>
					<div class="formControl">
						<label>Impressão / Download</label>
						<div>
							<a class="btnDefault" href="{{ $boleto->link }}" target="_blank">Imprimir / Fazer Download</a>
						</div>
					</div>
				</form>

			</div>
			
		</div>

	</div>

</section>
<script nonce="{{ csp_nonce() }}">
  function copyField(button)
  {
    var copyText = document.getElementById("barcode");
    copyText.select();
    copyText.setSelectionRange(0, 99999); // For mobile devices
    navigator.clipboard.writeText(copyText.value);

    $(button).html('Copiado!');
  }

  function sendMail(button)
  {
    jQuery.validator.setDefaults({
      errorClass: "error"
    });
    if (!$("#send-mail-form").valid())
      return;

    $.get("{{ route('boletos.send_mail') }}", {
      to: $('#email').val(),
      id: "{{ $boleto->id }}"
    }, function() {
      $(button).html('Enviado!');
    });
  }
</script>
@endsection



