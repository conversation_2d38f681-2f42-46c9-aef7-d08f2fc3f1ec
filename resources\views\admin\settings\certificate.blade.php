@extends('layouts.admin')
@section('title', 'Certidão')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="fw-bold py-3 m-0">
    <span class="text-muted fw-light">Configurações / </span> Certidão
  </h4>
</div>

<form class="card my-4" method="POST" action="{{ route('settings.certificate.update') }}">
  @csrf
  <h5 class="card-header">Configurações de Certidão</h5>
  <div class="card-body">
    <div class="row">
      <div class="col-6">
        <label class="form-label">Valor</label>
        <input type="text" class="form-control measures @error('value') is-invalid @enderror" placeholder="Digite o valor da certidão" name="value" value="{{ $certificateSettings->value }}" required>
        <br>

        <label class="form-label">Taxa do boleto</label>
        <input type="text" class="form-control measures @error('boleto_fee') is-invalid @enderror" placeholder="Digite a taxa do boleto" name="boleto_fee" value="{{ $certificateSettings->boleto_fee }}" required>

        <br>
        <label class="form-label">Valor (Carta Registrada)</label>
        <input type="text" class="form-control measures @error('value') is-invalid @enderror" placeholder="Digite o valor da certidão" name="value_registered_letter" value="{{ $certificateSettings->value_registered_letter }}" required>

        <br>
        <label class="form-label">Valor (e-Notariado)</label>
        <input type="text" class="form-control measures @error('value') is-invalid @enderror" placeholder="Digite o valor do e-notariado" name="value_e_notariado" value="{{ $certificateSettings->value_e_notariado ?? 0.0 }}" required>

      </div>

      <div class="col-6">
        <label class="form-label">Largura do pacote (CM)</label>
        <input type="text" class="form-control measures @error('value') is-invalid @enderror" placeholder="Digite a largura" name="widthCM" value="{{ $certificateSettings->widthCM }}" required>
        <br>

        <label class="form-label">Altura do pacote (CM)</label>
        <input type="text" class="form-control measures @error('value') is-invalid @enderror" placeholder="Digite a altura" name="heightCM" value="{{ $certificateSettings->heightCM }}" required>
        <br>

        <label class="form-label">Comprimento do pacote (CM)</label>
        <input type="text" class="form-control measures @error('value') is-invalid @enderror" placeholder="Digite o comprimento" name="lengthCM" value="{{ $certificateSettings->lengthCM }}" required>
        <br>

        <label class="form-label">Peso do pacote (KG)</label>
        <input type="text" class="form-control measures @error('value') is-invalid @enderror" placeholder="Digite o peso" name="weightKG" value="{{ $certificateSettings->weightKG }}">
        <br>
      </div>
    </div>
    <hr>
    <h1 class="h5">Configurações de Pedidos de Certidão</h1>
    <div class="row">
        <div class="col-12">
            <label class="form-label">Chave PIX</label>
            <input type="text" class="form-control measures @error('value') is-invalid @enderror" placeholder="Digite a chave pix" name="ChavePix" value="{{ $certificateSettings->ChavePix ?? "" }}">
        </div>
        <div class="col-12">
            <label class="form-label">Mensagem Elegível</label>
            <textarea rows="10" class="form-control editor measures @error('value') is-invalid @enderror" name="MensagemElegivel">{{ $certificateSettings->MensagemElegivel ?? "" }}</textarea>
        </div>
        <div class="col-12">
            <label class="form-label">Mensagem Não Elegível</label>
            <textarea rows="10" class="form-control editor measures @error('value') is-invalid @enderror" name="MensagemNaoElegivel">{{ $certificateSettings->MensagemNaoElegivel ?? "" }}</textarea>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            Variáveis disponíveis: <br>
            <b>
                @{{chavepix}}, @{{valorpix}}, @{{valorboleto}}, @{{boletocodigobarras}}, @{{protocolo}}, @{{tipoato}}, @{{finalidade}}, @{{partes}}, @{{livro}}, @{{folha}}, @{{nomerazaosocial}}, @{{telefone}}, @{{email}}, @{{tipocertidao}}, @{{formaentrega}}, @{{tipoenvio}}, @{{cep}}, @{{logradouro}}, @{{numero}}, @{{complemento}}, @{{bairro}}, @{{cidade}}, @{{uf}}, @{{cpfcnpj}}
            </b>
        </div>
    </div>
    <button class="btn btn-primary mt-3">Salvar</button>
  </div>
</form>
<script nonce="{{ csp_nonce() }}" type="text/javascript">
function initEditor() {
    tinymce.init({
        selector: ".editor",
        theme: "silver",
        content_css: '/css/tinyTextArea-min.css',
        plugins: ['advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                'anchor', 'searchreplace', 'visualblocks', 'fullscreen',
                'insertdatetime', 'media', 'table', 'code', 'help', 'wordcount', 'hr'
        ],
        height: 300,
        resize: 'vertical',
        valid_elements : '*[*]',
        extended_valid_elements: 'script[language|type|src]',
        advcode_inline: true,
        tinymce_allow_script_urls: true,
        menubar: false,
        toolbar: 'undo redo | blocks | bold italic backcolor link | ' +
                'alignleft aligncenter alignright alignjustify | ' +
                'hr bullist numlist outdent indent | removeformat | code | help',
        language: 'pt_BR',
        setup: function(editor) {
            editor.on('keyup change undo', function () {
            editor.targetElm.innerHTML = editor.getContent();
            editor.targetElm.dispatchEvent(new Event("change"));
            });
        }
    });
}
$( document ).ready(function() {
    initEditor();
});
</script>
@endsection
