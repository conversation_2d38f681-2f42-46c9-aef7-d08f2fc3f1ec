<?php

namespace App\Bitrix;
use App\Models\ContactBitrix;
use App\Models\FormFilling;
use App\Http\Controllers\FormController;
use App\Models\Collaborator;

class IntegracaoBitrix{
    protected $urlBitrix;
    protected $curl;

    function __construct()
    {
        $this->urlBitrix = env('URL_BITRIX');
        $this->curl = curl_init();
        curl_setopt($this->curl, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($this->curl, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($this->curl, CURLOPT_POSTREDIR, 10);
        curl_setopt($this->curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($this->curl, CURLOPT_USERAGENT, 'Bitrix24 CRest PHP 1.36');
    }

    function retornaExecucao($parametros){
        curl_setopt($this->curl, CURLOPT_URL, $this->urlBitrix . $parametros);
        $retorno = curl_exec($this->curl);
        $erro = curl_error($this->curl);
        curl_close($this->curl);
        return $retorno;
    }

    function incluiContato($nome, $email, $telefone, $cpfCnpj){
        $nome = urlencode($nome);
        $email = urlencode($email);
        $telefone = urlencode($telefone);
        $cpfCnpj = str_replace('/', '', str_replace('.', '', str_replace('-', '', $cpfCnpj)));
        if(strlen($cpfCnpj) == 14){
            return $this->retornaExecucao("crm.company.add.json?FIELDS[NAME]=$nome&FIELDS[TITLE]=$nome&FIELDS[EMAIL][0][VALUE]=$email&FIELDS[PHONE][0][VALUE]=$telefone&FIELDS[UF_CRM_1722371594474]=$cpfCnpj&SOURCE_ID=WEB&FIELDS[OPENED]=Y&EXPORT=Y&FIELDS[UF_CRM_1722371559917]=$nome");
        }else{
            return $this->retornaExecucao("crm.contact.add.json?FIELDS[NAME]=$nome&FIELDS[EMAIL][0][VALUE]=$email&FIELDS[PHONE][0][VALUE]=$telefone&FIELDS[UF_CRM_1722370633224]=$cpfCnpj&SOURCE_ID=WEB&OPENED=Y&EXPORT=Y");
        }
    }

    function incluiDeal($nome, $pagina, $valor, $dados, $cpfCnpj, $idBitrix, $idOperador){
        $cpfCnpj = str_replace('/', '', str_replace('.', '', str_replace('-', '', $cpfCnpj)));
        $tipoPessoa = "CONTACT_ID";
        $nome = urlencode($nome);
        $valor = urlencode($valor);
        $dados = urlencode($dados);
        $cpfCnpj = urlencode($cpfCnpj);
        $idBitrix = urlencode($idBitrix);
        $pagina = urlencode($pagina);
        if(strlen($cpfCnpj) == 14){
            $tipoPessoa = "COMPANY_ID";
        }

        $tipo = "UC_332SNA";

        if($pagina != "")
            $tipo = $pagina;

        return $this->retornaExecucao("crm.deal.add.json?FIELDS[ASSIGNED_BY_ID]=$idOperador&FIELDS[TITLE]=$nome&FIELDS[SOURCE_ID]=WEB&FIELDS[OPENED]=Y&FIELDS[STAGE_ID]=NEW&FIELDS[TYPE_ID]=$tipo&FIELDS[CURRENCY_ID]=BRL&FIELDS[OPPORTUNITY]=$valor&FIELDS[SOURCE_DESCRIPTION]=$dados&FIELDS[IS_NEW]=Y&FIELDS[UF_CRM_1724867557]=$pagina&FIELDS[IS_MANUAL_OPPORTUNITY]=Y&FIELDS[$tipoPessoa]=$idBitrix");
    }

    function retornaTiposFormulario(){
        return json_decode($this->retornaExecucao("crm.status.entity.items.json?entityId=DEAL_TYPE"))->result;
    }

    function buscaContatoBitrix($id, $cpfCnpj){
        $cpfCnpj = str_replace('/', '', str_replace('.', '', str_replace('-', '', $cpfCnpj)));
        if(strlen($cpfCnpj) == 14){
            return $this->retornaExecucao("crm.company.get.json?ID=$id");
        }else{
            return $this->retornaExecucao("crm.contact.get.json?ID=$id");
        }
    }

    function buscaUsuarioBitrix($id){
        return $this->retornaExecucao("user.get.json?ID=$id");
    }

    function retornaIdContatoBitrix($cpfCNPJ){
        $cpfCNPJ = str_replace('/', '', str_replace('.', '', str_replace('-', '', $cpfCNPJ)));
        $busca = ContactBitrix::where('cpfCnpj', $cpfCNPJ);
        if($busca->count() > 0){
            return $busca->first()->idBitrix;
        }else{
            return 0;
        }
    }

    function salvarContato($cpfCNPJ, $idBitrix){
        $cpfCNPJ = str_replace('/', '', str_replace('.', '', str_replace('-', '', $cpfCNPJ)));
        $buscaContato = ContactBitrix::where('cpfCnpj', $cpfCNPJ);
        $buscaContato->delete();
        $contactBitrix = new ContactBitrix;
        $contactBitrix->cpfCnpj = $cpfCNPJ;
        $contactBitrix->idBitrix = $idBitrix;
        $contactBitrix->save();
    }

    function buscaDealBitrix($id){
        return $this->retornaExecucao("crm.deal.get.json?ID=$id");
    }

    function retornoBitrix(){
        if($_POST["auth"]["application_token"] != env("TOKEN_WEBHOOK_BITRIX")){
            exit;
        }

        $id = $_POST["data"]["FIELDS"]["ID"];
        $dealBitrix = json_decode($this->buscaDealBitrix($id));

        if($dealBitrix->result->STAGE_ID != "WON" && $dealBitrix->result->STAGE_ID != "LOSE"){
            exit;
        }

        $contatoBitrix = null;

        if(isset($dealBitrix->result->CONTACT_ID) && $dealBitrix->result->CONTACT_ID != 0)
            $contatoBitrix = json_decode($this->buscaContatoBitrix($dealBitrix->result->CONTACT_ID, ""));
        elseif(isset($dealBitrix->result->COMPANY_ID) && $dealBitrix->result->COMPANY_ID != 0)
            $contatoBitrix = json_decode($this->buscaContatoBitrix($dealBitrix->result->COMPANY_ID, "11111111111111")); //14 dígitos para entender que é CNPJ

        $forms = FormFilling::where('bitrix_id', $id);

        $form = new FormFilling;
        $form->page_title = "Bitrix";
        $form->bitrix_status = "OK";
        $form->origem = "Bitrix";
        $form->bitrix_id = $id;
        $fields = json_decode(json_encode(array('' => '')));

        if($forms->count() > 0){
            $form = $forms->first();
            $fields = json_decode($form->fields);
        }
        try{
            $fields->ganho_ou_perda = $dealBitrix->result->STAGE_ID;
            $fields->nome = $contatoBitrix->result->NAME ?? $contatoBitrix->result->TITLE;
            $fields->profissao = $contatoBitrix->result->POST ?? null;
            $fields->telefone = null;
            $fields->email = null;
            $fields->nome_empresa_contato = null;
            $fields->razao_social = null;
            $fields->cnpj_empresa_contato = null;

            if(isset($contatoBitrix->result->COMPANY_ID) && $contatoBitrix->result->COMPANY_ID != "0"){
                $empresaContato = json_decode($this->buscaContatoBitrix($contatoBitrix->result->COMPANY_ID, "11111111111111")); //14 dígitos para entender que é CNPJ
                $fields->nome_empresa_contato = $empresaContato->result->TITLE;
                $fields->razao_social = $empresaContato->result->UF_CRM_1722371559917;
                $fields->cnpj_empresa_contato = $empresaContato->result->UF_CRM_1722371594474;
            }

            if($contatoBitrix->result->HAS_PHONE = "Y")
                $fields->telefone = $contatoBitrix->result->PHONE[0]->VALUE;

            if($contatoBitrix->result->HAS_EMAIL = "Y")
                $fields->email = $contatoBitrix->result->EMAIL[0]->VALUE;

            if(isset($contatoBitrix->result->BIRTHDATE) && $contatoBitrix->result->BIRTHDATE != ""){
                $date = date_create($contatoBitrix->result->BIRTHDATE);
                $fields->data_nascimento = date_format($date,"d/m/Y");
            }else{
                $fields->data_nascimento = null;
            }
            $fields->CPF = null;
            $fields->CNPJ = null;
            $fields->sexo = null;
            $fields->estado_civil = null;

            if(isset($contatoBitrix->result->UF_CRM_1722370703944)){
                switch($contatoBitrix->result->UF_CRM_1722370703944){
                    case "48":
                        $fields->sexo = "Masculino";
                        break;
                    case "50":
                        $fields->sexo = "Feminino";
                        break;
                    case "52":
                        $fields->sexo = "Não quis declarar";
                        break;
                }
            }

            if(isset($contatoBitrix->result->UF_CRM_1722370970916)){
                switch($contatoBitrix->result->UF_CRM_1722370970916){
                    case "54":
                        $fields->estado_civil = "Solteiro";
                        break;
                    case "56":
                        $fields->estado_civil = "Casado";
                        break;
                    case "58":
                        $fields->estado_civil = "Separado";
                        break;
                    case "60":
                        $fields->estado_civil = "Divorciado";
                        break;
                    case "62":
                        $fields->estado_civil = "Viúvo";
                        break;
                    case "64":
                        $fields->estado_civil = "União Estável";
                        break;
                }
            }

            $escreventeBitrix = json_decode($this->buscaUsuarioBitrix($dealBitrix->result->ASSIGNED_BY_ID));
            $fields->escrevente_nome = $escreventeBitrix->result[0]->NAME;
            $fields->valor = $dealBitrix->result->OPPORTUNITY;

            if(isset($dealBitrix->result->CONTACT_ID) && $dealBitrix->result->CONTACT_ID != 0){
                $fields->CPF = $contatoBitrix->result->UF_CRM_1722370633224;
                $this->salvarContato($fields->CPF, $dealBitrix->result->CONTACT_ID);
            }elseif(isset($dealBitrix->result->COMPANY_ID) && $dealBitrix->result->COMPANY_ID != 0){
                $fields->CNPJ = $contatoBitrix->result->UF_CRM_1722371594474;
                $this->salvarContato($fields->CNPJ, $dealBitrix->result->COMPANY_ID);
            }

            $form->fields = json_encode($fields);
            $form->save();

        }catch(\Exception $e){
            $teste = $e->getMessage();
        }
    }

    function buscaEscreventeBitrix($email){
        $email = urlencode($email);
        return $this->retornaExecucao("user.search.json?EMAIL=$email");
    }

    function buscaOperadorBD($operadorId){
        $buscaEscrevente = Collaborator::whereRaw('JSON_EXTRACT(settings, "$.perfil_bitrix") like \'%operador%\' and id = ?', $operadorId)->get();
        if($buscaEscrevente->count() > 0){
            $escrevente = $buscaEscrevente->first();
            $escreventeBitrix = json_decode($this->buscaEscreventeBitrix($escrevente->email));
            if(count($escreventeBitrix->result) == 0){
                return $this->buscaOperadorAdministradorBD();
            }else{
                return $escreventeBitrix->result[0]->ID;
            }
        }else{
            return $this->buscaOperadorAdministradorBD();
        }
    }

    function buscaOperadorAdministradorBD($tentativas = 0){
        if($tentativas == 10)
            return null;
        $buscaEscreventesAdmin = Collaborator::whereRaw('JSON_EXTRACT(settings, "$.perfil_bitrix") like \'%administrador%\'')->inRandomOrder()->limit(1)->get();
        if($buscaEscreventesAdmin->count() > 0){
            $escrevente = $buscaEscreventesAdmin->first();
            $escreventeBitrix = json_decode($this->buscaEscreventeBitrix($escrevente->email));
            if(count($escreventeBitrix->result) == 0){
                return $this->buscaOperadorAdministradorBD($tentativas + 1);
            }else{
                return $escreventeBitrix->result[0]->ID;
            }
        }else{
            return null;
        }
    }

    function enviarFormParaBitrix($id){
        $form = FormFilling::find($id);
        if($form->bitrix_id != 0)
            return "";

        $json = json_decode($form->fields);
        $cpfCNPJ = $json->CPF ?? $json->CNPJ ?? "";

        $idBitrix = 0;

        if($cpfCNPJ != "")
            $idBitrix = $this->retornaIdContatoBitrix($cpfCNPJ);

        try{
            $existeContatoNoBitrix = true;
            if($idBitrix != 0){
                $buscaContatoBitrix = json_decode($this->buscaContatoBitrix($idBitrix, $cpfCNPJ));
                if(isset($buscaContatoBitrix->error_description))
                    $existeContatoNoBitrix = false;
            }
            if($idBitrix == 0 || !$existeContatoNoBitrix){
                $retorno = json_decode($this->incluiContato($json->nome, $json->email, $json->telefone, $cpfCNPJ));
                if(isset($retorno->error_description)){
                    $form->bitrix_status = $retorno->error_description;
                    $form->save();
                    return;
                }else{
                    $idBitrix = $retorno->result;
                }
            }

            $valor = 0;

            if(isset($json->Valor))
                $valor += $json->Valor;

            if(isset($json->Taxa_de_entrega))
                $valor += $json->Taxa_de_entrega;

            if(isset($json->Opção_digital))
                $valor += $json->Opção_digital;

            $idOperador = null;

            if(!isset($json->Escrevente)){
                $idOperador = $this->buscaOperadorAdministradorBD();
            }else{
                $idOperador = $this->buscaOperadorBD($json->Escrevente);
            }

            if($idBitrix != 0){
                $formController = new FormController;
                $dadosFormulario = $formController->retornaDadosFormulario($json);
                $retornoEnvioFormulario = json_decode($this->incluiDeal($form->page_title . ' - ' . $json->nome, $json->bitrix, $valor, $dadosFormulario, $cpfCNPJ, $idBitrix, $idOperador));
                if(isset($retornoEnvioFormulario->error_description)){
                    $form->bitrix_status = $retornoEnvioFormulario->error_description;
                    $form->save();
                    return;
                }else{
                    $form->bitrix_status = "OK";
                    $form->bitrix_id = $retornoEnvioFormulario->result;
                    $form->save();
                }
            }
        }catch(\Exception $e){
            $form->bitrix_status = $e->getMessage();
            $form->save();
            return;
        }
        if($idBitrix != 0 && $cpfCNPJ != ""){
            $this->salvarContato($cpfCNPJ, $idBitrix);
        }
    }

}
