<?php
namespace App\Services\Financas\Exception;

class BoletoException extends \Exception {
    private $errorsObject;

    public function __construct($message = "", $code = 0, $previous = null, $errorsObject = null)
    {
        parent::__construct($message, $code, $previous);
        $this->errorsObject = $errorsObject;
    }

    public function getErrorsObject()
    {
        return $this->errorsObject;
    }
}
