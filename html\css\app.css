@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800&family=Roboto+Condensed:ital,wght@0,300;0,400;0,700;1,300;1,400;1,700&display=swap');

:root{
	--azul: #00909D;
	--azulEscuro: #095D6E;
	--cinza: #808080;
	--vermelho: #E83946;
}

body{
	margin: 0;
	background-color: #F5F5F5;
	overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6, p, li, a, span, label, input, textarea, select, button{
	font-family: 'Roboto Condensed';
	font-weight: 400;
}

p{
	font-size: 24px;
	line-height: 32px;
}

h1{
	font-size: 32px;
	line-height: 42px;
	color: var(--azul);
}

h2{
	font-size: 24px;
	line-height: 32px;
	color: var(--azul);
}

h3{
	font-size: 20px;
	line-height: 28px;
	color: var(--azul);
}

h4{
	font-size: 18px;
	line-height: 26px;
	color: var(--azul);
}

h5{
	font-size: 16px;
	line-height: 24px;
	color: var(--azul);
}

h6{
	font-size: 14px;
	line-height: 22px;
	color: var(--azul);
}

.mt-0{
	margin-top: 0!important;
}

.mt-1{
	margin-top: 8px!important;
}
.mt-2{
	margin-top: 16px;
}
.mt-3{
	margin-top: 24px;
}


.mb-0{
	margin-bottom: 0!important;
}
.mb-1{
	margin-bottom: 8px!important;
}

.mb-3{
	margin-bottom: 24px!important;
}

.p-0{
	padding: 0 !important;
}
.px-1{
  padding-left: 8px;
  padding-right: 8px;
}

.d-flex{
	display: flex;
}

.align-items-center{
	align-items: center;
}

.align-items-start{
	align-items: flex-start;
}


.justify-content-between{
	justify-content: space-between;
}

.flex-direction-column{
	flex-direction: column;
}

.centerAll *{
	text-align: center;
}

.subTitle{
	font-size: 16px;
	line-height: 22px;
	text-transform: uppercase;
	color: white;
}

.subTitle.blue{
	color: var(--azul);
}

.btnLink{
	font-size: 14px;
	line-height: 14px;
	color: var(--azul);
	font-family: 'Open Sans', sans-serif;
}
.btnAsLink {
  background: transparent;
  border:none;
  color: var(--cinza);cursor:pointer;
  text-decoration: underline;
  font-size:15px;
}



.btnDefault{
  border:none;
	font-size: 16px;
	line-height: 22px;
	padding: 10px 25px;
	border-radius: 5px;
	text-transform: uppercase;
	text-decoration: none !important;
	text-align: center;
	color: white !important;
	background-color: var(--azul);
	display: inline-flex;
	position: relative;
	cursor: pointer;
}

.btnDefault.red{
	background-color: var(--vermelho);
	color: white !important;
}

.red{
	color: var(--vermelho) !important;
}

.blue{
	color: var(--azul) !important;
}

.darkBlue{
	color: var(--azulEscuro) !important;
}

.disclaimer{
	font-size: 12px !important;
	line-height: 22px !important;
}

.container-fluid{
	width: 1130px;
	max-width: 100%;
	padding: 0 10px;
	margin: 0 auto;
	position: relative;
}

.bannerDestaque{
	width: 100%;
	height: 490px;
	background-size: cover;
	background-position: top center;
}

.lingueta{
	height: 100%;
	display: flex;
    align-items: center;
}

.lingueta::after{
	content: url('/img/linguetaBase.svg');
	width: 100px;
	height: 49px;
	position: absolute;
	bottom: -9px;
	left: 10px;
}

.bannerDestaque p{
	font-size: 28px;
	line-height: 32px;
	color: white;
	display: inline-flex;
	margin: 36px 0;
}

.bannerDestaque  .subTitle{
	font-size: 16px;
	line-height: 22px;
	margin: 0;
}

.bannerDestaque ul{
	padding: 0;
	margin: 30px 0;
}

.bannerDestaque li{
	list-style: none;
}

.bannerDestaque li a{
	font-size: 14px;
	line-height: 24px;
	color: var(--cinza);
	text-decoration: none;
	display: flex;
	align-items: center;
	/* Removido por estar ultrapassando o espaço da div */
	/* white-space: nowrap; */
}

.bannerDestaque li a .padlock{
	width: 12px;
	margin-right: 5px;
	filter: grayscale(100%);
}

.bannerDestaque form{
	width: calc(100% - 10px);
	/*max-width: 460px;*/
	height: 40px;
	display: flex;
	align-items: center;
	background-color: #F1F1F1;
	border-radius: 5px;
}

.bannerDestaque form input{
	width: 100%;
	height: 100%;
	background-color: transparent;
	border: none;
}

.bannerDestaque form input::placeholder{
	font-size: 16px;
	line-height: 22px;
	text-transform: uppercase;
}

.bannerDestaque form input[type="submit"]{
	width: 40px;
	background-size: 20px;
	background-position: center;
	background-repeat: no-repeat;
	background-image: url('/img/search.svg');
}

section.atalhosNav{
	padding: 80px 0;
	padding-bottom: 60px;
}

section.atalhosNav .col-xs-12 .row .col-xs-12{
	padding: 0;
}

section.atalhosNav .col-xs-12 .row .col-xs-12:nth-child(odd){
	padding-right: 10px;
}

section.atalhosNav .col-xs-12 .row .col-xs-12:nth-child(even){
	padding-left: 10px;
}

.boxAfterForm{
	padding: 20px !important;
	margin: 20px 0;
}

.boxAfterForm h3{
	margin-top: 0 !important;
}

.blocoAtalhoDefault{
	background: #FFFFFF;
	border-radius: 10px;
	height: auto;
	min-height: 136px;
	padding: 20px;
	margin-bottom: 20px;
	display: flex;
}

.blocoAtalhoDefault.bgGray{
	background-color: #F5F5F5;
}

.blocoAtalhoDefault .iconeAtalho{
	width: 20px;
	margin-right: 20px;
	height: 100%;
	display: inline-table;
  color: #2a5c6e;
  font-size: 35px;
}

.blocoAtalhoDefault .infoAtalho{
	width: 100%;
	flex: 1 0;
}

.blocoAtalhoDefault p{
	font-size: 16px;
	line-height: 22px;
	color: var(--azul);
	margin: 0;
	margin-bottom: 30px;
	min-height: 40px;
}

.blocoAtalhoBg{
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	height: 292px;
	border-radius: 10px;
	position: relative;
	display: flex;
	flex-direction: column;
	justify-content: flex-end;
	align-items: flex-start;
	padding: 30px;
	margin-bottom: 20px;
}

.blocoAtalhoBg:before{
	content: '';
	display: block;
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	border-radius: 10px;
	background: linear-gradient(180deg, #000000 0%, rgba(0, 0, 0, 0.8) 0%, rgba(217, 217, 217, 0) 100%);
	transform: rotate(-180deg);
	z-index: 1;
}

.blocoAtalhoBg *{
	color: white !important;
	z-index: 2;
}

.blocoAtalhoBg p{
	font-size: 16px;
	line-height: 22px;
}

.listAtalhos .col-xs-12:first-child{
	padding-left: 0;
}

.listAtalhos .col-xs-12:last-child{
	padding-right: 0;
}

.escrituraOnline{
	height: 360px;
	display: flex;
	align-items: center;
	background-size: cover;
	background-position: top center;
	position: relative;
}

.escrituraOnline:before{
	content: '';
	display: block;
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	background: linear-gradient(90deg, #000000 0%, rgba(0, 0, 0, 0.8) 0%, rgba(217, 217, 217, 0) 100%);
	z-index: 1;
}

.escrituraOnline .container-fluid{
	flex-direction: column;
	align-items: flex-start;
	justify-content: center;
}

.escrituraOnline p{
	font-size: 24px;
	line-height: 32px;
}

.escrituraOnline *{
	color: white;
	z-index: 2;
}

section.newsletter{
	margin: 80px 0;
}

section.newsletter .container-fluid.true{
	background-color: #F1F1F1;
	padding: 40px;
}

section.newsletter p{
	font-size: 24px;
	line-height: 32px;
	color: var(--azul);
}

section.newsletter form .line{
	display: flex;
	width: 100%;
	height: 48px;
	background-color: white;
}

section.newsletter form .line input{
	width: 100%;
	height: 100%;
	background-color: transparent;
	border: none;
	font-size: 16px;
	line-height: 26px;
	padding-left: 20px;
}

section.newsletter form .line input[type="submit"]{
	width: auto;
	padding: 0 20px;
	color: var(--azul);
	text-transform: uppercase;
}

section.newsletter form .line input::placeholder{
	text-transform: uppercase;
}

.lineLGPD{
	display: flex;
	align-items: flex-start;
	justify-content: flex-start;
	margin-top: 10px;
}

.lineLGPD input[type="checkbox"]{
	appearance: unset;
	width: 14px;
	height: 14px;
	border-radius: 100%;
	background-color: white;
	cursor: pointer;
	padding: 0;
	border-color: white;
}

.lineLGPD input[type="checkbox"]:checked {
	background-color: var(--cinza);
}

.lineLGPD label{
	font-size: 12px;
	line-height: 16px;
	color: var(--cinza);
	padding-left: 5px;
}

section.previewBlog{
	padding-bottom: 40px;
}

section.previewBlog.bgWhite{
	background-color: white;
}

section.previewBlog h2,
section.previewBlog .blocoBlog{
	margin-bottom: 40px;
}

section.previewBlog .row .col-xs-12{
	padding: 0;
}

section.previewBlog .row .col-xs-12:nth-child(odd){
	padding-right: 20px;
}

section.previewBlog .row .col-xs-12:nth-child(even){
	padding-left: 20px;
}

.blocoBlog{
	display: flex;
}

.blocoBlog .imageBlog{
	width: 362px;
	max-width: 100%;
	height: 220px;
	border-radius: 10px;
	margin-right: 10px;
}
.blocoBlog .imageBlog img{
  max-width: 100%;
  max-height: 100%
}

.blocoBlog .infoBlog{
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: flex-start;
}

.blocoBlog p{
	font-size: 24px;
	line-height: 32px;
	color: var(--cinza);
}

.blocoBlog .btnLink::before{
	width: 100%;
	height: 5px;
	content: url('/img/lineBefore.svg');
	margin-bottom: 20px;
}

.blocoBlog .btnLink{
	display: flex;
	flex-direction: column;
	font-size: 16px;
	color: var(--cinza);
}

.contentIncludeMenu{
	background-color: white;
	padding: 80px 0;
}

.contentIncludeMenu.bgGray{
	background-color: #F1F1F1;
	padding-bottom: 0;
}

.contentIncludeMenu.bgGray li{
	background-color: #F1F1F1;
}

.contentIncludeMenu .subMenu{
	visibility: visible;
	background-color: transparent;
	position: relative;
	top: 0;
	z-index: 0;
}

.contentIncludeMenu .subMenu:before,
.contentIncludeMenu .subMenu:after{
	display: none;
}

.contentIncludeMenu .subMenu li{
	list-style: none;
	max-height: 100%;
}

.contentIncludeMenu .subMenu li a{
	text-decoration: none;
}

.contentIncludeMenu .subMenu li ul li:first-child a{
	text-transform: unset;
}

.blocosNumber .blocoNumber{
	padding: 20px;
	background-color: #F1F1F1;
	border-radius: 10px;
}

.blocosNumber .blocoNumber .icone{
	width: 40px;
	height: 40px;
	border-radius: 100%;
	background-color: var(--azul);
	display: flex; justify-content: center; align-items: center;
	font-size: 18px;
	color: white;
}

.blocosNumber .blocoNumber p{
	font-size: 16px;
	line-height: 22px;
	color: var(--cinza);
}

.blocosNumber .col-xs-12:first-child{
	padding-left: 0;
}

.blocosNumber .col-xs-12:last-child{
	padding-right: 0;
}
.blocosNumber .col{
  margin-bottom: 20px;
  min-width: 100%;
}
.blocoNumber{
  display: flex
}
.blocoNumber .icone{
  margin-right: 20px
}
.blocoNumber p{
  margin-top: 0;
  flex: 1
}

section.contentServico{
	padding: 80px 0;
	background-color: white;
}

section.contentServico:not(.singleNovidade) h1{
	margin-bottom: 50px;
}

section.contentServico p,
section.contentServico li,
section.contentServico .news_single_content {
	font-family: 'Open Sans';
	font-weight: 400;
	font-size: 16px;
	line-height: 26px;
	color: #808080;
}

section.contentServico picture img {
  margin-bottom: 20px;
}

section.contentServico .listAtalhos{
	margin: 20px 0;
}

section.contentServico .listAtalhos .col-xs-12:nth-child(odd){
	padding-left: 0px !important;
}

section.contentServico .listAtalhos .col-xs-12:nth-child(even){
	padding-right: 0px !important;
}

.gridImagens{
	margin-bottom: 50px;
}

section.contentServico .blocoAtalhoDefault{
	display: flex;
  align-items: center;
}

section.contentServico .blocoAtalhoDefault .iconeAtalho{
	width: 50%;
	display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  margin-right: 0;
}

section.contentServico .blocoAtalhoDefault .infoAtalho{
	width: 50%;
}

section.contentServico strong,
section.contentServico p a,
section.contentServico .news_single_content a {
	color: var(--azul);
}

section.contentServico h2{
	margin-top: 50px;
}

section.contentServico .container-fluid .row > .col-lg-9{
	padding-right: 100px !important;
}

section.contentServico .d-flex img{
	margin-right: 20px;
}

.boxWidgetSidebar{
	background-color: #F1F1F1;
	border-radius: 5px;
	padding: 20px;
	position: sticky;
    top: 50px;
}

.boxWidgetSidebar h3{
	font-size: 24px;
	line-height: 32px;
	margin-bottom: 10px !important;
}

.boxWidgetSidebar.red{
	background-color: var(--vermelho);
}

.boxWidgetSidebar.red *{
	color: white;
	margin: 0;
}

.sticky{
	position: sticky;
	top: 50px;
}

.sticky .blocoAtalhoBg p{
	font-size: 16px;
	line-height: 22px;
	color: white;
}

.sticky .blocoAtalhoBg a{
	width: 100%;
	justify-content: center;
}

.boxWidgetSidebar a{
	font-size: 16px;
	line-height: 26px;
	color: var(--cinza);
	text-decoration: underline;
}

.boxWidgetSidebar a.current{
	color: var(--vermelho) !important;
	text-decoration: none;
}

.boxWidgetSidebar p.red{
	font-size: 24px;
	line-height: 32px;
	color: var(--vermelho);
	margin-top: 0;
	font-family: 'Roboto Condensed';
}

p.red strong{
	color: var(--vermelho);
}

.boxWidgetSidebar .btnDefault{
	margin-top: 20px;
	width: 100%;
	display: flex;
	justify-content: center;
}

/* Accordion styles */
.tabs {
  border-radius: 8px;
  overflow: hidden;
  margin: 20px 0;
}

.tab {
  width: 100%;
  color: var(--cinza);
  overflow: hidden;
  margin-bottom: 10px;
  font-size: 16px;
  line-height: 26px;
}

.tabs .tab:last-child{
	margin-bottom: 0;
}

.tab-label {
    display: flex;
    justify-content: space-between;
    padding: 15px 20px;
    background: #F1F1F1;
    font-weight: bold;
    cursor: pointer;
}

.tab-label::after {
  	content: "\276F";
  	width: 1em;
  	height: 1em;
  	text-align: center;
  	transition: all .35s;
  	transform: rotate(90deg);
  	margin-top: 5px;
}
  
.tab-content {
    max-height: 0;
    padding: 0 1em;
    color: var(--cinza);
    background: #F1F1F1;
    transition: all .35s;
 }

.tab-close {
    display: flex;
    justify-content: flex-end;
    padding: 1em;
    font-size: 0.75em;
    background: #F1F1F1;
    cursor: pointer;
}

.tabs input {
  position: absolute;
  opacity: 0;
  z-index: -1;
}

input:checked  + .tab-label {
    background: darken(#F1F1F1, 10%);
}

input:checked ~ label::after {
  	transform: rotate(-90deg);
}

input:checked ~ .tab-content {
	max-height: 100vh;
	padding: 20px;
	padding-top: 10px;
	padding-right: 10%;
}

section.pageNovidades{
	padding: 80px 0;
	padding-bottom: 30px;
	background-color: white;
}

section.pageNovidades .listNovidades{
	margin-top: 50px;
}

.listNovidades .blocoBlog{
	margin-bottom: 50px;
}

section.account{
	padding: 80px 0;
	min-height: 600px;
	background-color: white;
}

section.account h1{
	margin-bottom: 32px;
}

section.account p{
	font-size: 16px;
	line-height: 26px;
	color: var(--cinza);
}

.reCaptcha{
	margin-bottom: 20px;
}

.reCaptcha img{
	max-width: 250px;
}

form hr{
	width: 100%;
	margin: 0 auto;
	position: relative;
	height: 1px;
	background-color: black;
	margin-top: 10px;
	margin-bottom: 10px;
	opacity: 0.1;
}

section.account form{
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	justify-content: flex-start;
}

section.account form :is(input, select){
	width: 100%;
	height: auto;
	padding: 10px;
	border: 1px solid var(--cinza);
	background-color: transparent;
	margin-bottom: 20px;
}

section.account form .btnDefault{
	border: none;
}

section.account form .d-flex{
	align-items: center;
}

section.account form .d-flex input:not([type="checkbox"], [type="radio"]){
  margin: 0 10px 0 0;
}

.resendToken{
	margin-left: 50px;
}

.resendToken, .resendToken a{
	font-size: 16px;
	line-height: 26px;
	color: var(--cinza);
}

.contentServicesAccount{
	border-radius: 10px;
	overflow: hidden;
}

.contentServicesAccount .item{
	background: #F1F1F1;
	padding: 20px;
}

.contentServicesAccount .item:not(:last-child){
	margin-bottom: 10px;
}

.contentServicesAccount .item h2{
	font-size: 16px;
	line-height: 22px;
}

.contentServicesAccount .item a{
	font-size: 16px;
    line-height: 26px;
    color: var(--cinza);
}

details > summary {
  list-style: none;
}

details > summary::-webkit-details-marker {
  display: none;
}

.itemDuvida summary{
	font-size: 16px;
	line-height: 26px;
	color: var(--cinza);
	text-decoration: underline;
	cursor: pointer;
}

details[open].itemDuvida summary {
  text-decoration: none;
  color: var(--vermelho);
}

.itemDuvida input{
	width: 100%;
    appearance: unset;
    height: 100%;
    z-index: 2;
}

.itemDuvida .tab{
	position: relative;
}

.itemDuvida .tabs{
	margin: 10px 0;
}

.itemSearch summary{
	font-size: 16px;
	line-height: 26px;
	color: var(--cinza);
}

.itemSearch summary a{
  text-decoration: underline;
  color: var(--cinza);
}

.itemSearch summary label{
  background: var(--azul);
    color: #FFF;
    padding: 3px 8px;
    border-radius: 15px;
    font-size: 13px;
    margin-left: 6px;
}

.pagenav{
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 40px;
	margin-bottom: 50px;
}

.pagenav :is(a, span){
	font-size: 24px;
	line-height: 32px;
	text-decoration: none;
	color: var(--cinza);
	margin: 0 10px;
}

.pagenav .active{
	color: white;
	background-color: var(--azul);
	border-radius: 100%;
	width: 40px;
	height: 40px;
	display: inline-flex;
	align-items: center;
	justify-content: center;
}

.menuConta li{
	list-style: none;
}

.menuConta li a{
	font-size: 16px;
	line-height: 26px;
	color: var(--cinza);
}

.notificacao{
	background-color: #F1F1F1;
	border-radius: 10px;
	padding: 20px;
	position: absolute;
  bottom: 0px;
  margin-top: 32px;
}

.notificacao h3{
	font-size: 24px;
	line-height: 32px;
	color: var(--cinza);
}

.notificacao p{
	margin: 10px 0;
}

.notificacao :is(p, a){
	font-size: 16px;
	line-height: 26px;
}

.formControl{
	margin-bottom: 10px;
	max-width: 556px;
	width: 100%;
}

.formControl input{
	margin-top: 10px;
}

.formControl input[readonly] {
  background-color: #eeeeee;
}

.formControl label{
	font-size: 16px;
	line-height: 26px;
	color: var(--cinza);
}

.formControl .btnDefault{
	border: none;
}

.formControl.check{
	display: flex;
	align-items: flex-start;
	margin: 20px 0;
}

.formControl.check input{
	appearance: unset;
	width: 15px;
	height: 13px;
	border: 1px solid var(--cinza);
	background-color: transparent;
	border-radius: 100%;
	margin: 0;
	margin-right: 10px;
	margin-top: 5px;
	display: inline-flex;
	padding: 0;
}

.formControl.check input:checked{
	background-color: var(--cinza);
}

.formControl.check  a{
	color: var(--cinza);
}

.email-input-info {
	font-size: 12px;
	line-height: 14px;
	color: #a7a7a7;
	margin-top:6px;
	display:block
}

form.includePage{
	margin: 30px 0;
}

form :is(input, select){
	background-color: transparent;
	border: 1px solid var(--cinza);
	padding: 12px;
	width: 100%;
}

.formControl.file{
	margin-bottom: 15px;
}

.formControl.file input{
	padding: 0;
	border: none;
}

[type="file"] {
  color: white;
}

[type="file"]::-webkit-file-upload-button {
  background: var(--cinza);
  border: 2px solid var(--cinza);
  border-radius: 5px;
  color: white;
  cursor: pointer;
  font-size: 16px;
  line-height: 22px;
  outline: none;
  padding: 10px 25px;
  transition: all 1s ease;
}

.downloads{
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	margin: 50px 0;
}

.downloads a{
	margin-bottom: 10px;
	font-size: 16px;
	line-height: 26px;
	color: var(--cinza);
}

.downloads i.download::after{
	content: url('/img/download.svg');
	margin-right: 10px;
}

.searchForm{
	display: flex;
	align-items: center;
	background: #FFFFFF;
	box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
	border-radius: 5px;
	width: 100%;
	max-width: 268px;
  position: relative;
}

.uneedForm, .searchForm  {
  position: relative;
}
.uneedForm .result, .searchForm .result{
  display: none;
  background-color: #FFF;
  position: absolute;
  top: 47px;
  border:1px solid #ccc;
  width: 100%;
  font-size: 13px;
  border-radius: 5px;
  z-index: 10000;
}
.uneedForm .result ul, .searchForm .result ul {
  max-height: 186px;
  overflow-y: auto;
  margin: 0px!important;
  text-indent: 0px;
  padding: 0px;
}
.searchForm .result ul {
  max-height: 300px;
}
.searchForm .result ul li:last-child{
  border-top: 1px solid #f2f2f2;
}
.uneedForm .result ul li, .searchForm .result ul li {
  list-style: none;
}
.uneedForm .result ul li a, .searchForm .result ul li a {
  padding: 8px;
  display: flex;
  text-decoration: none;
  color: #000;
}
.uneedForm .result ul li a:hover, .searchForm .result ul li a:hover {
  background-color: #f2f2f2;
}

.searchForm input{
	border: none;
}

.searchForm input[type="submit"]{
	width: 30px;
	background-image: url('/img/search.svg');
	background-position: center;
	background-size: contain;
	background-repeat: no-repeat;
	font-size: 0;
	margin-right: 10px;
}

.buscaAutoatendimento{
	padding: 80px 0;
	background-color: white;
}

.boxSearchTheme{
	margin: 50px 0;
}

.boxSearchTheme h2{
	font-size: 16px;
	line-height: 22px;
	text-transform: uppercase;
	color: #095D6E;
	margin-bottom: 15px;
}

.boxSearchTheme a{
	font-size: 16px;
	line-height: 26px;
	color: var(--cinza);
}

.boxSearchTheme li{
	list-style: none;
}

.boxBgGray{
	background: #F1F1F1;
	border-radius: 10px;
	padding: 5px 20px;
  font-family: 'Open Sans';
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: #808080;
  padding:20px;
}

.message{
	border-radius: 6px;
	padding: 20px 15px;
	margin: 20px 0;
}

.message p:first-child{
	margin: 0;
	font-size: 16px;
	line-height: 22px;
	font-weight: bold;
}

.message.success{
	background-color: #a2db58;
}

.message.success p:first-child{
	color: #3c763d;
}

.message.success p:not(:first-child){
	margin-bottom: 0;
	margin-top: 5px;
	font-size: 14px;
	line-height: 18px;
	color: #3c763d;;
	font-weight: normal;
}

.message.error{
	background-color: #f2dede;
}

.message.error p:first-child{
	color: #a94442;
}

.message.error p:not(:first-child){
	margin-bottom: 0;
	margin-top: 5px;
	font-size: 14px;
	line-height: 18px;
	color: #a94442;
	font-weight: normal;
}

.typeRadio{
	display: flex;
	align-items: flex-start;
	justify-content: flex-start;
}

.d-flex .typeRadio{
	margin-right: 20px;
	margin-top: 10px;
}

.typeRadio input{
	width: auto !important;
	margin-top: 5px;
	margin-right: 10px;
}

.qrcode{
	width: 100%;
}

.qrcode img{
	max-width: 300px;
}

.resultadoBusca{
	margin: 50px 0;
	border: 1px solid rgba(112, 112, 112, 0.1);
	padding: 20px;
	width: 100%;
}

.pagamento-online .resultadoBusca{
	margin: 25px 0;
}

.resultadoBusca .headBusca{
	padding-bottom: 20px;
	margin-bottom: 20px;
	border-bottom: 1px solid rgba(112, 112, 112, 0.1);
}

.headBusca.invertColor{
	background-color: #00909d;
	margin-left: -20px;
	margin-right: -20px;
	margin-top: -20px;
	padding: 10px 20px !important;
	display: flex;
	align-items: center;
}

.headBusca.invertColor *{
	color: white;
}

.registroEmail{
	flex: 1 0;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: flex-end;
}

.registroEmail img{
	max-width: 220px;
}

.registroEmail a{
	margin-left: 20px;
	padding: 5px 10px;
}

.resultadoBusca table{
	margin: 50px 0;
}

.resultadoBusca.m-hidde table{
	margin: 0;
}

table{
	width: 100%;
}

table .head th{
	font-size: 16px;
	line-height: 26px;
	font-weight: 500;
	color: var(--cinza);
	text-align: left;
	padding: 5px 0;
	border-bottom: 1px solid var(--azul);
}

table .head th:last-child{
	text-align: right;
}

table tr:not(.head) td{
	padding: 5px 0;
	font-size: 14px;
	line-height: 24px;
	color: var(--cinza);
}

table tr td:last-child{
	text-align: right;
}

table span{
	font-size: 12px;
}

tbody{
	width: 100%;
/*  display: flex;*/
/*  flex-direction: column;*/
}

tbody tr{
/*	display: flex;*/
/*  justify-content: space-between;*/
  width: 100%;
}

.btnTable{
	color: var(--azul);
	font-size: 16px;
	position: relative;
}

.btnTable span::before{
	content: "\f0ca";
	margin-right: 5px;
	transition: 1s all;
}

.btnTable.active span::before{
	content: "\f063";
	transition: 1s all;
}

.btnTable.download span::before{
	content: "\f019";
}

.registrosResultados{
	max-height: 0px;
	overflow: hidden;
	transition: 0.3s all;
}

.registrosResultados.active{
	max-height: 100%;
	transition: 1s all;
}

.registrosResultados table{
	margin-top: 20px;
}

.modeloPadrao{
	padding: 100px 0;
	background-color: white;
}

.modeloPadrao a{
	font-size: 18px;
}

.lista{
	margin: 20px 0;
}

.lista li{
	font-size: 18px;
	line-height: 22px;
	margin: 10px 0;
}

.cursor-p {
  cursor: pointer;
}



/* 
 * STARTS MEDIA QUERIES
 */

@media(max-width: 1024px){
	.blocoBlog{
		flex-direction: column;
	}
	.bannerDestaque form{
		margin-bottom: 40px;
	}
	.bannerDestaque{
		height: auto;
		padding: 50px 0;
	}
	.bannerDestaque .lingueta::after{
		bottom: -59px;
	}
	.blocoAtalhoDefault .iconeAtalho{
		margin-right: 10px;
	}
	.blocoAtalhoDefault p{
		margin-bottom: 20px;
	}
	section.newsletter form{
		height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
	}
	.subMenu .menuLink{
		position: relative !important;
    bottom: auto !important;
	}
	.blocosNumber .col-xs-12{
		padding: 10px !important;
		margin: 10px 0;
	}
	section.contentServico .container-fluid .row > .col-lg-9{
		padding-right: 50px !important;
	}
	.boxWidgetSidebar{
		padding: 20px 10px;
	}
	.boxWidgetSidebar .btnDefault{
		padding: 10px;
    font-size: 15px;
	}
	.notificacao {
		position: relative;
		margin-top: 50px;
	}
}

@media(max-width: 768px){
	.gridImagens{
		flex-direction: column;
	}
	.gridImagens img{
		width: auto !important;
		margin-bottom: 20px;
	}
}

@media(max-width: 600px){
	.blocoBlog{
		flex-direction: column;
	}
	section.atalhosNav .col-xs-12 .row .col-xs-12{
		padding: 0 10px;
	}
	.bannerDestaque{
		height: auto;
		padding: 50px 0;
		padding-bottom: 30px;
		background-position: top left;
	}
	.bannerDestaque .col-lg-2{
		margin: 20px 0;
	}
	.bannerDestaque form{
		margin-bottom: 30px;
	}
	.lingueta::after{
		bottom: -39px;
	}
	.bannerDestaque li a{
		color: white;
	}
	.escrituraOnline{
		height: auto;
	}
	.escrituraOnline .lingueta{
		min-height: 360px;
		height: auto;
	}
	.escrituraOnline .lingueta::after{
		bottom: -9px !important;
	}
	section.newsletter{
		padding: 0 10px;
	}
	section.newsletter .container-fluid{
		padding: 20px 0 !important;
		border-radius: 10px;
	}
	section.previewBlog .row .col-xs-12{
		padding: 0 10px !important;
	}
	.listAtalhos .col-xs-12{
		padding: 0 !important;
	}
	.listAtalhos{
		margin-top: 80px;
	}
	.contentIncludeMenu .subMenu .menuLink{
		bottom: 0 !important;
	}
	section.contentServico .container-fluid .row > .col-lg-9{
		padding-right: 10px !important;
	}
	section.contentServico .container-fluid .row > .col-lg-9 ~ .col-lg-3{
		margin-top: 50px;
	}
	.blocosNumber .col-xs-12{
		padding: 0 !important;
		margin: 10px 0;
	}
	.formControl.check{
		display: inline-block;
	}
	.formControl.check input{
		height: 15px;
	}
	.singleNovidade img{
		width: auto !important;
	}
	.contentServicesAccount{
		margin-top: 80px;
	}
	.notificacao{
		margin-top: 50px;
		position: relative;
	}
}

section.content-institucional{
  position: relative;
  margin: 0;
  background-color: white;
}
section.titlePage{
  background-color: white;
  padding-top: 80px;
}
section.content-institucional .content{
  padding: 40px 0;
}
.content-sidebar{
  position: absolute;
  width: 100%;
  height: 100%;
}
.content-sidebar .container-fluid{
  position: relative;
}
.content-sidebar *:not(.boxWidgetSidebar){
  height: 100%;
}
.boxWidgetSidebar{
  position: sticky;
  width: 100%;
  top: 100px;
  left: 0;
  z-index: 2;
  margin-top: 40px;
}
section.content-institucional :is(li, p){
  font-family: 'Open Sans';
    font-weight: 400;
    font-size: 16px;
    line-height: 26px;
    color: #808080;
}

.spinner {
  width: 22px;
  aspect-ratio: 1;
  display:grid;
  -webkit-mask: conic-gradient(from 15deg,#0000,#000);
  mask: conic-gradient(from 15deg,#0000,#000);
  animation: s6 1s infinite steps(12);
}
.spinner,
.spinner:before,
.spinner:after{
  background:
    radial-gradient(closest-side at 50% 12.5%,
     #fff 96%,#0000) 50% 0/20% 80% repeat-y,
    radial-gradient(closest-side at 12.5% 50%,
     #fff 96%,#0000) 0 50%/80% 20% repeat-x;
}
.spinner:before,
.spinner:after {
  content: "";
  grid-area: 1/1;
  transform: rotate(30deg);
}
.spinner:after {
  transform: rotate(60deg);
}

@keyframes s6 {
  100% {transform:rotate(1turn)}
}

.breadcrumb {
  font-size: 16px;
  color: var(--cinza);
  margin-bottom: 8px;
}
.breadcrumb a {
  color: var(--cinza);
}

.ui-widget {
  font-size: 14px!important;
}
li.ui-menu-item {
  list-style: none!important;
}

.validation-error {
  display: flex;
  margin-top: -10px;
  margin-bottom: 20px;
  color: red!important;
}